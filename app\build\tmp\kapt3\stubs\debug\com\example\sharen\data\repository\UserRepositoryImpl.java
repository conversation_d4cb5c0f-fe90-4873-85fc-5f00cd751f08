package com.example.sharen.data.repository;

/**
 * پیاده‌سازی Repository برای مدیریت کاربران
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\u0012\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ2\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\r\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000f\u0010\u0010J*\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u000bJ*\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u000bJ\b\u0010\u0015\u001a\u00020\tH\u0002J\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u0017H\u0016J$\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001b\u0010\u001cJ\u0014\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u0017H\u0016J,\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001f\u0010\u000bJ\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u00172\u0006\u0010!\u001a\u00020\"H\u0016J2\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00190\u00062\u0006\u0010$\u001a\u00020\t2\u0006\u0010%\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b&\u0010\u0010J\"\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b(\u0010\u001cJT\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00190\u00062\u0006\u0010$\u001a\u00020\t2\u0006\u0010%\u001a\u00020\t2\u0006\u0010*\u001a\u00020\t2\u0006\u0010+\u001a\u00020\t2\u0006\u0010!\u001a\u00020\"2\b\u0010,\u001a\u0004\u0018\u00010\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b-\u0010.J2\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u00100\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b1\u0010\u0010J*\u00102\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010$\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b3\u0010\u000bJ\u001c\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u00172\u0006\u00105\u001a\u00020\tH\u0016J*\u00106\u001a\b\u0012\u0004\u0012\u00020\u00190\u00062\u0006\u00107\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b8\u00109J2\u0010:\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010;\u001a\u00020<H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b=\u0010>J,\u0010?\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\u00062\u0006\u0010@\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bA\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006B"}, d2 = {"Lcom/example/sharen/data/repository/UserRepositoryImpl;", "Lcom/example/sharen/domain/repository/UserRepository;", "userDao", "Lcom/example/sharen/data/local/dao/UserDao;", "(Lcom/example/sharen/data/local/dao/UserDao;)V", "approveUser", "Lkotlin/Result;", "", "userId", "", "approveUser-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changePassword", "oldPassword", "newPassword", "changePassword-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteUser", "deleteUser-gIAlu-s", "generateReferrerCode", "generateReferrerCode-gIAlu-s", "generateUniqueReferrerCode", "getAllUsers", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/sharen/domain/model/User;", "getCurrentUser", "getCurrentUser-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPendingUsers", "getUserById", "getUserById-gIAlu-s", "getUsersByRole", "role", "Lcom/example/sharen/domain/model/UserRole;", "login", "email", "password", "login-0E7RQCE", "logout", "logout-IoAF18A", "register", "name", "phone", "referrerCode", "register-bMdYcbs", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/example/sharen/domain/model/UserRole;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "rejectUser", "reason", "rejectUser-0E7RQCE", "resetPassword", "resetPassword-gIAlu-s", "searchUsers", "query", "updateProfile", "user", "updateProfile-gIAlu-s", "(Lcom/example/sharen/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadProfileImage", "imageData", "", "uploadProfileImage-0E7RQCE", "(Ljava/lang/String;[BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateReferrerCode", "code", "validateReferrerCode-gIAlu-s", "app_debug"})
public final class UserRepositoryImpl implements com.example.sharen.domain.repository.UserRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.UserDao userDao = null;
    
    @javax.inject.Inject
    public UserRepositoryImpl(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.UserDao userDao) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.User>> getAllUsers() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.User>> getUsersByRole(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.UserRole role) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.User>> searchUsers(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.User>> getPendingUsers() {
        return null;
    }
    
    private final java.lang.String generateUniqueReferrerCode() {
        return null;
    }
}