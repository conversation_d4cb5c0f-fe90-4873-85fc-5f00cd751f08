<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.customer.CustomerDetailsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@color/primary"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:title="@string/customer_details">

            <ImageView
                android:id="@+id/iv_customer_header"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary"
                android:contentDescription="@string/customer_avatar"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax" />

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/card_customer_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/customer_name"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_customer_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="علی محمدی" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:background="@color/divider" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/phone_number"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_customer_phone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        tools:text="۰۹۱۲۳۴۵۶۷۸۹" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:background="@color/divider" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/address"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_customer_address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        tools:text="تهران، خیابان آزادی، پلاک ۱۲۳" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/card_financial_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/card_customer_info">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/debt_amount"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_debt_amount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/error"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="۱,۵۰۰,۰۰۰ تومان" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:background="@color/divider" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/total_purchases"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_total_purchases"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        tools:text="۱۲,۰۰۰,۰۰۰ تومان" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:background="@color/divider" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/last_purchase"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_last_purchase"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        tools:text="۱۴۰۲/۰۸/۱۲" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"
                        android:background="@color/divider" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/customer_since"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_customer_since"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        tools:text="۱۴۰۱/۰۶/۱۰" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/tv_recent_invoices_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/recent_transactions"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@+id/card_financial_info" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_recent_invoices"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:nestedScrollingEnabled="false"
                app:layout_constraintTop_toBottomOf="@+id/tv_recent_invoices_title"
                tools:itemCount="3"
                tools:listitem="@layout/item_transaction" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="@string/edit_customer"
        android:src="@android:drawable/ic_menu_edit"
        app:layout_anchor="@id/app_bar"
        app:layout_anchorGravity="bottom|end" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_new_invoice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/new_invoice"
        android:src="@android:drawable/ic_input_add" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 