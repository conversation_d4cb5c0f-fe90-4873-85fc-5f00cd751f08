package com.example.sharen.data.local;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&J\b\u0010\r\u001a\u00020\u000eH&J\b\u0010\u000f\u001a\u00020\u0010H&J\b\u0010\u0011\u001a\u00020\u0012H&J\b\u0010\u0013\u001a\u00020\u0014H&\u00a8\u0006\u0015"}, d2 = {"Lcom/example/sharen/data/local/AppDatabase;", "Landroidx/room/RoomDatabase;", "()V", "categoryDao", "Lcom/example/sharen/data/local/dao/CategoryDao;", "customerDao", "Lcom/example/sharen/data/local/dao/CustomerDao;", "installmentDao", "Lcom/example/sharen/data/local/dao/InstallmentDao;", "invoiceDao", "Lcom/example/sharen/data/local/dao/InvoiceDao;", "invoiceItemDao", "Lcom/example/sharen/data/local/dao/InvoiceItemDao;", "paymentDao", "Lcom/example/sharen/data/local/dao/PaymentDao;", "productDao", "Lcom/example/sharen/data/local/dao/ProductDao;", "sellerDao", "Lcom/example/sharen/data/local/dao/SellerDao;", "userDao", "Lcom/example/sharen/data/local/dao/UserDao;", "app_debug"})
@androidx.room.Database(entities = {com.example.sharen.data.local.entity.UserEntity.class, com.example.sharen.data.local.entity.CustomerEntity.class, com.example.sharen.data.local.entity.ProductEntity.class, com.example.sharen.data.local.entity.CategoryEntity.class, com.example.sharen.data.local.entity.InvoiceEntity.class, com.example.sharen.data.local.entity.InvoiceItemEntity.class, com.example.sharen.data.local.entity.PaymentEntity.class, com.example.sharen.data.local.entity.SellerEntity.class, com.example.sharen.data.local.entity.InstallmentEntity.class}, version = 1, exportSchema = false)
@androidx.room.TypeConverters(value = {com.example.sharen.data.db.converter.DateConverter.class, com.example.sharen.data.db.converter.InstallmentStatusConverter.class})
public abstract class AppDatabase extends androidx.room.RoomDatabase {
    
    public AppDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.UserDao userDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.CustomerDao customerDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.ProductDao productDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.CategoryDao categoryDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.InvoiceDao invoiceDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.InvoiceItemDao invoiceItemDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.PaymentDao paymentDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.SellerDao sellerDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.InstallmentDao installmentDao();
}