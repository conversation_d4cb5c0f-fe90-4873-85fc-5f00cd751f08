package com.example.sharen.data.repository

import com.example.sharen.data.local.entity.UserEntity
import kotlinx.coroutines.flow.Flow

interface UserRepository {
    fun getAllUsers(): Flow<List<UserEntity>>
    fun getUnapprovedUsers(): Flow<List<UserEntity>>
    suspend fun createUser(name: String, email: String, phone: String, role: String): Result<UserEntity>
    suspend fun updateUser(user: UserEntity): Result<Unit>
    suspend fun deleteUser(userId: Long): Result<Unit>
    suspend fun approveUser(userId: Long): Result<Unit>
    suspend fun getUserById(userId: Long): Result<UserEntity>
} 