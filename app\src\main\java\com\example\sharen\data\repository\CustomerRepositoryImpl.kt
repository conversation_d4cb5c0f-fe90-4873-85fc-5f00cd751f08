package com.example.sharen.data.repository

import com.example.sharen.data.model.Customer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CustomerRepositoryImpl @Inject constructor() : CustomerRepository {

    // For now, we'll use an in-memory list as our "database"
    private val customers = MutableStateFlow<List<Customer>>(generateMockCustomers())
    
    override suspend fun getCustomers(): Flow<List<Customer>> {
        // Simulate network delay
        delay(500)
        return customers.asStateFlow()
            .flowOn(Dispatchers.IO)
    }

    override suspend fun searchCustomers(query: String): Flow<List<Customer>> {
        // Simulate network delay
        delay(300)
        return customers.asStateFlow()
            .map { customerList ->
                customerList.filter { 
                    it.name.contains(query, ignoreCase = true) || 
                    it.phone.contains(query, ignoreCase = true) || 
                    (it.address?.contains(query, ignoreCase = true) ?: false)
                }
            }
            .flowOn(Dispatchers.IO)
    }

    override suspend fun getCustomerById(customerId: String): Flow<Customer?> = flow {
        // Simulate network delay
        delay(300)
        val customer = customers.value.find { it.id == customerId }
        emit(customer)
    }.flowOn(Dispatchers.IO)

    override suspend fun createCustomer(customer: Customer): Result<Customer> = withContext(Dispatchers.IO) {
        try {
            // Create a copy with a new ID and current timestamps
            val newCustomer = customer.copy(
                id = UUID.randomUUID().toString(),
                createdAt = Date(),
                updatedAt = Date()
            )
            
            // Add to our list
            val currentList = customers.value.toMutableList()
            currentList.add(newCustomer)
            customers.value = currentList
            
            // Simulate network delay
            delay(800)
            
            Result.success(newCustomer)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCustomer(customer: Customer): Result<Customer> = withContext(Dispatchers.IO) {
        try {
            // Verify the customer exists
            val exists = customers.value.any { it.id == customer.id }
            if (!exists) {
                return@withContext Result.failure(IllegalArgumentException("Customer not found"))
            }
            
            // Update with current timestamp
            val updatedCustomer = customer.copy(updatedAt = Date())
            
            // Update in our list
            val currentList = customers.value.toMutableList()
            val index = currentList.indexOfFirst { it.id == customer.id }
            currentList[index] = updatedCustomer
            customers.value = currentList
            
            // Simulate network delay
            delay(800)
            
            Result.success(updatedCustomer)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteCustomer(customerId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            // Verify the customer exists
            val exists = customers.value.any { it.id == customerId }
            if (!exists) {
                return@withContext Result.failure(IllegalArgumentException("Customer not found"))
            }
            
            // Remove from our list
            val currentList = customers.value.toMutableList()
            currentList.removeIf { it.id == customerId }
            customers.value = currentList
            
            // Simulate network delay
            delay(600)
            
            Result.success(true)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getCustomerCount(): Flow<Int> = flow {
        // Simulate network delay
        delay(200)
        emit(customers.value.size)
    }.flowOn(Dispatchers.IO)

    // Mock data for testing
    private fun generateMockCustomers(): List<Customer> {
        return listOf(
            Customer(
                id = "cust-001",
                name = "علی محمدی",
                phone = "09123456789",
                address = "تهران، خیابان آزادی، پلاک 123",
                notes = "مشتری ویژه",
                debtAmount = 1500000,
                totalPurchases = 12000000,
                lastPurchaseDate = Date(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
                createdAt = Date(System.currentTimeMillis() - 180 * 24 * 60 * 60 * 1000), // 180 days ago
                updatedAt = Date(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000)
            ),
            Customer(
                id = "cust-002",
                name = "زهرا احمدی",
                phone = "09987654321",
                address = "اصفهان، خیابان چهارباغ، کوچه گلها، پلاک 45",
                debtAmount = 0,
                totalPurchases = 8500000,
                lastPurchaseDate = Date(System.currentTimeMillis() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
                createdAt = Date(System.currentTimeMillis() - 150 * 24 * 60 * 60 * 1000), // 150 days ago
                updatedAt = Date(System.currentTimeMillis() - 10 * 24 * 60 * 60 * 1000)
            ),
            Customer(
                id = "cust-003",
                name = "حسین رضایی",
                phone = "09123789456",
                address = "مشهد، بلوار وکیل آباد، پلاک 78",
                notes = "پرداخت نقدی",
                debtAmount = 3200000,
                totalPurchases = 5200000,
                lastPurchaseDate = Date(System.currentTimeMillis() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
                createdAt = Date(System.currentTimeMillis() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
                updatedAt = Date(System.currentTimeMillis() - 5 * 24 * 60 * 60 * 1000)
            ),
            Customer(
                id = "cust-004",
                name = "فاطمه کریمی",
                phone = "09361234567",
                address = "شیراز، خیابان ملاصدرا، کوچه 12، پلاک 34",
                debtAmount = 500000,
                totalPurchases = 9700000,
                lastPurchaseDate = Date(System.currentTimeMillis() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
                createdAt = Date(System.currentTimeMillis() - 210 * 24 * 60 * 60 * 1000), // 210 days ago
                updatedAt = Date(System.currentTimeMillis() - 15 * 24 * 60 * 60 * 1000)
            ),
            Customer(
                id = "cust-005",
                name = "محمد قاسمی",
                phone = "09129876543",
                address = "تبریز، ولیعصر، پلاک 56",
                notes = "خرید عمده",
                debtAmount = 0,
                totalPurchases = 16700000,
                lastPurchaseDate = Date(System.currentTimeMillis() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
                createdAt = Date(System.currentTimeMillis() - 110 * 24 * 60 * 60 * 1000), // 110 days ago
                updatedAt = Date(System.currentTimeMillis() - 20 * 24 * 60 * 60 * 1000)
            )
        )
    }
} 