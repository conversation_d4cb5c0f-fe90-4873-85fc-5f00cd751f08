<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_add_payment" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_add_payment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_add_payment_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="11" startOffset="8" endLine="18" endOffset="47"/></Target><Target id="@+id/amountLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="33" startOffset="12" endLine="45" endOffset="67"/></Target><Target id="@+id/amountEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="39" startOffset="16" endLine="43" endOffset="55"/></Target><Target id="@+id/paymentMethodLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="47" startOffset="12" endLine="60" endOffset="67"/></Target><Target id="@+id/paymentMethodSpinner" view="AutoCompleteTextView"><Expressions/><location startLine="54" startOffset="16" endLine="58" endOffset="46"/></Target><Target id="@+id/referenceNumberLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="62" startOffset="12" endLine="75" endOffset="67"/></Target><Target id="@+id/referenceNumberEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="16" endLine="73" endOffset="46"/></Target><Target id="@+id/notesLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="77" startOffset="12" endLine="91" endOffset="67"/></Target><Target id="@+id/notesEditText" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="84" startOffset="16" endLine="89" endOffset="42"/></Target><Target id="@+id/saveButton" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="93" startOffset="12" endLine="98" endOffset="45"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="100" startOffset="12" endLine="106" endOffset="43"/></Target></Targets></Layout>