package com.example.sharen.ui.payment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.SavedStateHandle;
import androidx.navigation.NavArgs;
import java.lang.IllegalArgumentException;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class PaymentAddEditFragmentArgs implements NavArgs {
  private final HashMap arguments = new HashMap();

  private PaymentAddEditFragmentArgs() {
  }

  @SuppressWarnings("unchecked")
  private PaymentAddEditFragmentArgs(HashMap argumentsMap) {
    this.arguments.putAll(argumentsMap);
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static PaymentAddEditFragmentArgs fromBundle(@NonNull Bundle bundle) {
    PaymentAddEditFragmentArgs __result = new PaymentAddEditFragmentArgs();
    bundle.setClassLoader(PaymentAddEditFragmentArgs.class.getClassLoader());
    if (bundle.containsKey("paymentId")) {
      String paymentId;
      paymentId = bundle.getString("paymentId");
      __result.arguments.put("paymentId", paymentId);
    } else {
      __result.arguments.put("paymentId", null);
    }
    if (bundle.containsKey("customerId")) {
      String customerId;
      customerId = bundle.getString("customerId");
      if (customerId == null) {
        throw new IllegalArgumentException("Argument \"customerId\" is marked as non-null but was passed a null value.");
      }
      __result.arguments.put("customerId", customerId);
    } else {
      throw new IllegalArgumentException("Required argument \"customerId\" is missing and does not have an android:defaultValue");
    }
    if (bundle.containsKey("orderId")) {
      String orderId;
      orderId = bundle.getString("orderId");
      if (orderId == null) {
        throw new IllegalArgumentException("Argument \"orderId\" is marked as non-null but was passed a null value.");
      }
      __result.arguments.put("orderId", orderId);
    } else {
      throw new IllegalArgumentException("Required argument \"orderId\" is missing and does not have an android:defaultValue");
    }
    return __result;
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static PaymentAddEditFragmentArgs fromSavedStateHandle(
      @NonNull SavedStateHandle savedStateHandle) {
    PaymentAddEditFragmentArgs __result = new PaymentAddEditFragmentArgs();
    if (savedStateHandle.contains("paymentId")) {
      String paymentId;
      paymentId = savedStateHandle.get("paymentId");
      __result.arguments.put("paymentId", paymentId);
    } else {
      __result.arguments.put("paymentId", null);
    }
    if (savedStateHandle.contains("customerId")) {
      String customerId;
      customerId = savedStateHandle.get("customerId");
      if (customerId == null) {
        throw new IllegalArgumentException("Argument \"customerId\" is marked as non-null but was passed a null value.");
      }
      __result.arguments.put("customerId", customerId);
    } else {
      throw new IllegalArgumentException("Required argument \"customerId\" is missing and does not have an android:defaultValue");
    }
    if (savedStateHandle.contains("orderId")) {
      String orderId;
      orderId = savedStateHandle.get("orderId");
      if (orderId == null) {
        throw new IllegalArgumentException("Argument \"orderId\" is marked as non-null but was passed a null value.");
      }
      __result.arguments.put("orderId", orderId);
    } else {
      throw new IllegalArgumentException("Required argument \"orderId\" is missing and does not have an android:defaultValue");
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  @Nullable
  public String getPaymentId() {
    return (String) arguments.get("paymentId");
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public String getCustomerId() {
    return (String) arguments.get("customerId");
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public String getOrderId() {
    return (String) arguments.get("orderId");
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public Bundle toBundle() {
    Bundle __result = new Bundle();
    if (arguments.containsKey("paymentId")) {
      String paymentId = (String) arguments.get("paymentId");
      __result.putString("paymentId", paymentId);
    } else {
      __result.putString("paymentId", null);
    }
    if (arguments.containsKey("customerId")) {
      String customerId = (String) arguments.get("customerId");
      __result.putString("customerId", customerId);
    }
    if (arguments.containsKey("orderId")) {
      String orderId = (String) arguments.get("orderId");
      __result.putString("orderId", orderId);
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public SavedStateHandle toSavedStateHandle() {
    SavedStateHandle __result = new SavedStateHandle();
    if (arguments.containsKey("paymentId")) {
      String paymentId = (String) arguments.get("paymentId");
      __result.set("paymentId", paymentId);
    } else {
      __result.set("paymentId", null);
    }
    if (arguments.containsKey("customerId")) {
      String customerId = (String) arguments.get("customerId");
      __result.set("customerId", customerId);
    }
    if (arguments.containsKey("orderId")) {
      String orderId = (String) arguments.get("orderId");
      __result.set("orderId", orderId);
    }
    return __result;
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
        return true;
    }
    if (object == null || getClass() != object.getClass()) {
        return false;
    }
    PaymentAddEditFragmentArgs that = (PaymentAddEditFragmentArgs) object;
    if (arguments.containsKey("paymentId") != that.arguments.containsKey("paymentId")) {
      return false;
    }
    if (getPaymentId() != null ? !getPaymentId().equals(that.getPaymentId()) : that.getPaymentId() != null) {
      return false;
    }
    if (arguments.containsKey("customerId") != that.arguments.containsKey("customerId")) {
      return false;
    }
    if (getCustomerId() != null ? !getCustomerId().equals(that.getCustomerId()) : that.getCustomerId() != null) {
      return false;
    }
    if (arguments.containsKey("orderId") != that.arguments.containsKey("orderId")) {
      return false;
    }
    if (getOrderId() != null ? !getOrderId().equals(that.getOrderId()) : that.getOrderId() != null) {
      return false;
    }
    return true;
  }

  @Override
  public int hashCode() {
    int result = 1;
    result = 31 * result + (getPaymentId() != null ? getPaymentId().hashCode() : 0);
    result = 31 * result + (getCustomerId() != null ? getCustomerId().hashCode() : 0);
    result = 31 * result + (getOrderId() != null ? getOrderId().hashCode() : 0);
    return result;
  }

  @Override
  public String toString() {
    return "PaymentAddEditFragmentArgs{"
        + "paymentId=" + getPaymentId()
        + ", customerId=" + getCustomerId()
        + ", orderId=" + getOrderId()
        + "}";
  }

  public static final class Builder {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    public Builder(@NonNull PaymentAddEditFragmentArgs original) {
      this.arguments.putAll(original.arguments);
    }

    @SuppressWarnings("unchecked")
    public Builder(@NonNull String customerId, @NonNull String orderId) {
      if (customerId == null) {
        throw new IllegalArgumentException("Argument \"customerId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("customerId", customerId);
      if (orderId == null) {
        throw new IllegalArgumentException("Argument \"orderId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("orderId", orderId);
    }

    @NonNull
    public PaymentAddEditFragmentArgs build() {
      PaymentAddEditFragmentArgs result = new PaymentAddEditFragmentArgs(arguments);
      return result;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public Builder setPaymentId(@Nullable String paymentId) {
      this.arguments.put("paymentId", paymentId);
      return this;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public Builder setCustomerId(@NonNull String customerId) {
      if (customerId == null) {
        throw new IllegalArgumentException("Argument \"customerId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("customerId", customerId);
      return this;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public Builder setOrderId(@NonNull String orderId) {
      if (orderId == null) {
        throw new IllegalArgumentException("Argument \"orderId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("orderId", orderId);
      return this;
    }

    @SuppressWarnings({"unchecked","GetterOnBuilder"})
    @Nullable
    public String getPaymentId() {
      return (String) arguments.get("paymentId");
    }

    @SuppressWarnings({"unchecked","GetterOnBuilder"})
    @NonNull
    public String getCustomerId() {
      return (String) arguments.get("customerId");
    }

    @SuppressWarnings({"unchecked","GetterOnBuilder"})
    @NonNull
    public String getOrderId() {
      return (String) arguments.get("orderId");
    }
  }
}
