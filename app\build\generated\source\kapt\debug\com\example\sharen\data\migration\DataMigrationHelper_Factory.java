package com.example.sharen.data.migration;

import com.example.sharen.data.local.SharenDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataMigrationHelper_Factory implements Factory<DataMigrationHelper> {
  private final Provider<SharenDatabase> databaseProvider;

  public DataMigrationHelper_Factory(Provider<SharenDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public DataMigrationHelper get() {
    return newInstance(databaseProvider.get());
  }

  public static DataMigrationHelper_Factory create(Provider<SharenDatabase> databaseProvider) {
    return new DataMigrationHelper_Factory(databaseProvider);
  }

  public static DataMigrationHelper newInstance(SharenDatabase database) {
    return new DataMigrationHelper(database);
  }
}
