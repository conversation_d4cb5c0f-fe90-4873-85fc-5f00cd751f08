<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.product.ProductDetailsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            app:contentScrim="?attr/colorPrimary"
            app:expandedTitleMarginEnd="64dp"
            app:expandedTitleMarginStart="48dp"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:title="@string/product_details">

            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/product_image"
                android:scaleType="centerCrop"
                app:layout_collapseMode="parallax"
                app:srcCompat="@android:drawable/ic_menu_gallery" />

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Basic Info Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/product_name"
                        android:textSize="12sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/tvProductName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@android:color/black"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        tools:text="نام محصول" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:background="@android:color/darker_gray" />

                </LinearLayout>

                <!-- Code & Barcode Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/product_code"
                            android:textSize="12sp"
                            android:textColor="@android:color/darker_gray" />

                        <TextView
                            android:id="@+id/tvProductCode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            tools:text="SH-001" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/barcode"
                            android:textSize="12sp"
                            android:textColor="@android:color/darker_gray" />

                        <TextView
                            android:id="@+id/tvBarcode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            tools:text="12345678" />

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@android:color/darker_gray" />

                <!-- Category Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/category"
                        android:textSize="12sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/tvCategory"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        tools:text="پوشاک مردانه" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp"
                        android:background="@android:color/darker_gray" />

                </LinearLayout>

                <!-- Price Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/purchase_price"
                            android:textSize="12sp"
                            android:textColor="@android:color/darker_gray" />

                        <TextView
                            android:id="@+id/tvPurchasePrice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            tools:text="150,000 تومان" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/selling_price"
                            android:textSize="12sp"
                            android:textColor="@android:color/darker_gray" />

                        <TextView
                            android:id="@+id/tvSellingPrice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@android:color/holo_green_dark"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            tools:text="250,000 تومان" />

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@android:color/darker_gray" />

                <!-- Stock Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/stock"
                            android:textSize="12sp"
                            android:textColor="@android:color/darker_gray" />

                        <TextView
                            android:id="@+id/tvStock"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            tools:text="25 عدد" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/minimum_stock"
                            android:textSize="12sp"
                            android:textColor="@android:color/darker_gray" />

                        <TextView
                            android:id="@+id/tvMinimumStock"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@android:color/black"
                            android:textSize="16sp"
                            tools:text="5 عدد" />

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@android:color/darker_gray" />

                <!-- Description Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/description"
                        android:textSize="12sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/tvDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        tools:text="پیراهن مردانه ساده با کیفیت عالی" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabEdit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/edit_product"
        app:srcCompat="@android:drawable/ic_menu_edit" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 