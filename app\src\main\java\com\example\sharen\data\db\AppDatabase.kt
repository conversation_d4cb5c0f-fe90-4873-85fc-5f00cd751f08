package com.example.sharen.data.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.sharen.data.model.Installment

@Database(
    entities = [Installment::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(DateConverter::class, InstallmentStatusConverter::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun installmentDao(): InstallmentDao
} 