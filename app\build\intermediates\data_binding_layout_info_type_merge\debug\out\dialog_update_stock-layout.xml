<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_update_stock" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\dialog_update_stock.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_update_stock_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="30" endOffset="14"/></Target><Target id="@+id/tvCurrentStock" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="43"/></Target><Target id="@+id/etNewStock" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="22" startOffset="8" endLine="26" endOffset="40"/></Target></Targets></Layout>