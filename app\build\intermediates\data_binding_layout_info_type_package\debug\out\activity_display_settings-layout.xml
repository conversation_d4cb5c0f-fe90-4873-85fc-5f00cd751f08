<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_display_settings" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_display_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_display_settings_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="134" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="74"/></Target><Target id="@+id/themeModeGroup" view="RadioGroup"><Expressions/><location startLine="48" startOffset="20" endLine="72" endOffset="32"/></Target><Target id="@+id/radioLight" view="RadioButton"><Expressions/><location startLine="54" startOffset="24" endLine="58" endOffset="64"/></Target><Target id="@+id/radioDark" view="RadioButton"><Expressions/><location startLine="60" startOffset="24" endLine="64" endOffset="63"/></Target><Target id="@+id/radioSystem" view="RadioButton"><Expressions/><location startLine="66" startOffset="24" endLine="70" endOffset="65"/></Target><Target id="@+id/fontSizeSeekBar" view="SeekBar"><Expressions/><location startLine="95" startOffset="20" endLine="101" endOffset="46"/></Target></Targets></Layout>