package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.data.model.PaymentType
import java.util.Date

@Entity(
    tableName = "invoices",
    foreignKeys = [
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = SellerEntity::class,
            parentColumns = ["id"],
            childColumns = ["sellerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["approvedBy"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index("customerId"),
        Index("sellerId"),
        Index("approvedBy")
    ]
)
data class InvoiceEntity(
    @PrimaryKey
    val id: String,
    val invoiceNumber: String,
    val customerId: String,
    val customerName: String,
    val sellerId: String?,
    val sellerName: String?,
    val totalAmount: Long,
    val discount: Long,
    val tax: Long,
    val finalAmount: Long,
    val paidAmount: Long,
    val remainingAmount: Long,
    val status: String,
    val paymentType: String,
    val dueDate: Long?,
    val notes: String?,
    val isDeleted: Boolean,
    val createdAt: Long,
    val updatedAt: Long,
    val approvedAt: Long?,
    val approvedBy: String?
) {
    fun toInvoice(): Invoice = Invoice(
        id = id,
        invoiceNumber = invoiceNumber,
        customerId = customerId,
        customerName = customerName,
        sellerId = sellerId,
        sellerName = sellerName,
        totalAmount = totalAmount,
        discount = discount,
        tax = tax,
        finalAmount = finalAmount,
        paidAmount = paidAmount,
        remainingAmount = remainingAmount,
        status = InvoiceStatus.valueOf(status),
        paymentType = PaymentType.valueOf(paymentType),
        dueDate = dueDate?.let { Date(it) },
        notes = notes,
        isDeleted = isDeleted,
        createdAt = Date(createdAt),
        updatedAt = Date(updatedAt),
        approvedAt = approvedAt?.let { Date(it) },
        approvedBy = approvedBy
    )
    
    companion object {
        fun fromInvoice(invoice: Invoice): InvoiceEntity = InvoiceEntity(
            id = invoice.id,
            invoiceNumber = invoice.invoiceNumber,
            customerId = invoice.customerId,
            customerName = invoice.customerName,
            sellerId = invoice.sellerId,
            sellerName = invoice.sellerName,
            totalAmount = invoice.totalAmount,
            discount = invoice.discount,
            tax = invoice.tax,
            finalAmount = invoice.finalAmount,
            paidAmount = invoice.paidAmount,
            remainingAmount = invoice.remainingAmount,
            status = invoice.status.name,
            paymentType = invoice.paymentType.name,
            dueDate = invoice.dueDate?.time,
            notes = invoice.notes,
            isDeleted = invoice.isDeleted,
            createdAt = invoice.createdAt.time,
            updatedAt = invoice.updatedAt.time,
            approvedAt = invoice.approvedAt?.time,
            approvedBy = invoice.approvedBy
        )
    }
} 