// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProductTestBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton btnCreateProduct;

  @NonNull
  public final MaterialButton btnInfo;

  @NonNull
  public final MaterialButton btnShowProducts;

  @NonNull
  public final ImageView imageView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvTitle;

  private ActivityProductTestBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull MaterialButton btnCreateProduct,
      @NonNull MaterialButton btnInfo, @NonNull MaterialButton btnShowProducts,
      @NonNull ImageView imageView, @NonNull Toolbar toolbar, @NonNull TextView tvDescription,
      @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.btnCreateProduct = btnCreateProduct;
    this.btnInfo = btnInfo;
    this.btnShowProducts = btnShowProducts;
    this.imageView = imageView;
    this.toolbar = toolbar;
    this.tvDescription = tvDescription;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProductTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProductTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_product_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProductTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btnCreateProduct;
      MaterialButton btnCreateProduct = ViewBindings.findChildViewById(rootView, id);
      if (btnCreateProduct == null) {
        break missingId;
      }

      id = R.id.btnInfo;
      MaterialButton btnInfo = ViewBindings.findChildViewById(rootView, id);
      if (btnInfo == null) {
        break missingId;
      }

      id = R.id.btnShowProducts;
      MaterialButton btnShowProducts = ViewBindings.findChildViewById(rootView, id);
      if (btnShowProducts == null) {
        break missingId;
      }

      id = R.id.imageView;
      ImageView imageView = ViewBindings.findChildViewById(rootView, id);
      if (imageView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tvTitle;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new ActivityProductTestBinding((ConstraintLayout) rootView, appBarLayout,
          btnCreateProduct, btnInfo, btnShowProducts, imageView, toolbar, tvDescription, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
