package com.example.sharen.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharen.data.local.entity.InvoiceEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class InvoiceDao_SharenDatabase_Impl implements InvoiceDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<InvoiceEntity> __insertionAdapterOfInvoiceEntity;

  private final EntityDeletionOrUpdateAdapter<InvoiceEntity> __deletionAdapterOfInvoiceEntity;

  private final EntityDeletionOrUpdateAdapter<InvoiceEntity> __updateAdapterOfInvoiceEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllInvoices;

  public InvoiceDao_SharenDatabase_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfInvoiceEntity = new EntityInsertionAdapter<InvoiceEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `invoices` (`id`,`invoiceNumber`,`customerId`,`customerName`,`sellerId`,`sellerName`,`totalAmount`,`discount`,`tax`,`finalAmount`,`paidAmount`,`remainingAmount`,`status`,`paymentType`,`dueDate`,`notes`,`isDeleted`,`createdAt`,`updatedAt`,`approvedAt`,`approvedBy`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final InvoiceEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getInvoiceNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getInvoiceNumber());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCustomerId());
        }
        if (entity.getCustomerName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCustomerName());
        }
        if (entity.getSellerId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSellerId());
        }
        if (entity.getSellerName() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getSellerName());
        }
        statement.bindLong(7, entity.getTotalAmount());
        statement.bindLong(8, entity.getDiscount());
        statement.bindLong(9, entity.getTax());
        statement.bindLong(10, entity.getFinalAmount());
        statement.bindLong(11, entity.getPaidAmount());
        statement.bindLong(12, entity.getRemainingAmount());
        if (entity.getStatus() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getStatus());
        }
        if (entity.getPaymentType() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getPaymentType());
        }
        if (entity.getDueDate() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getDueDate());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getNotes());
        }
        final int _tmp = entity.isDeleted() ? 1 : 0;
        statement.bindLong(17, _tmp);
        statement.bindLong(18, entity.getCreatedAt());
        statement.bindLong(19, entity.getUpdatedAt());
        if (entity.getApprovedAt() == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, entity.getApprovedAt());
        }
        if (entity.getApprovedBy() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getApprovedBy());
        }
      }
    };
    this.__deletionAdapterOfInvoiceEntity = new EntityDeletionOrUpdateAdapter<InvoiceEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `invoices` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final InvoiceEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfInvoiceEntity = new EntityDeletionOrUpdateAdapter<InvoiceEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `invoices` SET `id` = ?,`invoiceNumber` = ?,`customerId` = ?,`customerName` = ?,`sellerId` = ?,`sellerName` = ?,`totalAmount` = ?,`discount` = ?,`tax` = ?,`finalAmount` = ?,`paidAmount` = ?,`remainingAmount` = ?,`status` = ?,`paymentType` = ?,`dueDate` = ?,`notes` = ?,`isDeleted` = ?,`createdAt` = ?,`updatedAt` = ?,`approvedAt` = ?,`approvedBy` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final InvoiceEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getInvoiceNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getInvoiceNumber());
        }
        if (entity.getCustomerId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getCustomerId());
        }
        if (entity.getCustomerName() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCustomerName());
        }
        if (entity.getSellerId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSellerId());
        }
        if (entity.getSellerName() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getSellerName());
        }
        statement.bindLong(7, entity.getTotalAmount());
        statement.bindLong(8, entity.getDiscount());
        statement.bindLong(9, entity.getTax());
        statement.bindLong(10, entity.getFinalAmount());
        statement.bindLong(11, entity.getPaidAmount());
        statement.bindLong(12, entity.getRemainingAmount());
        if (entity.getStatus() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getStatus());
        }
        if (entity.getPaymentType() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getPaymentType());
        }
        if (entity.getDueDate() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getDueDate());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getNotes());
        }
        final int _tmp = entity.isDeleted() ? 1 : 0;
        statement.bindLong(17, _tmp);
        statement.bindLong(18, entity.getCreatedAt());
        statement.bindLong(19, entity.getUpdatedAt());
        if (entity.getApprovedAt() == null) {
          statement.bindNull(20);
        } else {
          statement.bindLong(20, entity.getApprovedAt());
        }
        if (entity.getApprovedBy() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getApprovedBy());
        }
        if (entity.getId() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteAllInvoices = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM invoices";
        return _query;
      }
    };
  }

  @Override
  public Object insertInvoice(final InvoiceEntity invoice,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfInvoiceEntity.insert(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertInvoices(final List<InvoiceEntity> invoices,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfInvoiceEntity.insert(invoices);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteInvoice(final InvoiceEntity invoice,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfInvoiceEntity.handle(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateInvoice(final InvoiceEntity invoice,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfInvoiceEntity.handle(invoice);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllInvoices(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllInvoices.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllInvoices.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<InvoiceEntity>> getAllInvoices() {
    final String _sql = "SELECT * FROM invoices";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<InvoiceEntity>>() {
      @Override
      @NonNull
      public List<InvoiceEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfSellerName = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerName");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfTax = CursorUtil.getColumnIndexOrThrow(_cursor, "tax");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfIsDeleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isDeleted");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final List<InvoiceEntity> _result = new ArrayList<InvoiceEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final InvoiceEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpSellerName;
            if (_cursor.isNull(_cursorIndexOfSellerName)) {
              _tmpSellerName = null;
            } else {
              _tmpSellerName = _cursor.getString(_cursorIndexOfSellerName);
            }
            final long _tmpTotalAmount;
            _tmpTotalAmount = _cursor.getLong(_cursorIndexOfTotalAmount);
            final long _tmpDiscount;
            _tmpDiscount = _cursor.getLong(_cursorIndexOfDiscount);
            final long _tmpTax;
            _tmpTax = _cursor.getLong(_cursorIndexOfTax);
            final long _tmpFinalAmount;
            _tmpFinalAmount = _cursor.getLong(_cursorIndexOfFinalAmount);
            final long _tmpPaidAmount;
            _tmpPaidAmount = _cursor.getLong(_cursorIndexOfPaidAmount);
            final long _tmpRemainingAmount;
            _tmpRemainingAmount = _cursor.getLong(_cursorIndexOfRemainingAmount);
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpIsDeleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDeleted);
            _tmpIsDeleted = _tmp != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            _item = new InvoiceEntity(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpCustomerName,_tmpSellerId,_tmpSellerName,_tmpTotalAmount,_tmpDiscount,_tmpTax,_tmpFinalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpDueDate,_tmpNotes,_tmpIsDeleted,_tmpCreatedAt,_tmpUpdatedAt,_tmpApprovedAt,_tmpApprovedBy);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getInvoiceById(final String id,
      final Continuation<? super InvoiceEntity> $completion) {
    final String _sql = "SELECT * FROM invoices WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<InvoiceEntity>() {
      @Override
      @Nullable
      public InvoiceEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfSellerName = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerName");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfTax = CursorUtil.getColumnIndexOrThrow(_cursor, "tax");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfIsDeleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isDeleted");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final InvoiceEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpSellerName;
            if (_cursor.isNull(_cursorIndexOfSellerName)) {
              _tmpSellerName = null;
            } else {
              _tmpSellerName = _cursor.getString(_cursorIndexOfSellerName);
            }
            final long _tmpTotalAmount;
            _tmpTotalAmount = _cursor.getLong(_cursorIndexOfTotalAmount);
            final long _tmpDiscount;
            _tmpDiscount = _cursor.getLong(_cursorIndexOfDiscount);
            final long _tmpTax;
            _tmpTax = _cursor.getLong(_cursorIndexOfTax);
            final long _tmpFinalAmount;
            _tmpFinalAmount = _cursor.getLong(_cursorIndexOfFinalAmount);
            final long _tmpPaidAmount;
            _tmpPaidAmount = _cursor.getLong(_cursorIndexOfPaidAmount);
            final long _tmpRemainingAmount;
            _tmpRemainingAmount = _cursor.getLong(_cursorIndexOfRemainingAmount);
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpIsDeleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDeleted);
            _tmpIsDeleted = _tmp != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            _result = new InvoiceEntity(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpCustomerName,_tmpSellerId,_tmpSellerName,_tmpTotalAmount,_tmpDiscount,_tmpTax,_tmpFinalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpDueDate,_tmpNotes,_tmpIsDeleted,_tmpCreatedAt,_tmpUpdatedAt,_tmpApprovedAt,_tmpApprovedBy);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<InvoiceEntity>> getInvoicesByCustomer(final String customerId) {
    final String _sql = "SELECT * FROM invoices WHERE customerId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<InvoiceEntity>>() {
      @Override
      @NonNull
      public List<InvoiceEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfInvoiceNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "invoiceNumber");
          final int _cursorIndexOfCustomerId = CursorUtil.getColumnIndexOrThrow(_cursor, "customerId");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfSellerId = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerId");
          final int _cursorIndexOfSellerName = CursorUtil.getColumnIndexOrThrow(_cursor, "sellerName");
          final int _cursorIndexOfTotalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "totalAmount");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfTax = CursorUtil.getColumnIndexOrThrow(_cursor, "tax");
          final int _cursorIndexOfFinalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "finalAmount");
          final int _cursorIndexOfPaidAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "paidAmount");
          final int _cursorIndexOfRemainingAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingAmount");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPaymentType = CursorUtil.getColumnIndexOrThrow(_cursor, "paymentType");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfIsDeleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isDeleted");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfApprovedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedAt");
          final int _cursorIndexOfApprovedBy = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedBy");
          final List<InvoiceEntity> _result = new ArrayList<InvoiceEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final InvoiceEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpInvoiceNumber;
            if (_cursor.isNull(_cursorIndexOfInvoiceNumber)) {
              _tmpInvoiceNumber = null;
            } else {
              _tmpInvoiceNumber = _cursor.getString(_cursorIndexOfInvoiceNumber);
            }
            final String _tmpCustomerId;
            if (_cursor.isNull(_cursorIndexOfCustomerId)) {
              _tmpCustomerId = null;
            } else {
              _tmpCustomerId = _cursor.getString(_cursorIndexOfCustomerId);
            }
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            final String _tmpSellerId;
            if (_cursor.isNull(_cursorIndexOfSellerId)) {
              _tmpSellerId = null;
            } else {
              _tmpSellerId = _cursor.getString(_cursorIndexOfSellerId);
            }
            final String _tmpSellerName;
            if (_cursor.isNull(_cursorIndexOfSellerName)) {
              _tmpSellerName = null;
            } else {
              _tmpSellerName = _cursor.getString(_cursorIndexOfSellerName);
            }
            final long _tmpTotalAmount;
            _tmpTotalAmount = _cursor.getLong(_cursorIndexOfTotalAmount);
            final long _tmpDiscount;
            _tmpDiscount = _cursor.getLong(_cursorIndexOfDiscount);
            final long _tmpTax;
            _tmpTax = _cursor.getLong(_cursorIndexOfTax);
            final long _tmpFinalAmount;
            _tmpFinalAmount = _cursor.getLong(_cursorIndexOfFinalAmount);
            final long _tmpPaidAmount;
            _tmpPaidAmount = _cursor.getLong(_cursorIndexOfPaidAmount);
            final long _tmpRemainingAmount;
            _tmpRemainingAmount = _cursor.getLong(_cursorIndexOfRemainingAmount);
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final String _tmpPaymentType;
            if (_cursor.isNull(_cursorIndexOfPaymentType)) {
              _tmpPaymentType = null;
            } else {
              _tmpPaymentType = _cursor.getString(_cursorIndexOfPaymentType);
            }
            final Long _tmpDueDate;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmpDueDate = null;
            } else {
              _tmpDueDate = _cursor.getLong(_cursorIndexOfDueDate);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final boolean _tmpIsDeleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDeleted);
            _tmpIsDeleted = _tmp != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final Long _tmpApprovedAt;
            if (_cursor.isNull(_cursorIndexOfApprovedAt)) {
              _tmpApprovedAt = null;
            } else {
              _tmpApprovedAt = _cursor.getLong(_cursorIndexOfApprovedAt);
            }
            final String _tmpApprovedBy;
            if (_cursor.isNull(_cursorIndexOfApprovedBy)) {
              _tmpApprovedBy = null;
            } else {
              _tmpApprovedBy = _cursor.getString(_cursorIndexOfApprovedBy);
            }
            _item = new InvoiceEntity(_tmpId,_tmpInvoiceNumber,_tmpCustomerId,_tmpCustomerName,_tmpSellerId,_tmpSellerName,_tmpTotalAmount,_tmpDiscount,_tmpTax,_tmpFinalAmount,_tmpPaidAmount,_tmpRemainingAmount,_tmpStatus,_tmpPaymentType,_tmpDueDate,_tmpNotes,_tmpIsDeleted,_tmpCreatedAt,_tmpUpdatedAt,_tmpApprovedAt,_tmpApprovedBy);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
