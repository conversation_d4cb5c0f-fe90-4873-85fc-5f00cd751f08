package com.example.sharen.core.constants;

/**
 * ثابت‌های برنامه
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u0006\n\u0002\b,\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0018X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0018X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u0018X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0018X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020\u0011X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010<\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u00020\u0006X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010@\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010A\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010B\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010C\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Lcom/example/sharen/core/constants/AppConstants;", "", "()V", "ALLOWED_IMAGE_TYPES", "", "ANIMATION_DURATION_LONG", "", "ANIMATION_DURATION_MEDIUM", "ANIMATION_DURATION_SHORT", "API_KEY", "BASE_URL", "BUNDLE_CUSTOMER_ID", "BUNDLE_INVOICE_ID", "BUNDLE_PAYMENT_ID", "BUNDLE_PRODUCT_ID", "BUNDLE_USER_ID", "CURRENCY_DECIMAL_PLACES", "", "CURRENCY_SYMBOL", "DATABASE_NAME", "DATABASE_VERSION", "DATE_FORMAT_PERSIAN", "DATE_FORMAT_PERSIAN_FULL", "DEFAULT_COMMISSION_RATE", "", "DEFAULT_CREDIT_LIMIT", "DEFAULT_INSTALLMENT_COUNT", "DEFAULT_MIN_STOCK", "INITIAL_PAGE", "LANG_ENGLISH", "LANG_PERSIAN", "MAX_COMMISSION_RATE", "MAX_CREDIT_LIMIT", "MAX_DISCOUNT_PERCENTAGE", "MAX_IMAGE_SIZE_MB", "MAX_INSTALLMENT_COUNT", "MAX_NAME_LENGTH", "MAX_PASSWORD_LENGTH", "MIN_COMMISSION_RATE", "MIN_CREDIT_LIMIT", "MIN_DISCOUNT_PERCENTAGE", "MIN_INSTALLMENT_COUNT", "MIN_NAME_LENGTH", "MIN_PASSWORD_LENGTH", "MIN_STOCK_WARNING", "NETWORK_TIMEOUT", "NOTIFICATION_CHANNEL_GENERAL", "NOTIFICATION_CHANNEL_PAYMENT", "NOTIFICATION_CHANNEL_REMINDER", "PAGE_SIZE", "PREF_IS_LOGGED_IN", "PREF_LANGUAGE", "PREF_NAME", "PREF_THEME_MODE", "PREF_USER_ID", "PREF_USER_ROLE", "REQUEST_CODE_CAMERA", "REQUEST_CODE_GALLERY", "REQUEST_CODE_PERMISSION", "ROLE_ADMIN", "ROLE_CUSTOMER", "ROLE_MANAGER", "ROLE_SELLER", "SPLASH_DELAY", "THEME_DARK", "THEME_LIGHT", "THEME_SYSTEM", "TIME_FORMAT", "app_debug"})
public final class AppConstants {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DATABASE_NAME = "sharen_database";
    public static final int DATABASE_VERSION = 1;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PREF_NAME = "sharen_prefs";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PREF_USER_ID = "user_id";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PREF_USER_ROLE = "user_role";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PREF_IS_LOGGED_IN = "is_logged_in";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PREF_THEME_MODE = "theme_mode";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PREF_LANGUAGE = "language";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BASE_URL = "https://your-supabase-url.supabase.co/";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String API_KEY = "your-supabase-anon-key";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ROLE_ADMIN = "admin";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ROLE_MANAGER = "manager";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ROLE_SELLER = "seller";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ROLE_CUSTOMER = "customer";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String THEME_LIGHT = "light";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String THEME_DARK = "dark";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String THEME_SYSTEM = "system";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String LANG_PERSIAN = "fa";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String LANG_ENGLISH = "en";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DATE_FORMAT_PERSIAN = "yyyy/MM/dd";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DATE_FORMAT_PERSIAN_FULL = "yyyy/MM/dd HH:mm";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TIME_FORMAT = "HH:mm";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String CURRENCY_SYMBOL = "\u062a\u0648\u0645\u0627\u0646";
    public static final int CURRENCY_DECIMAL_PLACES = 0;
    public static final int PAGE_SIZE = 20;
    public static final int INITIAL_PAGE = 0;
    public static final int MAX_IMAGE_SIZE_MB = 5;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ALLOWED_IMAGE_TYPES = "image/jpeg,image/png,image/jpg";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NOTIFICATION_CHANNEL_GENERAL = "general";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NOTIFICATION_CHANNEL_PAYMENT = "payment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NOTIFICATION_CHANNEL_REMINDER = "reminder";
    public static final int REQUEST_CODE_CAMERA = 1001;
    public static final int REQUEST_CODE_GALLERY = 1002;
    public static final int REQUEST_CODE_PERMISSION = 1003;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUNDLE_CUSTOMER_ID = "customer_id";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUNDLE_INVOICE_ID = "invoice_id";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUNDLE_PRODUCT_ID = "product_id";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUNDLE_PAYMENT_ID = "payment_id";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUNDLE_USER_ID = "user_id";
    public static final long ANIMATION_DURATION_SHORT = 200L;
    public static final long ANIMATION_DURATION_MEDIUM = 300L;
    public static final long ANIMATION_DURATION_LONG = 500L;
    public static final long NETWORK_TIMEOUT = 30L;
    public static final long SPLASH_DELAY = 2000L;
    public static final int MIN_PASSWORD_LENGTH = 6;
    public static final int MAX_PASSWORD_LENGTH = 50;
    public static final int MIN_NAME_LENGTH = 2;
    public static final int MAX_NAME_LENGTH = 50;
    public static final double DEFAULT_COMMISSION_RATE = 0.05;
    public static final double MIN_COMMISSION_RATE = 0.0;
    public static final double MAX_COMMISSION_RATE = 0.5;
    public static final long DEFAULT_CREDIT_LIMIT = 10000000L;
    public static final long MIN_CREDIT_LIMIT = 0L;
    public static final long MAX_CREDIT_LIMIT = 1000000000L;
    public static final int MIN_INSTALLMENT_COUNT = 2;
    public static final int MAX_INSTALLMENT_COUNT = 24;
    public static final int DEFAULT_INSTALLMENT_COUNT = 6;
    public static final int MIN_STOCK_WARNING = 5;
    public static final int DEFAULT_MIN_STOCK = 10;
    public static final double MAX_DISCOUNT_PERCENTAGE = 100.0;
    public static final double MIN_DISCOUNT_PERCENTAGE = 0.0;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.core.constants.AppConstants INSTANCE = null;
    
    private AppConstants() {
        super();
    }
}