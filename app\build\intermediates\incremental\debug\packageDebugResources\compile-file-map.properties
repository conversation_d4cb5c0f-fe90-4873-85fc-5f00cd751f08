#Sat May 24 10:41:08 PDT 2025
com.example.sharen.app-main-5\:/drawable/ic_phone.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_phone.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_product_form.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_product_form.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_forgot_password.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_forgot_password.xml
com.example.sharen.app-main-5\:/drawable/ic_logo.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_logo.xml
com.example.sharen.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.sharen.app-packageDebugResources-2\:/layout/dialog_update_stock.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dialog_update_stock.xml
com.example.sharen.app-main-5\:/menu/menu_product_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_product_details.xml
com.example.sharen.app-main-5\:/drawable/circle_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_background.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_customer_form.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_customer_form.xml
com.example.sharen.app-main-5\:/drawable/ic_customers_empty.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_customers_empty.xml
com.example.sharen.app-main-5\:/drawable/ic_person.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person.xml
com.example.sharen.app-main-5\:/drawable/ic_code.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_code.xml
com.example.sharen.app-main-5\:/font/font_family.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\font\\font_family.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_product_list.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_product_list.xml
com.example.sharen.app-main-5\:/drawable/ic_product_placeholder.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_product_placeholder.xml
com.example.sharen.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_payment_detail.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_payment_detail.xml
com.example.sharen.app-main-5\:/menu/bottom_navigation_menu.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\menu\\bottom_navigation_menu.xml
com.example.sharen.app-main-5\:/xml/backup_rules.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\xml\\backup_rules.xml
com.example.sharen.app-main-5\:/navigation/nav_payment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\navigation\\nav_payment.xml
com.example.sharen.app-main-5\:/drawable/ic_close.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_close.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_customer_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_customer_details.xml
com.example.sharen.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.sharen.app-packageDebugResources-2\:/layout/activity_payment_list.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_payment_list.xml
com.example.sharen.app-main-5\:/drawable/splash_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\splash_background.xml
com.example.sharen.app-main-5\:/drawable/spinner_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\spinner_background.xml
com.example.sharen.app-main-5\:/drawable/status_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\status_background.xml
com.example.sharen.app-main-5\:/navigation/navigation.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\navigation\\navigation.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_installment_edit.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_installment_edit.xml
com.example.sharen.app-main-5\:/drawable/ic_analytics.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_analytics.xml
com.example.sharen.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.sharen.app-packageDebugResources-2\:/layout/item_product.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_product.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_payment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_payment.xml
com.example.sharen.app-main-5\:/drawable/circle_button_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_button_background.xml
com.example.sharen.app-main-5\:/mipmap-hdpi/ic_launcher.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.sharen.app-packageDebugResources-2\:/layout/activity_settings.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_settings.xml
com.example.sharen.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.sharen.app-main-5\:/drawable/rounded_status_bg.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_status_bg.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_customer.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_customer.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_installment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_installment.xml
com.example.sharen.app-main-5\:/font/iransans_regular.ttf=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\font\\iransans_regular.ttf
com.example.sharen.app-main-5\:/drawable/bg_status_paid.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_status_paid.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_transaction.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_transaction.xml
com.example.sharen.app-main-5\:/drawable/ic_add.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_add.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_notification.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_notification.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_login.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_login.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_display_settings.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_display_settings.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_payment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_payment.xml
com.example.sharen.app-main-5\:/drawable/ic_back.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_back.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_invoice_list.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_invoice_list.xml
com.example.sharen.app-packageDebugResources-2\:/layout/dialog_pay_installment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dialog_pay_installment.xml
com.example.sharen.app-main-5\:/drawable/ic_person_add.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person_add.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_invoice.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_invoice.xml
com.example.sharen.app-main-5\:/menu/bottom_nav_menu.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\menu\\bottom_nav_menu.xml
com.example.sharen.app-main-5\:/drawable/ic_check.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_check.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_installment_detail.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_installment_detail.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_security.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_security.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_dashboard.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_dashboard.xml
com.example.sharen.app-main-5\:/drawable/ic_email.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_email.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_main.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.example.sharen.app-main-5\:/drawable/ic_delete.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_delete.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_installment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_installment.xml
com.example.sharen.app-main-5\:/drawable/ic_lock.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_lock.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_payment_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_payment_details.xml
com.example.sharen.app-main-5\:/menu/menu_payment_detail.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_payment_detail.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_installment_list.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_installment_list.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_register.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_register.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_product_test.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_product_test.xml
com.example.sharen.app-main-5\:/navigation/nav_graph_installment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\navigation\\nav_graph_installment.xml
com.example.sharen.app-main-5\:/drawable/ic_call.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_call.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_notification.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_notification.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_sales_invoice.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_sales_invoice.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_invoice_product.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_invoice_product.xml
com.example.sharen.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.sharen.app-main-5\:/drawable/ic_inventory.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_inventory.xml
com.example.sharen.app-main-5\:/drawable/badge_vip_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\badge_vip_background.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_invoice_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_invoice_details.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_add_payment.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_add_payment.xml
com.example.sharen.app-main-5\:/drawable/ic_arrow_back.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_arrow_back.xml
com.example.sharen.app-main-5\:/drawable/ic_edit.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_edit.xml
com.example.sharen.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.sharen.app-packageDebugResources-2\:/layout/activity_splash.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_splash.xml
com.example.sharen.app-main-5\:/drawable/bg_circle_avatar.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_circle_avatar.xml
com.example.sharen.app-packageDebugResources-2\:/layout/dialog_installment_form.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dialog_installment_form.xml
com.example.sharen.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.sharen.app-packageDebugResources-2\:/layout/activity_profile.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_profile.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_report.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_report.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_customer.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_customer.xml
com.example.sharen.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.sharen.app-main-5\:/menu/menu_invoice_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_invoice_details.xml
com.example.sharen.app-packageDebugResources-2\:/layout/item_payment_method.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_payment_method.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_payment_add_edit.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_payment_add_edit.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_password_change.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_password_change.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_notification_settings.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_notification_settings.xml
com.example.sharen.app-main-5\:/xml/file_paths.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\xml\\file_paths.xml
com.example.sharen.app-main-5\:/font/iransans_bold.ttf=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\font\\iransans_bold.ttf
com.example.sharen.app-main-5\:/xml/data_extraction_rules.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\xml\\data_extraction_rules.xml
com.example.sharen.app-main-5\:/drawable/ic_launcher_foreground.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_foreground.xml
com.example.sharen.app-main-5\:/drawable/badge_debtor_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\badge_debtor_background.xml
com.example.sharen.app-main-5\:/mipmap-mdpi/ic_launcher.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.sharen.app-main-5\:/drawable/ic_launcher_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_background.xml
com.example.sharen.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.sharen.app-main-5\:/drawable/ic_customer_avatar.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_customer_avatar.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_product_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_product_details.xml
com.example.sharen.app-packageDebugResources-2\:/layout/fragment_payment_list.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\fragment_payment_list.xml
com.example.sharen.app-packageDebugResources-2\:/layout/activity_customer_list.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_customer_list.xml
com.example.sharen.app-main-5\:/menu/menu_customer_details.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_customer_details.xml
com.example.sharen.app-main-5\:/drawable/search_background.xml=C\:\\New folder\\sharen\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\search_background.xml
