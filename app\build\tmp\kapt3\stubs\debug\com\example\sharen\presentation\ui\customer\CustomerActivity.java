package com.example.sharen.presentation.ui.customer;

/**
 * صفحه مدیریت مشتریان
 */
@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u0000 (2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0001(B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\f\u001a\u00020\u0002H\u0016J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\b\u0010\u0011\u001a\u00020\u000eH\u0002J\u0010\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\u0010\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\b\u0010\u0016\u001a\u00020\u000eH\u0002J\b\u0010\u0017\u001a\u00020\u000eH\u0016J\b\u0010\u0018\u001a\u00020\u000eH\u0002J\b\u0010\u0019\u001a\u00020\u000eH\u0002J\"\u0010\u001a\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001c2\b\u0010\u001e\u001a\u0004\u0018\u00010\u001fH\u0014J\b\u0010 \u001a\u00020\u000eH\u0002J\b\u0010!\u001a\u00020\u000eH\u0002J\b\u0010\"\u001a\u00020\u000eH\u0002J\b\u0010#\u001a\u00020\u000eH\u0002J\b\u0010$\u001a\u00020\u000eH\u0016J\u0010\u0010%\u001a\u00020\u000e2\u0006\u0010&\u001a\u00020\'H\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0006\u001a\u00020\u00078BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\n\u0010\u000b\u001a\u0004\b\b\u0010\t\u00a8\u0006)"}, d2 = {"Lcom/example/sharen/presentation/ui/customer/CustomerActivity;", "Lcom/example/sharen/core/base/BaseActivity;", "Lcom/example/sharen/databinding/ActivityCustomerBinding;", "()V", "customerAdapter", "Lcom/example/sharen/presentation/ui/customer/adapter/CustomerAdapter;", "customerViewModel", "Lcom/example/sharen/presentation/viewmodel/CustomerViewModel;", "getCustomerViewModel", "()Lcom/example/sharen/presentation/viewmodel/CustomerViewModel;", "customerViewModel$delegate", "Lkotlin/Lazy;", "getViewBinding", "makePhoneCall", "", "phoneNumber", "", "navigateToAddCustomer", "navigateToCustomerDetails", "customer", "Lcom/example/sharen/domain/model/Customer;", "navigateToEditCustomer", "observeCustomers", "observeData", "observeLoadingState", "observeMessages", "onActivityResult", "requestCode", "", "resultCode", "data", "Landroid/content/Intent;", "setupClickListeners", "setupRecyclerView", "setupSearchView", "setupToolbar", "setupViews", "updateEmptyState", "isEmpty", "", "Companion", "app_debug"})
public final class CustomerActivity extends com.example.sharen.core.base.BaseActivity<com.example.sharen.databinding.ActivityCustomerBinding> {
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy customerViewModel$delegate = null;
    private com.example.sharen.presentation.ui.customer.adapter.CustomerAdapter customerAdapter;
    private static final int REQUEST_ADD_CUSTOMER = 1001;
    private static final int REQUEST_EDIT_CUSTOMER = 1002;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.presentation.ui.customer.CustomerActivity.Companion Companion = null;
    
    public CustomerActivity() {
        super();
    }
    
    private final com.example.sharen.presentation.viewmodel.CustomerViewModel getCustomerViewModel() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.databinding.ActivityCustomerBinding getViewBinding() {
        return null;
    }
    
    @java.lang.Override
    public void setupViews() {
    }
    
    @java.lang.Override
    public void observeData() {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void setupSearchView() {
    }
    
    private final void observeCustomers() {
    }
    
    private final void observeLoadingState() {
    }
    
    private final void observeMessages() {
    }
    
    private final void updateEmptyState(boolean isEmpty) {
    }
    
    private final void navigateToCustomerDetails(com.example.sharen.domain.model.Customer customer) {
    }
    
    private final void navigateToAddCustomer() {
    }
    
    private final void navigateToEditCustomer(com.example.sharen.domain.model.Customer customer) {
    }
    
    private final void makePhoneCall(java.lang.String phoneNumber) {
    }
    
    @java.lang.Override
    protected void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable
    android.content.Intent data) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/sharen/presentation/ui/customer/CustomerActivity$Companion;", "", "()V", "REQUEST_ADD_CUSTOMER", "", "REQUEST_EDIT_CUSTOMER", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}