package com.example.sharen.domain.usecase.product;

/**
 * Use Case برای بروزرسانی موجودی محصول
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J2\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u000e"}, d2 = {"Lcom/example/sharen/domain/usecase/product/UpdateProductStockUseCase;", "", "productRepository", "Lcom/example/sharen/domain/repository/ProductRepository;", "(Lcom/example/sharen/domain/repository/ProductRepository;)V", "invoke", "Lkotlin/Result;", "", "productId", "", "newStock", "", "invoke-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class UpdateProductStockUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.ProductRepository productRepository = null;
    
    @javax.inject.Inject
    public UpdateProductStockUseCase(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.ProductRepository productRepository) {
        super();
    }
}