package com.example.sharen.domain.repository;

/**
 * Repository Interface برای مدیریت فاکتورها
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u000e\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J2\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\r\u0010\u000eJ*\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J*\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0014\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0015\u0010\u0012J\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u0017H&J,\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001a\u0010\u0012J\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00180\u00172\u0006\u0010\n\u001a\u00020\u000bH&J\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u00172\u0006\u0010\u001d\u001a\u00020\u000bH&J$\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u00172\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H&J\u001c\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u00172\u0006\u0010#\u001a\u00020\u000bH&J\u001c\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u00172\u0006\u0010%\u001a\u00020&H&J\u001e\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u00172\b\b\u0002\u0010(\u001a\u00020)H&J>\u0010*\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010+0\u00032\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b,\u0010-J2\u0010.\u001a\b\u0012\u0004\u0012\u00020/0\u00032\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b0\u0010-J*\u00101\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b2\u0010\u0012J\u001c\u00103\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00180\u00172\u0006\u00104\u001a\u00020\u000bH&J*\u00105\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b6\u0010\u0007J*\u00107\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\f\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b8\u00109J2\u0010:\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010%\u001a\u00020&H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b;\u0010<\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006="}, d2 = {"Lcom/example/sharen/domain/repository/InvoiceRepository;", "", "addInvoice", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Invoice;", "invoice", "addInvoice-gIAlu-s", "(Lcom/example/sharen/domain/model/Invoice;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addInvoiceItem", "Lcom/example/sharen/domain/model/InvoiceItem;", "invoiceId", "", "item", "addInvoiceItem-0E7RQCE", "(Ljava/lang/String;Lcom/example/sharen/domain/model/InvoiceItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInvoice", "", "deleteInvoice-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInvoiceItem", "itemId", "deleteInvoiceItem-gIAlu-s", "getAllInvoices", "Lkotlinx/coroutines/flow/Flow;", "", "getInvoiceById", "getInvoiceById-gIAlu-s", "getInvoiceItems", "getInvoicesByCustomer", "customerId", "getInvoicesByDateRange", "startDate", "Ljava/util/Date;", "endDate", "getInvoicesBySeller", "sellerId", "getInvoicesByStatus", "status", "Lcom/example/sharen/domain/model/InvoiceStatus;", "getRecentInvoices", "limit", "", "getSalesStatistics", "", "getSalesStatistics-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTotalSales", "", "getTotalSales-0E7RQCE", "recalculateInvoice", "recalculateInvoice-gIAlu-s", "searchInvoices", "query", "updateInvoice", "updateInvoice-gIAlu-s", "updateInvoiceItem", "updateInvoiceItem-gIAlu-s", "(Lcom/example/sharen/domain/model/InvoiceItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInvoiceStatus", "updateInvoiceStatus-0E7RQCE", "(Ljava/lang/String;Lcom/example/sharen/domain/model/InvoiceStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface InvoiceRepository {
    
    /**
     * دریافت تمام فاکتورها
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> getAllInvoices();
    
    /**
     * دریافت فاکتورهای مشتری
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> getInvoicesByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    /**
     * دریافت فاکتورهای فروشنده
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> getInvoicesBySeller(@org.jetbrains.annotations.NotNull
    java.lang.String sellerId);
    
    /**
     * جستجوی فاکتورها
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> searchInvoices(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * دریافت فاکتورها در بازه زمانی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> getInvoicesByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate);
    
    /**
     * دریافت فاکتورها بر اساس وضعیت
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> getInvoicesByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.InvoiceStatus status);
    
    /**
     * دریافت آیتم‌های فاکتور
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.InvoiceItem>> getInvoiceItems(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId);
    
    /**
     * دریافت فاکتورهای اخیر
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Invoice>> getRecentInvoices(int limit);
    
    /**
     * Repository Interface برای مدیریت فاکتورها
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}