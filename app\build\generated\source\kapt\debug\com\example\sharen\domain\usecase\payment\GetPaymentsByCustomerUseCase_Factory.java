package com.example.sharen.domain.usecase.payment;

import com.example.sharen.domain.repository.PaymentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetPaymentsByCustomerUseCase_Factory implements Factory<GetPaymentsByCustomerUseCase> {
  private final Provider<PaymentRepository> paymentRepositoryProvider;

  public GetPaymentsByCustomerUseCase_Factory(
      Provider<PaymentRepository> paymentRepositoryProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public GetPaymentsByCustomerUseCase get() {
    return newInstance(paymentRepositoryProvider.get());
  }

  public static GetPaymentsByCustomerUseCase_Factory create(
      Provider<PaymentRepository> paymentRepositoryProvider) {
    return new GetPaymentsByCustomerUseCase_Factory(paymentRepositoryProvider);
  }

  public static GetPaymentsByCustomerUseCase newInstance(PaymentRepository paymentRepository) {
    return new GetPaymentsByCustomerUseCase(paymentRepository);
  }
}
