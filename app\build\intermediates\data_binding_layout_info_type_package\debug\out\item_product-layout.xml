<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_product" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_product_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="94" endOffset="51"/></Target><Target id="@+id/ivProductImage" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="63"/></Target><Target id="@+id/tvProductName" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="38" endOffset="36"/></Target><Target id="@+id/tvProductCategory" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="52" endOffset="42"/></Target><Target id="@+id/tvProductCode" view="TextView"><Expressions/><location startLine="54" startOffset="8" endLine="63" endOffset="37"/></Target><Target id="@+id/tvProductPrice" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="75" endOffset="40"/></Target><Target id="@+id/tvStockStatus" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="91" endOffset="32"/></Target></Targets></Layout>