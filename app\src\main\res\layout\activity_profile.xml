<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.profile.ProfileActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/profile"
        app:titleTextColor="@android:color/white" />

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Profile Image -->
            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/ivProfileImage"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_customer_avatar"
                app:civ_border_color="@color/primary"
                app:civ_border_width="2dp" />

            <!-- Name -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/name"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPersonName" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Email -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/email"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="textEmailAddress" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Phone -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="@string/phone"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="phone" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Save Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:backgroundTint="@color/primary"
                android:text="@string/save"
                android:textColor="@android:color/white" />

            <!-- Change Password Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnChangePassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:backgroundTint="@color/secondary"
                android:text="@string/change_password"
                android:textColor="@android:color/white" />

            <!-- Logout Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/error"
                android:text="@string/logout"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
