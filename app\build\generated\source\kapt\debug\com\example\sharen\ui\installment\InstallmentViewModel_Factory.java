package com.example.sharen.ui.installment;

import com.example.sharen.data.repository.InstallmentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InstallmentViewModel_Factory implements Factory<InstallmentViewModel> {
  private final Provider<InstallmentRepository> repositoryProvider;

  public InstallmentViewModel_Factory(Provider<InstallmentRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public InstallmentViewModel get() {
    return newInstance(repositoryProvider.get());
  }

  public static InstallmentViewModel_Factory create(
      Provider<InstallmentRepository> repositoryProvider) {
    return new InstallmentViewModel_Factory(repositoryProvider);
  }

  public static InstallmentViewModel newInstance(InstallmentRepository repository) {
    return new InstallmentViewModel(repository);
  }
}
