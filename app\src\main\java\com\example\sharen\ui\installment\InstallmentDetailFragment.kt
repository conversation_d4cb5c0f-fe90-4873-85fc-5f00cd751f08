package com.example.sharen.ui.installment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.example.sharen.R
import com.example.sharen.databinding.FragmentInstallmentDetailBinding
import com.example.sharen.databinding.DialogPayInstallmentBinding
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
class InstallmentDetailFragment : Fragment() {

    private var _binding: FragmentInstallmentDetailBinding? = null
    private val binding get() = _binding!!

    private val viewModel: InstallmentViewModel by viewModels()
    private val args: InstallmentDetailFragmentArgs by navArgs()
    private val dateFormat = SimpleDateFormat("yyyy/MM/dd", Locale.getDefault())

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentInstallmentDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupObservers()
        setupListeners()
        viewModel.getInstallment(args.installmentId)
    }

    private fun setupObservers() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.installments.collect { installments ->
                    installments.find { it.id == args.installmentId }?.let { installment ->
                        updateUI(installment)
                    }
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.loading.collect { isLoading ->
                    binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
                }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.error.collect { error ->
                    error?.let {
                        Snackbar.make(binding.root, it, Snackbar.LENGTH_LONG).show()
                        viewModel.clearError()
                    }
                }
            }
        }
    }

    private fun setupListeners() {
        binding.buttonEdit.setOnClickListener {
            findNavController().navigate(
                InstallmentDetailFragmentDirections.actionInstallmentDetailToInstallmentEdit(args.installmentId)
            )
        }

        binding.buttonDelete.setOnClickListener {
            showDeleteConfirmationDialog()
        }

        binding.buttonPay.setOnClickListener {
            showPayDialog()
        }

        binding.buttonRemind.setOnClickListener {
            viewModel.sendReminder(args.installmentId)
            Snackbar.make(binding.root, R.string.installment_reminder_sent, Snackbar.LENGTH_LONG).show()
        }
    }

    private fun updateUI(installment: Installment) {
        binding.apply {
            textViewTotalAmount.text = installment.totalAmount.toString()
            textViewPaidAmount.text = installment.paidAmount.toString()
            textViewRemainingAmount.text = (installment.totalAmount - installment.paidAmount).toString()
            textViewDueDate.text = dateFormat.format(installment.dueDate)
            chipStatus.text = installment.status.name
            textViewNotes.text = installment.notes ?: "-"

            // Show/hide buttons based on installment status
            buttonPay.isEnabled = installment.status == InstallmentStatus.PENDING
            buttonRemind.isEnabled = installment.status == InstallmentStatus.PENDING
            buttonEdit.isEnabled = installment.status != InstallmentStatus.PAID
            buttonDelete.isEnabled = installment.status != InstallmentStatus.PAID
        }
    }

    private fun showDeleteConfirmationDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.delete_installment)
            .setMessage(R.string.confirm_delete_installment)
            .setPositiveButton(R.string.delete) { _, _ ->
                viewModel.deleteInstallment(args.installmentId)
                findNavController().navigateUp()
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    private fun showPayDialog() {
        val installment = viewModel.installments.value.find { it.id == args.installmentId } ?: return
        val remainingAmount = installment.totalAmount - installment.paidAmount

        val dialogBinding = DialogPayInstallmentBinding.inflate(layoutInflater)
        dialogBinding.editTextAmount.setText(remainingAmount.toString())

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(R.string.pay_installment)
            .setView(dialogBinding.root)
            .setPositiveButton(R.string.pay) { _, _ ->
                val amount = dialogBinding.editTextAmount.text.toString().toDoubleOrNull()
                if (amount != null && amount > 0 && amount <= remainingAmount) {
                    viewModel.payInstallment(args.installmentId, amount)
                    Snackbar.make(binding.root, R.string.installment_paid, Snackbar.LENGTH_LONG).show()
                } else {
                    Snackbar.make(binding.root, R.string.error_invalid_amount, Snackbar.LENGTH_LONG).show()
                }
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 