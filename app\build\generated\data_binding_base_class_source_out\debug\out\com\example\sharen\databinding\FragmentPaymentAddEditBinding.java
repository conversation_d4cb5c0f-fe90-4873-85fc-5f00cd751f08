// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPaymentAddEditBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonCancel;

  @NonNull
  public final MaterialButton buttonSave;

  @NonNull
  public final TextInputEditText editTextAmount;

  @NonNull
  public final TextInputEditText editTextNotes;

  @NonNull
  public final TextInputEditText editTextReference;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final AutoCompleteTextView spinnerPaymentMethod;

  @NonNull
  public final AutoCompleteTextView spinnerPaymentStatus;

  @NonNull
  public final TextInputLayout textInputLayoutAmount;

  @NonNull
  public final TextInputLayout textInputLayoutNotes;

  @NonNull
  public final TextInputLayout textInputLayoutPaymentMethod;

  @NonNull
  public final TextInputLayout textInputLayoutPaymentStatus;

  @NonNull
  public final TextInputLayout textInputLayoutReference;

  @NonNull
  public final TextView textViewPaymentMethodError;

  @NonNull
  public final TextView textViewPaymentStatusError;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentPaymentAddEditBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonCancel, @NonNull MaterialButton buttonSave,
      @NonNull TextInputEditText editTextAmount, @NonNull TextInputEditText editTextNotes,
      @NonNull TextInputEditText editTextReference, @NonNull ProgressBar progressBar,
      @NonNull AutoCompleteTextView spinnerPaymentMethod,
      @NonNull AutoCompleteTextView spinnerPaymentStatus,
      @NonNull TextInputLayout textInputLayoutAmount, @NonNull TextInputLayout textInputLayoutNotes,
      @NonNull TextInputLayout textInputLayoutPaymentMethod,
      @NonNull TextInputLayout textInputLayoutPaymentStatus,
      @NonNull TextInputLayout textInputLayoutReference,
      @NonNull TextView textViewPaymentMethodError, @NonNull TextView textViewPaymentStatusError,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonSave = buttonSave;
    this.editTextAmount = editTextAmount;
    this.editTextNotes = editTextNotes;
    this.editTextReference = editTextReference;
    this.progressBar = progressBar;
    this.spinnerPaymentMethod = spinnerPaymentMethod;
    this.spinnerPaymentStatus = spinnerPaymentStatus;
    this.textInputLayoutAmount = textInputLayoutAmount;
    this.textInputLayoutNotes = textInputLayoutNotes;
    this.textInputLayoutPaymentMethod = textInputLayoutPaymentMethod;
    this.textInputLayoutPaymentStatus = textInputLayoutPaymentStatus;
    this.textInputLayoutReference = textInputLayoutReference;
    this.textViewPaymentMethodError = textViewPaymentMethodError;
    this.textViewPaymentStatusError = textViewPaymentStatusError;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPaymentAddEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPaymentAddEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_payment_add_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPaymentAddEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonCancel;
      MaterialButton buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.buttonSave;
      MaterialButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.editTextAmount;
      TextInputEditText editTextAmount = ViewBindings.findChildViewById(rootView, id);
      if (editTextAmount == null) {
        break missingId;
      }

      id = R.id.editTextNotes;
      TextInputEditText editTextNotes = ViewBindings.findChildViewById(rootView, id);
      if (editTextNotes == null) {
        break missingId;
      }

      id = R.id.editTextReference;
      TextInputEditText editTextReference = ViewBindings.findChildViewById(rootView, id);
      if (editTextReference == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.spinnerPaymentMethod;
      AutoCompleteTextView spinnerPaymentMethod = ViewBindings.findChildViewById(rootView, id);
      if (spinnerPaymentMethod == null) {
        break missingId;
      }

      id = R.id.spinnerPaymentStatus;
      AutoCompleteTextView spinnerPaymentStatus = ViewBindings.findChildViewById(rootView, id);
      if (spinnerPaymentStatus == null) {
        break missingId;
      }

      id = R.id.textInputLayoutAmount;
      TextInputLayout textInputLayoutAmount = ViewBindings.findChildViewById(rootView, id);
      if (textInputLayoutAmount == null) {
        break missingId;
      }

      id = R.id.textInputLayoutNotes;
      TextInputLayout textInputLayoutNotes = ViewBindings.findChildViewById(rootView, id);
      if (textInputLayoutNotes == null) {
        break missingId;
      }

      id = R.id.textInputLayoutPaymentMethod;
      TextInputLayout textInputLayoutPaymentMethod = ViewBindings.findChildViewById(rootView, id);
      if (textInputLayoutPaymentMethod == null) {
        break missingId;
      }

      id = R.id.textInputLayoutPaymentStatus;
      TextInputLayout textInputLayoutPaymentStatus = ViewBindings.findChildViewById(rootView, id);
      if (textInputLayoutPaymentStatus == null) {
        break missingId;
      }

      id = R.id.textInputLayoutReference;
      TextInputLayout textInputLayoutReference = ViewBindings.findChildViewById(rootView, id);
      if (textInputLayoutReference == null) {
        break missingId;
      }

      id = R.id.textViewPaymentMethodError;
      TextView textViewPaymentMethodError = ViewBindings.findChildViewById(rootView, id);
      if (textViewPaymentMethodError == null) {
        break missingId;
      }

      id = R.id.textViewPaymentStatusError;
      TextView textViewPaymentStatusError = ViewBindings.findChildViewById(rootView, id);
      if (textViewPaymentStatusError == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentPaymentAddEditBinding((CoordinatorLayout) rootView, buttonCancel,
          buttonSave, editTextAmount, editTextNotes, editTextReference, progressBar,
          spinnerPaymentMethod, spinnerPaymentStatus, textInputLayoutAmount, textInputLayoutNotes,
          textInputLayoutPaymentMethod, textInputLayoutPaymentStatus, textInputLayoutReference,
          textViewPaymentMethodError, textViewPaymentStatusError, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
