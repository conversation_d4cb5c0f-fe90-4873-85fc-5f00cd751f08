<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <application
        android:name=".SharenApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Sharen"
        tools:targetApi="31">
        <activity
            android:name=".ui.auth.SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity 
            android:name=".ui.auth.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
            
        <activity 
            android:name=".ui.auth.RegisterActivity"
            android:exported="false" />
            
        <activity 
            android:name=".ui.auth.ForgotPasswordActivity"
            android:exported="false" />
            
        <activity 
            android:name=".ui.dashboard.DashboardActivity" />
            
        <activity 
            android:name=".ui.customer.CustomerListActivity" />
            
        <activity 
            android:name=".ui.customer.CustomerDetailsActivity"
            android:exported="false" />
            
        <activity 
            android:name=".ui.customer.CustomerFormActivity"
            android:exported="false" />
            
        <activity 
            android:name=".ui.product.ProductListActivity" />
            
        <activity 
            android:name=".ui.product.ProductDetailsActivity"
            android:exported="false" />
            
        <activity 
            android:name=".ui.product.ProductFormActivity"
            android:exported="false" />
            
        <activity 
            android:name=".ui.product.ProductTestActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.invoice.InvoiceListActivity" />
            
        <activity
            android:name=".ui.invoice.InvoiceDetailsActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.invoice.SalesInvoiceActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.report.ReportActivity" />
            
        <activity
            android:name=".ui.profile.ProfileActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.profile.PasswordChangeActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.profile.NotificationSettingsActivity" />
            
        <activity
            android:name=".ui.profile.DisplaySettingsActivity" />
            
        <activity
            android:name=".ui.payment.PaymentActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.payment.PaymentListActivity"
            android:exported="false" />
            
        <activity
            android:name=".ui.settings.SettingsActivity" />
            
        <activity
            android:name=".ui.profile.SecurityActivity" />
            
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>