-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:75:9-83:20
	android:grantUriPermissions
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:79:13-47
	android:authorities
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:77:13-64
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:78:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:76:13-62
manifest
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:2:1-86:12
MERGED from [androidx.databinding:databinding-adapters:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5c85f241787ca975ca5fa8ff7b3abe0\transformed\databinding-adapters-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\5004a97cb7dc8062fa16b840e772e26d\transformed\databinding-ktx-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e7f458e89d20a35d32d7accfb3a3095\transformed\databinding-runtime-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\477dd360ee908cc2c47cc1866c8e475f\transformed\viewbinding-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.aliab:Persian-Date-Picker-Dialog:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de5c6087e6e8099c79463a02f792cb25\transformed\Persian-Date-Picker-Dialog-1.8.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.hilt:hilt-navigation-fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b25b6da9f48b436388dfa4293d86710\transformed\hilt-navigation-fragment-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fd2e400e0e6e1189c3abedebe8a5f1b\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e19ffa17c8107a30cf8da51595307665\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e64f79d3becc4a168637120bbd00d6b7\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\db14ab46855b6b3881d50a981c761187\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\718071914bc0b7676e4d4e420f10a2ce\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\64ef2fd018b9292fed42e9659d5a333f\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d3407e628a1a5ae7444ae1cc913acd5\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff7c4bbb583b681d30645c3dd59c4ea8\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\92de7ef93adc4accc3707735266617be\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cad535b8da700643fbff50b55d6345da\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9f6704c3a9f18700dc8b3aa437cf6df\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c6c050f398432a931b6ee52c494a0f0\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c3e8f4a7ead4047abde331bf94f8ded\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1fff946e491ca2820c16eae4e8658e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04b3418c9e49cfb82177be117aeccb73\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e741f154df0901f2e7a188e39368a893\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c326a7b12a0f646862a4c084d1840334\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\875cfff27f79ef93586cb6c5896eb3d4\transformed\hilt-work-1.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.50] C:\Users\<USER>\.gradle\caches\8.12\transforms\d93476cb59880de3d5bea44feb90c293\transformed\hilt-android-2.50\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb4392a448786d2dac23690c61464f0e\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\deb7fcee24bd565bb4e56d2ced7b41cd\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\de9b58fba77d5bf606c0a3ca65112b15\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\38ac181bde7d4b3fe1f0020edeb85132\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcecc836389a62ceb723920106438ef\transformed\activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\093c5fb5689923fe06ceb4583cfb2c52\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\75722a330a576fa72abf9a6f0a82d667\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:17:1-132:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa284aa4aab87822765d0335c6f122d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc0761dc19337267d84f4063dfd5eee7\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c82ef162eb08284bb52c19050cfbd3cd\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d0dbbefd9d6f255a287860f66db14291\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef73f4eebe2139358b334d631e924393\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb49cac98d19c51a3c97e50c18b3949e\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\59e9d6232c99a795dfad2d47e2da6cf7\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\46f7d17b573b7515a12ff0fbf51740d5\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\584ba6de600b0b2de543cf4a240cd130\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\929b9240d24aeb3703f5157de8604924\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a1ca8c49dded26ddf6804ff0b9818a1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab5fb6818b6562600503b06553eed6d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7470cfdef8efca626a5314889e13507b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\36b7d76ad2f58bd1891e547f214a82df\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc8ca5417d853dff86010c14712bbb21\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8783b0651565a62c004763fdd4ef5315\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\858e732f51e71b3b97441d90b999b156\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1acad1539ee7d97e8d5773c047b9075e\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a098678d2e28ff48309f0173c17c1f3\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8435a3a582374c9c2373119d2b4994f8\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3796789e5f2c5605d83340e4c0256fb\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\be4ee609cc36724fa244e825f1cdc4e0\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cf0d5ed50805ac881e7618033934ff7\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1cb9b60623db7b1240a92a3d6ec2d51\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4a7c50bad58b4d141aa2541b246c582\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a1f436386034f797112f3fe962fdfcf\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5bcbf758b8ad74828740818432eb0e81\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf206b7a0bdc7b8c5c735214c771f2a9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f4891602327450c20f7f1089221afcea\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c33dfd598d7f5cd9493e3c31b54d9b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ada6cd01769e3db6f41ea87e1cf55807\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4947c8c8f82cbd37fcb873254c78abf3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67c46fef93599cc498da70ae63e3709f\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e25154ae9a84858f82cefea9843a4f4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6b87a72c1240a1b5d232cd18ad3ef55\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93d40b49572cfb0848e92cb5be0d5e29\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\141d7b7a374a5c99f9674fc86825e26a\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba42166b23584acd65358876f90402e2\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\46e619b66ff692f7fbc181ca9734df7c\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e7b5267fb09ce156305a6d31dbe90d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f808fe96c0fb86139ec57aa436721b2b\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2041c592220dedec16d3b1f3d460e556\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b95ed4432398560d7b8e968b0d281afc\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6deb02d5036e4c4e473136df26114169\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\facceb7182a7be6418b7a3f59afda498\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\9699702750ae15264150987f34a92d1b\transformed\krypto-debug\AndroidManifest.xml:2:1-9:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3c7d8dec4f524b158a44e92135002e\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\c49329f623719a3fdcb1405f34aa51c0\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\be2d7c30c5203c92c2b43c4c9442e65c\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e1f15c7316e56cadf6817b0da6b71a3\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79346057590700477e9d5e9502cb4282\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a528f28d8d6cd0b09e9f3d146f151e3\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e907fae608558eb35db57c99eeb38393\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b40160fec95bf60b86fc39e1f91de936\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb836af334ead3d902e425ea79f1b22c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe93e09fded37ed8016f00b0eee8963c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\895e74e841efc17f6531e29c7b3e059e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a822e87a2b8c36cacf1168fab84c8dbb\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9e5d83c14b7483c6f10d6619b23fe62\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ff3186779a9cfd4eabcccbee49b869e\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2077a6705442552801dcf9762a1f3c82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b24fa3a990d7986aa702dc13e3394489\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ee953b071d97dcad887ccf2700d95c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1a4ab591a1058cc36e51f7c72c24268\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.50] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f61e53be1b6f3cca8b7efa23c05c2e0\transformed\dagger-lint-aar-2.50\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.samanzamani:PersianDate:1.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9f9c6f64e8ce04ecff2e57d983bf023\transformed\PersianDate-1.3.4\AndroidManifest.xml:2:1-12:12
	package
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.CAMERA
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:8:5-107
	android:maxSdkVersion
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:8:78-104
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:9:5-76
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:9:22-73
application
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:11:5-84:19
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:11:5-84:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c6c050f398432a931b6ee52c494a0f0\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c6c050f398432a931b6ee52c494a0f0\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c3e8f4a7ead4047abde331bf94f8ded\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c3e8f4a7ead4047abde331bf94f8ded\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:30:5-130:19
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:30:5-130:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2077a6705442552801dcf9762a1f3c82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2077a6705442552801dcf9762a1f3c82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.samanzamani:PersianDate:1.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9f9c6f64e8ce04ecff2e57d983bf023\transformed\PersianDate-1.3.4\AndroidManifest.xml:9:5-10:19
MERGED from [com.github.samanzamani:PersianDate:1.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9f9c6f64e8ce04ecff2e57d983bf023\transformed\PersianDate-1.3.4\AndroidManifest.xml:9:5-10:19
	android:extractNativeLibs
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:19:9-35
	android:label
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:17:9-41
	android:fullBackupContent
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:15:9-54
	android:roundIcon
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:18:9-54
	tools:targetApi
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:21:9-29
	android:icon
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:16:9-43
	android:allowBackup
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:20:9-44
	android:dataExtractionRules
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:14:9-65
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:12:9-42
activity#com.example.sharen.MainActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:22:9-30:20
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:24:13-36
	android:theme
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:25:13-55
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:23:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:26:13-29:29
action#android.intent.action.MAIN
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:28:27-74
activity#com.example.sharen.presentation.ui.auth.LoginActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:32:9-35:63
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:35:13-60
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:33:13-63
activity#com.example.sharen.presentation.ui.auth.RegisterActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:37:9-40:63
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:39:13-37
	android:theme
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:40:13-60
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:38:13-66
activity#com.example.sharen.presentation.ui.auth.ForgotPasswordActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:42:9-45:63
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:44:13-37
	android:theme
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:45:13-60
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:43:13-72
activity#com.example.sharen.presentation.ui.dashboard.DashboardActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:47:9-49:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:48:13-72
activity#com.example.sharen.presentation.ui.customer.CustomerActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:51:9-53:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:52:13-70
activity#com.example.sharen.presentation.ui.product.ProductActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:55:9-57:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:56:13-68
activity#com.example.sharen.presentation.ui.invoice.InvoiceActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:59:9-61:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:60:13-68
activity#com.example.sharen.presentation.ui.payment.PaymentActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:63:9-65:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:64:13-68
activity#com.example.sharen.presentation.ui.report.ReportActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:67:9-69:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:69:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:68:13-66
activity#com.example.sharen.presentation.ui.settings.SettingsActivity
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:71:9-73:40
	android:exported
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:73:13-37
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:72:13-70
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:80:13-82:54
	android:resource
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:82:17-51
	android:name
		ADDED from C:\New folder\sharen\app\src\main\AndroidManifest.xml:81:17-67
uses-sdk
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5c85f241787ca975ca5fa8ff7b3abe0\transformed\databinding-adapters-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d5c85f241787ca975ca5fa8ff7b3abe0\transformed\databinding-adapters-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\5004a97cb7dc8062fa16b840e772e26d\transformed\databinding-ktx-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\5004a97cb7dc8062fa16b840e772e26d\transformed\databinding-ktx-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e7f458e89d20a35d32d7accfb3a3095\transformed\databinding-runtime-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e7f458e89d20a35d32d7accfb3a3095\transformed\databinding-runtime-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\477dd360ee908cc2c47cc1866c8e475f\transformed\viewbinding-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\477dd360ee908cc2c47cc1866c8e475f\transformed\viewbinding-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.aliab:Persian-Date-Picker-Dialog:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de5c6087e6e8099c79463a02f792cb25\transformed\Persian-Date-Picker-Dialog-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.aliab:Persian-Date-Picker-Dialog:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\de5c6087e6e8099c79463a02f792cb25\transformed\Persian-Date-Picker-Dialog-1.8.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.hilt:hilt-navigation-fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b25b6da9f48b436388dfa4293d86710\transformed\hilt-navigation-fragment-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-fragment:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b25b6da9f48b436388dfa4293d86710\transformed\hilt-navigation-fragment-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fd2e400e0e6e1189c3abedebe8a5f1b\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fd2e400e0e6e1189c3abedebe8a5f1b\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e19ffa17c8107a30cf8da51595307665\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e19ffa17c8107a30cf8da51595307665\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e64f79d3becc4a168637120bbd00d6b7\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\e64f79d3becc4a168637120bbd00d6b7\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\db14ab46855b6b3881d50a981c761187\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\db14ab46855b6b3881d50a981c761187\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\718071914bc0b7676e4d4e420f10a2ce\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\718071914bc0b7676e4d4e420f10a2ce\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\64ef2fd018b9292fed42e9659d5a333f\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\64ef2fd018b9292fed42e9659d5a333f\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d3407e628a1a5ae7444ae1cc913acd5\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d3407e628a1a5ae7444ae1cc913acd5\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff7c4bbb583b681d30645c3dd59c4ea8\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff7c4bbb583b681d30645c3dd59c4ea8\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\92de7ef93adc4accc3707735266617be\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\92de7ef93adc4accc3707735266617be\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cad535b8da700643fbff50b55d6345da\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cad535b8da700643fbff50b55d6345da\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9f6704c3a9f18700dc8b3aa437cf6df\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d9f6704c3a9f18700dc8b3aa437cf6df\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c6c050f398432a931b6ee52c494a0f0\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c6c050f398432a931b6ee52c494a0f0\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c3e8f4a7ead4047abde331bf94f8ded\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c3e8f4a7ead4047abde331bf94f8ded\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1fff946e491ca2820c16eae4e8658e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1fff946e491ca2820c16eae4e8658e\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04b3418c9e49cfb82177be117aeccb73\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04b3418c9e49cfb82177be117aeccb73\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e741f154df0901f2e7a188e39368a893\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e741f154df0901f2e7a188e39368a893\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c326a7b12a0f646862a4c084d1840334\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c326a7b12a0f646862a4c084d1840334\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\875cfff27f79ef93586cb6c5896eb3d4\transformed\hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\875cfff27f79ef93586cb6c5896eb3d4\transformed\hilt-work-1.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.50] C:\Users\<USER>\.gradle\caches\8.12\transforms\d93476cb59880de3d5bea44feb90c293\transformed\hilt-android-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.50] C:\Users\<USER>\.gradle\caches\8.12\transforms\d93476cb59880de3d5bea44feb90c293\transformed\hilt-android-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb4392a448786d2dac23690c61464f0e\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb4392a448786d2dac23690c61464f0e\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\deb7fcee24bd565bb4e56d2ced7b41cd\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\deb7fcee24bd565bb4e56d2ced7b41cd\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\de9b58fba77d5bf606c0a3ca65112b15\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\de9b58fba77d5bf606c0a3ca65112b15\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\38ac181bde7d4b3fe1f0020edeb85132\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\38ac181bde7d4b3fe1f0020edeb85132\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcecc836389a62ceb723920106438ef\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bcecc836389a62ceb723920106438ef\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\093c5fb5689923fe06ceb4583cfb2c52\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\093c5fb5689923fe06ceb4583cfb2c52\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\75722a330a576fa72abf9a6f0a82d667\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\75722a330a576fa72abf9a6f0a82d667\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa284aa4aab87822765d0335c6f122d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa284aa4aab87822765d0335c6f122d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc0761dc19337267d84f4063dfd5eee7\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc0761dc19337267d84f4063dfd5eee7\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c82ef162eb08284bb52c19050cfbd3cd\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c82ef162eb08284bb52c19050cfbd3cd\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d0dbbefd9d6f255a287860f66db14291\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d0dbbefd9d6f255a287860f66db14291\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef73f4eebe2139358b334d631e924393\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef73f4eebe2139358b334d631e924393\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb49cac98d19c51a3c97e50c18b3949e\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb49cac98d19c51a3c97e50c18b3949e\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\59e9d6232c99a795dfad2d47e2da6cf7\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\59e9d6232c99a795dfad2d47e2da6cf7\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\46f7d17b573b7515a12ff0fbf51740d5\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\46f7d17b573b7515a12ff0fbf51740d5\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\584ba6de600b0b2de543cf4a240cd130\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\584ba6de600b0b2de543cf4a240cd130\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\929b9240d24aeb3703f5157de8604924\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\929b9240d24aeb3703f5157de8604924\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a1ca8c49dded26ddf6804ff0b9818a1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4a1ca8c49dded26ddf6804ff0b9818a1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab5fb6818b6562600503b06553eed6d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fab5fb6818b6562600503b06553eed6d\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7470cfdef8efca626a5314889e13507b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7470cfdef8efca626a5314889e13507b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\36b7d76ad2f58bd1891e547f214a82df\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\36b7d76ad2f58bd1891e547f214a82df\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc8ca5417d853dff86010c14712bbb21\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cc8ca5417d853dff86010c14712bbb21\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8783b0651565a62c004763fdd4ef5315\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8783b0651565a62c004763fdd4ef5315\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\858e732f51e71b3b97441d90b999b156\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\858e732f51e71b3b97441d90b999b156\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1acad1539ee7d97e8d5773c047b9075e\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1acad1539ee7d97e8d5773c047b9075e\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a098678d2e28ff48309f0173c17c1f3\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a098678d2e28ff48309f0173c17c1f3\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8435a3a582374c9c2373119d2b4994f8\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\8435a3a582374c9c2373119d2b4994f8\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3796789e5f2c5605d83340e4c0256fb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3796789e5f2c5605d83340e4c0256fb\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\be4ee609cc36724fa244e825f1cdc4e0\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\be4ee609cc36724fa244e825f1cdc4e0\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cf0d5ed50805ac881e7618033934ff7\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cf0d5ed50805ac881e7618033934ff7\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1cb9b60623db7b1240a92a3d6ec2d51\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1cb9b60623db7b1240a92a3d6ec2d51\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4a7c50bad58b4d141aa2541b246c582\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4a7c50bad58b4d141aa2541b246c582\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a1f436386034f797112f3fe962fdfcf\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3a1f436386034f797112f3fe962fdfcf\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5bcbf758b8ad74828740818432eb0e81\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5bcbf758b8ad74828740818432eb0e81\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf206b7a0bdc7b8c5c735214c771f2a9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf206b7a0bdc7b8c5c735214c771f2a9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f4891602327450c20f7f1089221afcea\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f4891602327450c20f7f1089221afcea\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c33dfd598d7f5cd9493e3c31b54d9b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8c33dfd598d7f5cd9493e3c31b54d9b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ada6cd01769e3db6f41ea87e1cf55807\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ada6cd01769e3db6f41ea87e1cf55807\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4947c8c8f82cbd37fcb873254c78abf3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4947c8c8f82cbd37fcb873254c78abf3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67c46fef93599cc498da70ae63e3709f\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\67c46fef93599cc498da70ae63e3709f\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e25154ae9a84858f82cefea9843a4f4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6e25154ae9a84858f82cefea9843a4f4\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6b87a72c1240a1b5d232cd18ad3ef55\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b6b87a72c1240a1b5d232cd18ad3ef55\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93d40b49572cfb0848e92cb5be0d5e29\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\93d40b49572cfb0848e92cb5be0d5e29\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\141d7b7a374a5c99f9674fc86825e26a\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\141d7b7a374a5c99f9674fc86825e26a\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba42166b23584acd65358876f90402e2\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ba42166b23584acd65358876f90402e2\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\46e619b66ff692f7fbc181ca9734df7c\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\46e619b66ff692f7fbc181ca9734df7c\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e7b5267fb09ce156305a6d31dbe90d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37e7b5267fb09ce156305a6d31dbe90d\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f808fe96c0fb86139ec57aa436721b2b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f808fe96c0fb86139ec57aa436721b2b\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2041c592220dedec16d3b1f3d460e556\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2041c592220dedec16d3b1f3d460e556\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b95ed4432398560d7b8e968b0d281afc\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b95ed4432398560d7b8e968b0d281afc\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6deb02d5036e4c4e473136df26114169\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6deb02d5036e4c4e473136df26114169\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\facceb7182a7be6418b7a3f59afda498\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\facceb7182a7be6418b7a3f59afda498\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\9699702750ae15264150987f34a92d1b\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.soywiz.korlibs.krypto:krypto-android:4.0.10] C:\Users\<USER>\.gradle\caches\8.12\transforms\9699702750ae15264150987f34a92d1b\transformed\krypto-debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3c7d8dec4f524b158a44e92135002e\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af3c7d8dec4f524b158a44e92135002e\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\c49329f623719a3fdcb1405f34aa51c0\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\c49329f623719a3fdcb1405f34aa51c0\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\be2d7c30c5203c92c2b43c4c9442e65c\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\be2d7c30c5203c92c2b43c4c9442e65c\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e1f15c7316e56cadf6817b0da6b71a3\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e1f15c7316e56cadf6817b0da6b71a3\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79346057590700477e9d5e9502cb4282\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79346057590700477e9d5e9502cb4282\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a528f28d8d6cd0b09e9f3d146f151e3\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a528f28d8d6cd0b09e9f3d146f151e3\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e907fae608558eb35db57c99eeb38393\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e907fae608558eb35db57c99eeb38393\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b40160fec95bf60b86fc39e1f91de936\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b40160fec95bf60b86fc39e1f91de936\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb836af334ead3d902e425ea79f1b22c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb836af334ead3d902e425ea79f1b22c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe93e09fded37ed8016f00b0eee8963c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe93e09fded37ed8016f00b0eee8963c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\895e74e841efc17f6531e29c7b3e059e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\895e74e841efc17f6531e29c7b3e059e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a822e87a2b8c36cacf1168fab84c8dbb\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a822e87a2b8c36cacf1168fab84c8dbb\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9e5d83c14b7483c6f10d6619b23fe62\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e9e5d83c14b7483c6f10d6619b23fe62\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ff3186779a9cfd4eabcccbee49b869e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ff3186779a9cfd4eabcccbee49b869e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2077a6705442552801dcf9762a1f3c82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2077a6705442552801dcf9762a1f3c82\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b24fa3a990d7986aa702dc13e3394489\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b24fa3a990d7986aa702dc13e3394489\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ee953b071d97dcad887ccf2700d95c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\44ee953b071d97dcad887ccf2700d95c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1a4ab591a1058cc36e51f7c72c24268\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f1a4ab591a1058cc36e51f7c72c24268\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.50] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f61e53be1b6f3cca8b7efa23c05c2e0\transformed\dagger-lint-aar-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.50] C:\Users\<USER>\.gradle\caches\8.12\transforms\6f61e53be1b6f3cca8b7efa23c05c2e0\transformed\dagger-lint-aar-2.50\AndroidManifest.xml:18:3-42
MERGED from [com.github.samanzamani:PersianDate:1.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9f9c6f64e8ce04ecff2e57d983bf023\transformed\PersianDate-1.3.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.samanzamani:PersianDate:1.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\b9f9c6f64e8ce04ecff2e57d983bf023\transformed\PersianDate-1.3.4\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\New folder\sharen\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:25:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:27:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:28:22-74
provider#androidx.work.impl.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:31:9-37:35
	android:authorities
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:33:13-68
	android:multiprocess
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:36:13-40
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:35:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:37:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:34:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:32:13-69
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:39:9-44:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:43:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:44:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:45:9-51:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:48:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:49:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:50:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:51:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:47:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:46:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:52:9-57:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:55:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:57:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:54:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:53:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:59:9-64:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:62:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:63:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:64:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:61:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:60:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:65:9-75:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:68:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:69:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:70:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:67:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:66:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:71:13-74:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:72:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:72:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:73:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:73:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:76:9-86:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:79:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:80:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:81:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:78:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:77:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:82:13-85:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:83:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:83:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:84:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:84:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:87:9-97:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:90:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:91:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:92:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:89:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:88:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:93:13-96:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:94:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:94:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:95:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:95:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:98:9-107:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:101:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:102:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:103:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:100:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:99:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:104:13-106:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:105:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:105:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:108:9-119:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:111:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:112:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:113:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:110:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:109:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:114:13-118:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:115:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:115:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:116:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:116:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:117:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:117:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:120:9-129:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:123:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:124:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:125:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:122:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:121:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:126:13-128:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:127:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:127:25-95
provider#androidx.startup.InitializationProvider
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\90a69c579eaa8012e79c24e0e8243f66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:13:13-31
	android:authorities
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:11:13-68
	android:exported
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:10:13-67
meta-data#io.github.jan.supabase.gotrue.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.example.sharen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.sharen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
