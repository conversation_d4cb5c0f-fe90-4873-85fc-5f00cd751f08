package com.example.sharen.di;

import com.example.sharen.data.remote.ReportRemoteDataSource;
import com.example.sharen.data.repository.ReportRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReportModule_ProvideReportRepositoryFactory implements Factory<ReportRepository> {
  private final Provider<ReportRemoteDataSource> remoteDataSourceProvider;

  public ReportModule_ProvideReportRepositoryFactory(
      Provider<ReportRemoteDataSource> remoteDataSourceProvider) {
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public ReportRepository get() {
    return provideReportRepository(remoteDataSourceProvider.get());
  }

  public static ReportModule_ProvideReportRepositoryFactory create(
      Provider<ReportRemoteDataSource> remoteDataSourceProvider) {
    return new ReportModule_ProvideReportRepositoryFactory(remoteDataSourceProvider);
  }

  public static ReportRepository provideReportRepository(ReportRemoteDataSource remoteDataSource) {
    return Preconditions.checkNotNullFromProvides(ReportModule.INSTANCE.provideReportRepository(remoteDataSource));
  }
}
