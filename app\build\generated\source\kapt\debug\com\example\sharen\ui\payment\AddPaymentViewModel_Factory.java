package com.example.sharen.ui.payment;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddPaymentViewModel_Factory implements Factory<AddPaymentViewModel> {
  private final Provider<NonExistentClass> paymentRepositoryProvider;

  public AddPaymentViewModel_Factory(Provider<NonExistentClass> paymentRepositoryProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public AddPaymentViewModel get() {
    return newInstance(paymentRepositoryProvider.get());
  }

  public static AddPaymentViewModel_Factory create(
      Provider<NonExistentClass> paymentRepositoryProvider) {
    return new AddPaymentViewModel_Factory(paymentRepositoryProvider);
  }

  public static AddPaymentViewModel newInstance(NonExistentClass paymentRepository) {
    return new AddPaymentViewModel(paymentRepository);
  }
}
