package com.example.sharen.ui.payment;

import com.example.sharen.data.repository.PaymentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddPaymentViewModel_Factory implements Factory<AddPaymentViewModel> {
  private final Provider<PaymentRepository> paymentRepositoryProvider;

  public AddPaymentViewModel_Factory(Provider<PaymentRepository> paymentRepositoryProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public AddPaymentViewModel get() {
    return newInstance(paymentRepositoryProvider.get());
  }

  public static AddPaymentViewModel_Factory create(
      Provider<PaymentRepository> paymentRepositoryProvider) {
    return new AddPaymentViewModel_Factory(paymentRepositoryProvider);
  }

  public static AddPaymentViewModel newInstance(PaymentRepository paymentRepository) {
    return new AddPaymentViewModel(paymentRepository);
  }
}
