<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/iv_customer_avatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/bg_circle_avatar"
            android:contentDescription="@string/customer_avatar"
            android:padding="8dp"
            android:src="@android:drawable/ic_menu_myplaces"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/tv_customer_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tv_debt_amount"
            app:layout_constraintStart_toEndOf="@+id/iv_customer_avatar"
            app:layout_constraintTop_toTopOf="@+id/iv_customer_avatar"
            tools:text="علی محمدی" />

        <TextView
            android:id="@+id/tv_customer_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_customer_name"
            app:layout_constraintStart_toStartOf="@+id/tv_customer_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_customer_name"
            tools:text="۰۹۱۲۳۴۵۶۷۸۹" />

        <TextView
            android:id="@+id/tv_debt_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/error"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_customer_name"
            tools:text="۱,۵۰۰,۰۰۰ تومان" />

        <TextView
            android:id="@+id/tv_last_purchase"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_debt_amount"
            tools:text="آخرین خرید: ۲ روز پیش" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            app:layout_constraintTop_toBottomOf="@+id/iv_customer_avatar">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_call"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/call"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_call"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_message"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/message"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_dialog_email"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_new_invoice"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/new_invoice"
                android:textColor="@color/primary"
                android:textSize="12sp"
                app:icon="@android:drawable/ic_menu_add"
                app:iconSize="16dp"
                app:iconTint="@color/primary" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 