// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPasswordChangeBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnChangePassword;

  @NonNull
  public final TextInputEditText etConfirmPassword;

  @NonNull
  public final TextInputEditText etCurrentPassword;

  @NonNull
  public final TextInputEditText etNewPassword;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilConfirmPassword;

  @NonNull
  public final TextInputLayout tilCurrentPassword;

  @NonNull
  public final TextInputLayout tilNewPassword;

  @NonNull
  public final Toolbar toolbar;

  private ActivityPasswordChangeBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnChangePassword, @NonNull TextInputEditText etConfirmPassword,
      @NonNull TextInputEditText etCurrentPassword, @NonNull TextInputEditText etNewPassword,
      @NonNull ProgressBar progressBar, @NonNull TextInputLayout tilConfirmPassword,
      @NonNull TextInputLayout tilCurrentPassword, @NonNull TextInputLayout tilNewPassword,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnChangePassword = btnChangePassword;
    this.etConfirmPassword = etConfirmPassword;
    this.etCurrentPassword = etCurrentPassword;
    this.etNewPassword = etNewPassword;
    this.progressBar = progressBar;
    this.tilConfirmPassword = tilConfirmPassword;
    this.tilCurrentPassword = tilCurrentPassword;
    this.tilNewPassword = tilNewPassword;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPasswordChangeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPasswordChangeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_password_change, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPasswordChangeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnChangePassword;
      Button btnChangePassword = ViewBindings.findChildViewById(rootView, id);
      if (btnChangePassword == null) {
        break missingId;
      }

      id = R.id.etConfirmPassword;
      TextInputEditText etConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmPassword == null) {
        break missingId;
      }

      id = R.id.etCurrentPassword;
      TextInputEditText etCurrentPassword = ViewBindings.findChildViewById(rootView, id);
      if (etCurrentPassword == null) {
        break missingId;
      }

      id = R.id.etNewPassword;
      TextInputEditText etNewPassword = ViewBindings.findChildViewById(rootView, id);
      if (etNewPassword == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tilConfirmPassword;
      TextInputLayout tilConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilConfirmPassword == null) {
        break missingId;
      }

      id = R.id.tilCurrentPassword;
      TextInputLayout tilCurrentPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilCurrentPassword == null) {
        break missingId;
      }

      id = R.id.tilNewPassword;
      TextInputLayout tilNewPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilNewPassword == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityPasswordChangeBinding((ConstraintLayout) rootView, btnChangePassword,
          etConfirmPassword, etCurrentPassword, etNewPassword, progressBar, tilConfirmPassword,
          tilCurrentPassword, tilNewPassword, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
