<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_customer" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_customer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_customer_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="129" endOffset="35"/></Target><Target id="@+id/iv_customer_avatar" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="25" endOffset="37"/></Target><Target id="@+id/tv_customer_name" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="41" endOffset="36"/></Target><Target id="@+id/tv_customer_phone" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="55" endOffset="38"/></Target><Target id="@+id/tv_debt_amount" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="66" endOffset="42"/></Target><Target id="@+id/tv_last_purchase" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="48"/></Target><Target id="@+id/btn_call" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="86" startOffset="12" endLine="98" endOffset="47"/></Target><Target id="@+id/btn_message" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="100" startOffset="12" endLine="112" endOffset="47"/></Target><Target id="@+id/btn_new_invoice" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="114" startOffset="12" endLine="125" endOffset="47"/></Target></Targets></Layout>