package com.example.sharen.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0002\u0010\u000bJ\u000b\u0010\u0015\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0010\u0010\u0019\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0010JJ\u0010\u001a\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010\u001bJ\u0013\u0010\u001c\u001a\u00020\n2\b\u0010\u001d\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001J\t\u0010 \u001a\u00020\bH\u00d6\u0001R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0015\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u0011\u001a\u0004\b\t\u0010\u0010R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000fR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014\u00a8\u0006!"}, d2 = {"Lcom/example/sharen/data/model/ReportFilter;", "", "startDate", "Ljava/util/Date;", "endDate", "type", "Lcom/example/sharen/data/model/ReportType;", "createdBy", "", "isTemplate", "", "(Ljava/util/Date;Ljava/util/Date;Lcom/example/sharen/data/model/ReportType;Ljava/lang/String;Ljava/lang/Boolean;)V", "getCreatedBy", "()Ljava/lang/String;", "getEndDate", "()Ljava/util/Date;", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getStartDate", "getType", "()Lcom/example/sharen/data/model/ReportType;", "component1", "component2", "component3", "component4", "component5", "copy", "(Ljava/util/Date;Ljava/util/Date;Lcom/example/sharen/data/model/ReportType;Ljava/lang/String;Ljava/lang/Boolean;)Lcom/example/sharen/data/model/ReportFilter;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ReportFilter {
    @org.jetbrains.annotations.Nullable
    private final java.util.Date startDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date endDate = null;
    @org.jetbrains.annotations.Nullable
    private final com.example.sharen.data.model.ReportType type = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String createdBy = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean isTemplate = null;
    
    public ReportFilter(@org.jetbrains.annotations.Nullable
    java.util.Date startDate, @org.jetbrains.annotations.Nullable
    java.util.Date endDate, @org.jetbrains.annotations.Nullable
    com.example.sharen.data.model.ReportType type, @org.jetbrains.annotations.Nullable
    java.lang.String createdBy, @org.jetbrains.annotations.Nullable
    java.lang.Boolean isTemplate) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getStartDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getEndDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.example.sharen.data.model.ReportType getType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCreatedBy() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean isTemplate() {
        return null;
    }
    
    public ReportFilter() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.example.sharen.data.model.ReportType component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.model.ReportFilter copy(@org.jetbrains.annotations.Nullable
    java.util.Date startDate, @org.jetbrains.annotations.Nullable
    java.util.Date endDate, @org.jetbrains.annotations.Nullable
    com.example.sharen.data.model.ReportType type, @org.jetbrains.annotations.Nullable
    java.lang.String createdBy, @org.jetbrains.annotations.Nullable
    java.lang.Boolean isTemplate) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}