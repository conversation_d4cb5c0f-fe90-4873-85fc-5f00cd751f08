<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customer_list" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_customer_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_customer_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="129" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/card_search" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="24" startOffset="8" endLine="59" endOffset="43"/></Target><Target id="@+id/et_search" view="EditText"><Expressions/><location startLine="48" startOffset="16" endLine="57" endOffset="43"/></Target><Target id="@+id/tv_total_customers" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="71" endOffset="35"/></Target><Target id="@+id/rv_customers" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="73" startOffset="8" endLine="85" endOffset="52"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="87" startOffset="8" endLine="95" endOffset="55"/></Target><Target id="@+id/tv_empty_state" view="TextView"><Expressions/><location startLine="97" startOffset="8" endLine="109" endOffset="55"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="113" startOffset="4" endLine="118" endOffset="49"/></Target><Target id="@+id/fab_add_customer" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="120" startOffset="4" endLine="127" endOffset="54"/></Target></Targets></Layout>