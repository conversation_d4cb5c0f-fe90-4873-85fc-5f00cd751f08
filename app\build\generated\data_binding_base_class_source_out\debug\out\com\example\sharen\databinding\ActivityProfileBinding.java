// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProfileBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnChangePassword;

  @NonNull
  public final MaterialButton btnLogout;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final TextInputEditText etName;

  @NonNull
  public final TextInputEditText etPhone;

  @NonNull
  public final CircleImageView ivProfileImage;

  @NonNull
  public final TextInputLayout tilEmail;

  @NonNull
  public final TextInputLayout tilName;

  @NonNull
  public final TextInputLayout tilPhone;

  @NonNull
  public final Toolbar toolbar;

  private ActivityProfileBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnChangePassword, @NonNull MaterialButton btnLogout,
      @NonNull MaterialButton btnSave, @NonNull TextInputEditText etEmail,
      @NonNull TextInputEditText etName, @NonNull TextInputEditText etPhone,
      @NonNull CircleImageView ivProfileImage, @NonNull TextInputLayout tilEmail,
      @NonNull TextInputLayout tilName, @NonNull TextInputLayout tilPhone,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnChangePassword = btnChangePassword;
    this.btnLogout = btnLogout;
    this.btnSave = btnSave;
    this.etEmail = etEmail;
    this.etName = etName;
    this.etPhone = etPhone;
    this.ivProfileImage = ivProfileImage;
    this.tilEmail = tilEmail;
    this.tilName = tilName;
    this.tilPhone = tilPhone;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnChangePassword;
      MaterialButton btnChangePassword = ViewBindings.findChildViewById(rootView, id);
      if (btnChangePassword == null) {
        break missingId;
      }

      id = R.id.btnLogout;
      MaterialButton btnLogout = ViewBindings.findChildViewById(rootView, id);
      if (btnLogout == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.etEmail;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.etName;
      TextInputEditText etName = ViewBindings.findChildViewById(rootView, id);
      if (etName == null) {
        break missingId;
      }

      id = R.id.etPhone;
      TextInputEditText etPhone = ViewBindings.findChildViewById(rootView, id);
      if (etPhone == null) {
        break missingId;
      }

      id = R.id.ivProfileImage;
      CircleImageView ivProfileImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProfileImage == null) {
        break missingId;
      }

      id = R.id.tilEmail;
      TextInputLayout tilEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilEmail == null) {
        break missingId;
      }

      id = R.id.tilName;
      TextInputLayout tilName = ViewBindings.findChildViewById(rootView, id);
      if (tilName == null) {
        break missingId;
      }

      id = R.id.tilPhone;
      TextInputLayout tilPhone = ViewBindings.findChildViewById(rootView, id);
      if (tilPhone == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityProfileBinding((ConstraintLayout) rootView, btnChangePassword, btnLogout,
          btnSave, etEmail, etName, etPhone, ivProfileImage, tilEmail, tilName, tilPhone, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
