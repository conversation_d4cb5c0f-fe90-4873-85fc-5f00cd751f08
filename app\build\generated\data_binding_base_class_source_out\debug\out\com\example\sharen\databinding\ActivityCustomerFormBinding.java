// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCustomerFormBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final TextInputEditText etAddress;

  @NonNull
  public final TextInputEditText etCustomerName;

  @NonNull
  public final TextInputEditText etNotes;

  @NonNull
  public final TextInputEditText etPhone;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilAddress;

  @NonNull
  public final TextInputLayout tilCustomerName;

  @NonNull
  public final TextInputLayout tilNotes;

  @NonNull
  public final TextInputLayout tilPhone;

  @NonNull
  public final Toolbar toolbar;

  private ActivityCustomerFormBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnSave, @NonNull TextInputEditText etAddress,
      @NonNull TextInputEditText etCustomerName, @NonNull TextInputEditText etNotes,
      @NonNull TextInputEditText etPhone, @NonNull ProgressBar progressBar,
      @NonNull TextInputLayout tilAddress, @NonNull TextInputLayout tilCustomerName,
      @NonNull TextInputLayout tilNotes, @NonNull TextInputLayout tilPhone,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnSave = btnSave;
    this.etAddress = etAddress;
    this.etCustomerName = etCustomerName;
    this.etNotes = etNotes;
    this.etPhone = etPhone;
    this.progressBar = progressBar;
    this.tilAddress = tilAddress;
    this.tilCustomerName = tilCustomerName;
    this.tilNotes = tilNotes;
    this.tilPhone = tilPhone;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCustomerFormBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCustomerFormBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_customer_form, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCustomerFormBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.et_address;
      TextInputEditText etAddress = ViewBindings.findChildViewById(rootView, id);
      if (etAddress == null) {
        break missingId;
      }

      id = R.id.et_customer_name;
      TextInputEditText etCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (etCustomerName == null) {
        break missingId;
      }

      id = R.id.et_notes;
      TextInputEditText etNotes = ViewBindings.findChildViewById(rootView, id);
      if (etNotes == null) {
        break missingId;
      }

      id = R.id.et_phone;
      TextInputEditText etPhone = ViewBindings.findChildViewById(rootView, id);
      if (etPhone == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.til_address;
      TextInputLayout tilAddress = ViewBindings.findChildViewById(rootView, id);
      if (tilAddress == null) {
        break missingId;
      }

      id = R.id.til_customer_name;
      TextInputLayout tilCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tilCustomerName == null) {
        break missingId;
      }

      id = R.id.til_notes;
      TextInputLayout tilNotes = ViewBindings.findChildViewById(rootView, id);
      if (tilNotes == null) {
        break missingId;
      }

      id = R.id.til_phone;
      TextInputLayout tilPhone = ViewBindings.findChildViewById(rootView, id);
      if (tilPhone == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityCustomerFormBinding((CoordinatorLayout) rootView, btnSave, etAddress,
          etCustomerName, etNotes, etPhone, progressBar, tilAddress, tilCustomerName, tilNotes,
          tilPhone, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
