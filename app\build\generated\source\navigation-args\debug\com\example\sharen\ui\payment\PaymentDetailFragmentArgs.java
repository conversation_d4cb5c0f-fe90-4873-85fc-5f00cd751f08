package com.example.sharen.ui.payment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.lifecycle.SavedStateHandle;
import androidx.navigation.NavArgs;
import java.lang.IllegalArgumentException;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class PaymentDetailFragmentArgs implements NavArgs {
  private final HashMap arguments = new HashMap();

  private PaymentDetailFragmentArgs() {
  }

  @SuppressWarnings("unchecked")
  private PaymentDetailFragmentArgs(HashMap argumentsMap) {
    this.arguments.putAll(argumentsMap);
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static PaymentDetailFragmentArgs fromBundle(@NonNull Bundle bundle) {
    PaymentDetailFragmentArgs __result = new PaymentDetailFragmentArgs();
    bundle.setClassLoader(PaymentDetailFragmentArgs.class.getClassLoader());
    if (bundle.containsKey("paymentId")) {
      String paymentId;
      paymentId = bundle.getString("paymentId");
      if (paymentId == null) {
        throw new IllegalArgumentException("Argument \"paymentId\" is marked as non-null but was passed a null value.");
      }
      __result.arguments.put("paymentId", paymentId);
    } else {
      throw new IllegalArgumentException("Required argument \"paymentId\" is missing and does not have an android:defaultValue");
    }
    return __result;
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static PaymentDetailFragmentArgs fromSavedStateHandle(
      @NonNull SavedStateHandle savedStateHandle) {
    PaymentDetailFragmentArgs __result = new PaymentDetailFragmentArgs();
    if (savedStateHandle.contains("paymentId")) {
      String paymentId;
      paymentId = savedStateHandle.get("paymentId");
      if (paymentId == null) {
        throw new IllegalArgumentException("Argument \"paymentId\" is marked as non-null but was passed a null value.");
      }
      __result.arguments.put("paymentId", paymentId);
    } else {
      throw new IllegalArgumentException("Required argument \"paymentId\" is missing and does not have an android:defaultValue");
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public String getPaymentId() {
    return (String) arguments.get("paymentId");
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public Bundle toBundle() {
    Bundle __result = new Bundle();
    if (arguments.containsKey("paymentId")) {
      String paymentId = (String) arguments.get("paymentId");
      __result.putString("paymentId", paymentId);
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public SavedStateHandle toSavedStateHandle() {
    SavedStateHandle __result = new SavedStateHandle();
    if (arguments.containsKey("paymentId")) {
      String paymentId = (String) arguments.get("paymentId");
      __result.set("paymentId", paymentId);
    }
    return __result;
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
        return true;
    }
    if (object == null || getClass() != object.getClass()) {
        return false;
    }
    PaymentDetailFragmentArgs that = (PaymentDetailFragmentArgs) object;
    if (arguments.containsKey("paymentId") != that.arguments.containsKey("paymentId")) {
      return false;
    }
    if (getPaymentId() != null ? !getPaymentId().equals(that.getPaymentId()) : that.getPaymentId() != null) {
      return false;
    }
    return true;
  }

  @Override
  public int hashCode() {
    int result = 1;
    result = 31 * result + (getPaymentId() != null ? getPaymentId().hashCode() : 0);
    return result;
  }

  @Override
  public String toString() {
    return "PaymentDetailFragmentArgs{"
        + "paymentId=" + getPaymentId()
        + "}";
  }

  public static final class Builder {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    public Builder(@NonNull PaymentDetailFragmentArgs original) {
      this.arguments.putAll(original.arguments);
    }

    @SuppressWarnings("unchecked")
    public Builder(@NonNull String paymentId) {
      if (paymentId == null) {
        throw new IllegalArgumentException("Argument \"paymentId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("paymentId", paymentId);
    }

    @NonNull
    public PaymentDetailFragmentArgs build() {
      PaymentDetailFragmentArgs result = new PaymentDetailFragmentArgs(arguments);
      return result;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public Builder setPaymentId(@NonNull String paymentId) {
      if (paymentId == null) {
        throw new IllegalArgumentException("Argument \"paymentId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("paymentId", paymentId);
      return this;
    }

    @SuppressWarnings({"unchecked","GetterOnBuilder"})
    @NonNull
    public String getPaymentId() {
      return (String) arguments.get("paymentId");
    }
  }
}
