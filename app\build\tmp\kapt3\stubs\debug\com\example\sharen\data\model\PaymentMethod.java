package com.example.sharen.data.model;

/**
 * روش‌های پرداخت
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/example/sharen/data/model/PaymentMethod;", "", "(<PERSON><PERSON><PERSON>/lang/String;I)V", "CASH", "CREDIT_CARD", "BANK_TRANSFER", "CHECK", "INSTALLMENT", "app_debug"})
public enum PaymentMethod {
    /*public static final*/ CASH /* = new CASH() */,
    /*public static final*/ CREDIT_CARD /* = new CREDIT_CARD() */,
    /*public static final*/ BANK_TRANSFER /* = new BANK_TRANSFER() */,
    /*public static final*/ CHECK /* = new CHECK() */,
    /*public static final*/ INSTALLMENT /* = new INSTALLMENT() */;
    
    PaymentMethod() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.data.model.PaymentMethod> getEntries() {
        return null;
    }
}