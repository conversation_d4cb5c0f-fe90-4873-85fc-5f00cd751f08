// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityInvoiceDetailsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button btnAddPayment;

  @NonNull
  public final Button btnApproveInvoice;

  @NonNull
  public final Button btnEditInvoice;

  @NonNull
  public final Button btnPrintInvoice;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvInvoiceItems;

  @NonNull
  public final RecyclerView rvPayments;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvDiscount;

  @NonNull
  public final TextView tvFinalAmount;

  @NonNull
  public final TextView tvInvoiceDate;

  @NonNull
  public final TextView tvInvoiceNumber;

  @NonNull
  public final TextView tvNoItems;

  @NonNull
  public final TextView tvNoPayments;

  @NonNull
  public final TextView tvPaidAmount;

  @NonNull
  public final TextView tvPaymentType;

  @NonNull
  public final TextView tvRemainingAmount;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTax;

  @NonNull
  public final TextView tvTotalAmount;

  private ActivityInvoiceDetailsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button btnAddPayment, @NonNull Button btnApproveInvoice,
      @NonNull Button btnEditInvoice, @NonNull Button btnPrintInvoice,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView rvInvoiceItems,
      @NonNull RecyclerView rvPayments, @NonNull Toolbar toolbar, @NonNull TextView tvCustomerName,
      @NonNull TextView tvDiscount, @NonNull TextView tvFinalAmount,
      @NonNull TextView tvInvoiceDate, @NonNull TextView tvInvoiceNumber,
      @NonNull TextView tvNoItems, @NonNull TextView tvNoPayments, @NonNull TextView tvPaidAmount,
      @NonNull TextView tvPaymentType, @NonNull TextView tvRemainingAmount,
      @NonNull TextView tvStatus, @NonNull TextView tvTax, @NonNull TextView tvTotalAmount) {
    this.rootView = rootView;
    this.btnAddPayment = btnAddPayment;
    this.btnApproveInvoice = btnApproveInvoice;
    this.btnEditInvoice = btnEditInvoice;
    this.btnPrintInvoice = btnPrintInvoice;
    this.progressBar = progressBar;
    this.rvInvoiceItems = rvInvoiceItems;
    this.rvPayments = rvPayments;
    this.toolbar = toolbar;
    this.tvCustomerName = tvCustomerName;
    this.tvDiscount = tvDiscount;
    this.tvFinalAmount = tvFinalAmount;
    this.tvInvoiceDate = tvInvoiceDate;
    this.tvInvoiceNumber = tvInvoiceNumber;
    this.tvNoItems = tvNoItems;
    this.tvNoPayments = tvNoPayments;
    this.tvPaidAmount = tvPaidAmount;
    this.tvPaymentType = tvPaymentType;
    this.tvRemainingAmount = tvRemainingAmount;
    this.tvStatus = tvStatus;
    this.tvTax = tvTax;
    this.tvTotalAmount = tvTotalAmount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityInvoiceDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityInvoiceDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_invoice_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityInvoiceDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddPayment;
      Button btnAddPayment = ViewBindings.findChildViewById(rootView, id);
      if (btnAddPayment == null) {
        break missingId;
      }

      id = R.id.btnApproveInvoice;
      Button btnApproveInvoice = ViewBindings.findChildViewById(rootView, id);
      if (btnApproveInvoice == null) {
        break missingId;
      }

      id = R.id.btnEditInvoice;
      Button btnEditInvoice = ViewBindings.findChildViewById(rootView, id);
      if (btnEditInvoice == null) {
        break missingId;
      }

      id = R.id.btnPrintInvoice;
      Button btnPrintInvoice = ViewBindings.findChildViewById(rootView, id);
      if (btnPrintInvoice == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rvInvoiceItems;
      RecyclerView rvInvoiceItems = ViewBindings.findChildViewById(rootView, id);
      if (rvInvoiceItems == null) {
        break missingId;
      }

      id = R.id.rvPayments;
      RecyclerView rvPayments = ViewBindings.findChildViewById(rootView, id);
      if (rvPayments == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCustomerName;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tvDiscount;
      TextView tvDiscount = ViewBindings.findChildViewById(rootView, id);
      if (tvDiscount == null) {
        break missingId;
      }

      id = R.id.tvFinalAmount;
      TextView tvFinalAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvFinalAmount == null) {
        break missingId;
      }

      id = R.id.tvInvoiceDate;
      TextView tvInvoiceDate = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceDate == null) {
        break missingId;
      }

      id = R.id.tvInvoiceNumber;
      TextView tvInvoiceNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceNumber == null) {
        break missingId;
      }

      id = R.id.tvNoItems;
      TextView tvNoItems = ViewBindings.findChildViewById(rootView, id);
      if (tvNoItems == null) {
        break missingId;
      }

      id = R.id.tvNoPayments;
      TextView tvNoPayments = ViewBindings.findChildViewById(rootView, id);
      if (tvNoPayments == null) {
        break missingId;
      }

      id = R.id.tvPaidAmount;
      TextView tvPaidAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvPaidAmount == null) {
        break missingId;
      }

      id = R.id.tvPaymentType;
      TextView tvPaymentType = ViewBindings.findChildViewById(rootView, id);
      if (tvPaymentType == null) {
        break missingId;
      }

      id = R.id.tvRemainingAmount;
      TextView tvRemainingAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvRemainingAmount == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvTax;
      TextView tvTax = ViewBindings.findChildViewById(rootView, id);
      if (tvTax == null) {
        break missingId;
      }

      id = R.id.tvTotalAmount;
      TextView tvTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalAmount == null) {
        break missingId;
      }

      return new ActivityInvoiceDetailsBinding((CoordinatorLayout) rootView, btnAddPayment,
          btnApproveInvoice, btnEditInvoice, btnPrintInvoice, progressBar, rvInvoiceItems,
          rvPayments, toolbar, tvCustomerName, tvDiscount, tvFinalAmount, tvInvoiceDate,
          tvInvoiceNumber, tvNoItems, tvNoPayments, tvPaidAmount, tvPaymentType, tvRemainingAmount,
          tvStatus, tvTax, tvTotalAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
