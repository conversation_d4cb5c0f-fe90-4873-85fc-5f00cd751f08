package com.example.sharen.ui.payment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.navigation.NavDirections;
import com.example.sharen.R;
import java.lang.IllegalArgumentException;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class PaymentListFragmentDirections {
  private PaymentListFragmentDirections() {
  }

  @NonNull
  public static ActionPaymentListFragmentToPaymentAddEditFragment actionPaymentListFragmentToPaymentAddEditFragment(
      @NonNull String customerId, @NonNull String orderId) {
    return new ActionPaymentListFragmentToPaymentAddEditFragment(customerId, orderId);
  }

  @NonNull
  public static ActionPaymentListFragmentToPaymentDetailFragment actionPaymentListFragmentToPaymentDetailFragment(
      @NonNull String paymentId) {
    return new ActionPaymentListFragmentToPaymentDetailFragment(paymentId);
  }

  public static class ActionPaymentListFragmentToPaymentAddEditFragment implements NavDirections {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    private ActionPaymentListFragmentToPaymentAddEditFragment(@NonNull String customerId,
        @NonNull String orderId) {
      if (customerId == null) {
        throw new IllegalArgumentException("Argument \"customerId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("customerId", customerId);
      if (orderId == null) {
        throw new IllegalArgumentException("Argument \"orderId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("orderId", orderId);
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionPaymentListFragmentToPaymentAddEditFragment setPaymentId(
        @Nullable String paymentId) {
      this.arguments.put("paymentId", paymentId);
      return this;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionPaymentListFragmentToPaymentAddEditFragment setCustomerId(
        @NonNull String customerId) {
      if (customerId == null) {
        throw new IllegalArgumentException("Argument \"customerId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("customerId", customerId);
      return this;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionPaymentListFragmentToPaymentAddEditFragment setOrderId(@NonNull String orderId) {
      if (orderId == null) {
        throw new IllegalArgumentException("Argument \"orderId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("orderId", orderId);
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    @NonNull
    public Bundle getArguments() {
      Bundle __result = new Bundle();
      if (arguments.containsKey("paymentId")) {
        String paymentId = (String) arguments.get("paymentId");
        __result.putString("paymentId", paymentId);
      } else {
        __result.putString("paymentId", null);
      }
      if (arguments.containsKey("customerId")) {
        String customerId = (String) arguments.get("customerId");
        __result.putString("customerId", customerId);
      }
      if (arguments.containsKey("orderId")) {
        String orderId = (String) arguments.get("orderId");
        __result.putString("orderId", orderId);
      }
      return __result;
    }

    @Override
    public int getActionId() {
      return R.id.action_paymentListFragment_to_paymentAddEditFragment;
    }

    @SuppressWarnings("unchecked")
    @Nullable
    public String getPaymentId() {
      return (String) arguments.get("paymentId");
    }

    @SuppressWarnings("unchecked")
    @NonNull
    public String getCustomerId() {
      return (String) arguments.get("customerId");
    }

    @SuppressWarnings("unchecked")
    @NonNull
    public String getOrderId() {
      return (String) arguments.get("orderId");
    }

    @Override
    public boolean equals(Object object) {
      if (this == object) {
          return true;
      }
      if (object == null || getClass() != object.getClass()) {
          return false;
      }
      ActionPaymentListFragmentToPaymentAddEditFragment that = (ActionPaymentListFragmentToPaymentAddEditFragment) object;
      if (arguments.containsKey("paymentId") != that.arguments.containsKey("paymentId")) {
        return false;
      }
      if (getPaymentId() != null ? !getPaymentId().equals(that.getPaymentId()) : that.getPaymentId() != null) {
        return false;
      }
      if (arguments.containsKey("customerId") != that.arguments.containsKey("customerId")) {
        return false;
      }
      if (getCustomerId() != null ? !getCustomerId().equals(that.getCustomerId()) : that.getCustomerId() != null) {
        return false;
      }
      if (arguments.containsKey("orderId") != that.arguments.containsKey("orderId")) {
        return false;
      }
      if (getOrderId() != null ? !getOrderId().equals(that.getOrderId()) : that.getOrderId() != null) {
        return false;
      }
      if (getActionId() != that.getActionId()) {
        return false;
      }
      return true;
    }

    @Override
    public int hashCode() {
      int result = 1;
      result = 31 * result + (getPaymentId() != null ? getPaymentId().hashCode() : 0);
      result = 31 * result + (getCustomerId() != null ? getCustomerId().hashCode() : 0);
      result = 31 * result + (getOrderId() != null ? getOrderId().hashCode() : 0);
      result = 31 * result + getActionId();
      return result;
    }

    @Override
    public String toString() {
      return "ActionPaymentListFragmentToPaymentAddEditFragment(actionId=" + getActionId() + "){"
          + "paymentId=" + getPaymentId()
          + ", customerId=" + getCustomerId()
          + ", orderId=" + getOrderId()
          + "}";
    }
  }

  public static class ActionPaymentListFragmentToPaymentDetailFragment implements NavDirections {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    private ActionPaymentListFragmentToPaymentDetailFragment(@NonNull String paymentId) {
      if (paymentId == null) {
        throw new IllegalArgumentException("Argument \"paymentId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("paymentId", paymentId);
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionPaymentListFragmentToPaymentDetailFragment setPaymentId(
        @NonNull String paymentId) {
      if (paymentId == null) {
        throw new IllegalArgumentException("Argument \"paymentId\" is marked as non-null but was passed a null value.");
      }
      this.arguments.put("paymentId", paymentId);
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    @NonNull
    public Bundle getArguments() {
      Bundle __result = new Bundle();
      if (arguments.containsKey("paymentId")) {
        String paymentId = (String) arguments.get("paymentId");
        __result.putString("paymentId", paymentId);
      }
      return __result;
    }

    @Override
    public int getActionId() {
      return R.id.action_paymentListFragment_to_paymentDetailFragment;
    }

    @SuppressWarnings("unchecked")
    @NonNull
    public String getPaymentId() {
      return (String) arguments.get("paymentId");
    }

    @Override
    public boolean equals(Object object) {
      if (this == object) {
          return true;
      }
      if (object == null || getClass() != object.getClass()) {
          return false;
      }
      ActionPaymentListFragmentToPaymentDetailFragment that = (ActionPaymentListFragmentToPaymentDetailFragment) object;
      if (arguments.containsKey("paymentId") != that.arguments.containsKey("paymentId")) {
        return false;
      }
      if (getPaymentId() != null ? !getPaymentId().equals(that.getPaymentId()) : that.getPaymentId() != null) {
        return false;
      }
      if (getActionId() != that.getActionId()) {
        return false;
      }
      return true;
    }

    @Override
    public int hashCode() {
      int result = 1;
      result = 31 * result + (getPaymentId() != null ? getPaymentId().hashCode() : 0);
      result = 31 * result + getActionId();
      return result;
    }

    @Override
    public String toString() {
      return "ActionPaymentListFragmentToPaymentDetailFragment(actionId=" + getActionId() + "){"
          + "paymentId=" + getPaymentId()
          + "}";
    }
  }
}
