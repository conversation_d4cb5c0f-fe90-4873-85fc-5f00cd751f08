package com.example.sharen.ui.invoice;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J(\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020\u00072\b\u0010 \u001a\u0004\u0018\u00010\u00072\u0006\u0010!\u001a\u00020\"J\u0006\u0010#\u001a\u00020\u001dJ\u0006\u0010$\u001a\u00020\u000eJ\u0006\u0010%\u001a\u00020\u001dJ\u0006\u0010&\u001a\u00020\u000eJ\u000e\u0010\'\u001a\u00020\u001d2\u0006\u0010(\u001a\u00020\u0007J\u000e\u0010)\u001a\u00020\u001d2\u0006\u0010*\u001a\u00020+J\u001c\u0010,\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00112\u0006\u0010-\u001a\u00020\"2\u0006\u0010.\u001a\u00020\"J\u0016\u0010/\u001a\u00020\u001d2\u0006\u00100\u001a\u00020\u00072\u0006\u00101\u001a\u00020\u0007J\u001e\u00102\u001a\u00020\u001d2\u0006\u0010*\u001a\u00020+2\u0006\u00103\u001a\u00020+2\u0006\u00104\u001a\u00020\"R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0010\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0018R\u000e\u0010\u0019\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0013\u00a8\u00065"}, d2 = {"Lcom/example/sharen/ui/invoice/SalesInvoiceViewModel;", "Landroidx/lifecycle/ViewModel;", "invoiceRepository", "error/NonExistentClass", "(Lerror/NonExistentClass;)V", "_errorMessage", "Landroidx/lifecycle/MutableLiveData;", "", "_invoice", "Lcom/example/sharen/data/model/Invoice;", "_invoiceItems", "", "Lcom/example/sharen/data/model/InvoiceItem;", "_isLoading", "", "_saveStatus", "errorMessage", "Landroidx/lifecycle/LiveData;", "getErrorMessage", "()Landroidx/lifecycle/LiveData;", "invoice", "getInvoice", "invoiceItems", "getInvoiceItems", "Lerror/NonExistentClass;", "isDirty", "isEditMode", "isLoading", "addInvoiceItem", "", "productId", "productName", "productCode", "unitPrice", "", "calculateTotals", "hasUnsavedChanges", "initNewInvoice", "isCustomerSelected", "loadInvoice", "invoiceId", "removeInvoiceItem", "position", "", "saveInvoice", "discount", "tax", "setCustomer", "customerId", "customerName", "updateInvoiceItem", "newQuantity", "newDiscount", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class SalesInvoiceViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final error.NonExistentClass invoiceRepository = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<com.example.sharen.data.model.Invoice> _invoice = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<com.example.sharen.data.model.Invoice> invoice = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.example.sharen.data.model.InvoiceItem>> _invoiceItems = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.util.List<com.example.sharen.data.model.InvoiceItem>> invoiceItems = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _saveStatus = null;
    private boolean isDirty = false;
    private boolean isEditMode = false;
    
    @javax.inject.Inject
    public SalesInvoiceViewModel(@org.jetbrains.annotations.NotNull
    error.NonExistentClass invoiceRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<com.example.sharen.data.model.Invoice> getInvoice() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.util.List<com.example.sharen.data.model.InvoiceItem>> getInvoiceItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.String> getErrorMessage() {
        return null;
    }
    
    /**
     * مقداردهی اولیه فاکتور جدید
     */
    public final void initNewInvoice() {
    }
    
    /**
     * بارگذاری فاکتور موجود برای ویرایش
     */
    public final void loadInvoice(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId) {
    }
    
    /**
     * تنظیم مشتری فاکتور
     */
    public final void setCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName) {
    }
    
    /**
     * افزودن آیتم به فاکتور
     */
    public final void addInvoiceItem(@org.jetbrains.annotations.NotNull
    java.lang.String productId, @org.jetbrains.annotations.NotNull
    java.lang.String productName, @org.jetbrains.annotations.Nullable
    java.lang.String productCode, long unitPrice) {
    }
    
    /**
     * حذف آیتم از فاکتور
     */
    public final void removeInvoiceItem(int position) {
    }
    
    /**
     * به‌روزرسانی آیتم فاکتور
     */
    public final void updateInvoiceItem(int position, int newQuantity, long newDiscount) {
    }
    
    /**
     * محاسبه مجدد مبالغ فاکتور
     */
    public final void calculateTotals() {
    }
    
    /**
     * ذخیره فاکتور
     */
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.Boolean> saveInvoice(long discount, long tax) {
        return null;
    }
    
    /**
     * آیا مشتری انتخاب شده است؟
     */
    public final boolean isCustomerSelected() {
        return false;
    }
    
    /**
     * آیا تغییرات ذخیره نشده وجود دارد؟
     */
    public final boolean hasUnsavedChanges() {
        return false;
    }
}