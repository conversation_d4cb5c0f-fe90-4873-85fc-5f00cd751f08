package com.example.sharen.domain.model;

/**
 * وضعیت موجودی
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\r\u00a8\u0006\u000e"}, d2 = {"Lcom/example/sharen/domain/model/StockStatus;", "", "displayName", "", "colorRes", "", "(Ljava/lang/String;ILjava/lang/String;I)V", "getColorRes", "()I", "getDisplayName", "()Ljava/lang/String;", "IN_STOCK", "LOW_STOCK", "OUT_OF_STOCK", "app_debug"})
public enum StockStatus {
    /*public static final*/ IN_STOCK /* = new IN_STOCK(null, 0) */,
    /*public static final*/ LOW_STOCK /* = new LOW_STOCK(null, 0) */,
    /*public static final*/ OUT_OF_STOCK /* = new OUT_OF_STOCK(null, 0) */;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String displayName = null;
    private final int colorRes = 0;
    
    StockStatus(java.lang.String displayName, int colorRes) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final int getColorRes() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.domain.model.StockStatus> getEntries() {
        return null;
    }
}