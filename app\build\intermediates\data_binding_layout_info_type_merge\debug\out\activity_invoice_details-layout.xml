<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_invoice_details" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_invoice_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_invoice_details_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="497" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="55"/></Target><Target id="@+id/tvInvoiceNumber" view="TextView"><Expressions/><location startLine="73" startOffset="24" endLine="78" endOffset="51"/></Target><Target id="@+id/tvCustomerName" view="TextView"><Expressions/><location startLine="94" startOffset="24" endLine="99" endOffset="53"/></Target><Target id="@+id/tvInvoiceDate" view="TextView"><Expressions/><location startLine="115" startOffset="24" endLine="120" endOffset="53"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="136" startOffset="24" endLine="143" endOffset="53"/></Target><Target id="@+id/tvPaymentType" view="TextView"><Expressions/><location startLine="159" startOffset="24" endLine="164" endOffset="47"/></Target><Target id="@+id/rvInvoiceItems" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="198" startOffset="20" endLine="203" endOffset="71"/></Target><Target id="@+id/tvNoItems" view="TextView"><Expressions/><location startLine="205" startOffset="20" endLine="213" endOffset="51"/></Target><Target id="@+id/tvTotalAmount" view="TextView"><Expressions/><location startLine="258" startOffset="24" endLine="264" endOffset="56"/></Target><Target id="@+id/tvDiscount" view="TextView"><Expressions/><location startLine="280" startOffset="24" endLine="286" endOffset="55"/></Target><Target id="@+id/tvTax" view="TextView"><Expressions/><location startLine="302" startOffset="24" endLine="308" endOffset="55"/></Target><Target id="@+id/tvFinalAmount" view="TextView"><Expressions/><location startLine="330" startOffset="24" endLine="338" endOffset="56"/></Target><Target id="@+id/tvPaidAmount" view="TextView"><Expressions/><location startLine="354" startOffset="24" endLine="361" endOffset="56"/></Target><Target id="@+id/tvRemainingAmount" view="TextView"><Expressions/><location startLine="377" startOffset="24" endLine="384" endOffset="50"/></Target><Target id="@+id/rvPayments" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="418" startOffset="20" endLine="423" endOffset="63"/></Target><Target id="@+id/tvNoPayments" view="TextView"><Expressions/><location startLine="425" startOffset="20" endLine="433" endOffset="51"/></Target><Target id="@+id/btnAddPayment" view="Button"><Expressions/><location startLine="444" startOffset="16" endLine="450" endOffset="56"/></Target><Target id="@+id/btnApproveInvoice" view="Button"><Expressions/><location startLine="452" startOffset="16" endLine="458" endOffset="60"/></Target><Target id="@+id/btnEditInvoice" view="Button"><Expressions/><location startLine="468" startOffset="16" endLine="474" endOffset="57"/></Target><Target id="@+id/btnPrintInvoice" view="Button"><Expressions/><location startLine="476" startOffset="16" endLine="482" endOffset="58"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="490" startOffset="4" endLine="495" endOffset="35"/></Target></Targets></Layout>