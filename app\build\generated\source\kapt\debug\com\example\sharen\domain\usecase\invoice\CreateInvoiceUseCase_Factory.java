package com.example.sharen.domain.usecase.invoice;

import com.example.sharen.domain.repository.CustomerRepository;
import com.example.sharen.domain.repository.InvoiceRepository;
import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CreateInvoiceUseCase_Factory implements Factory<CreateInvoiceUseCase> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<CustomerRepository> customerRepositoryProvider;

  public CreateInvoiceUseCase_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<CustomerRepository> customerRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public CreateInvoiceUseCase get() {
    return newInstance(invoiceRepositoryProvider.get(), productRepositoryProvider.get(), customerRepositoryProvider.get());
  }

  public static CreateInvoiceUseCase_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new CreateInvoiceUseCase_Factory(invoiceRepositoryProvider, productRepositoryProvider, customerRepositoryProvider);
  }

  public static CreateInvoiceUseCase newInstance(InvoiceRepository invoiceRepository,
      ProductRepository productRepository, CustomerRepository customerRepository) {
    return new CreateInvoiceUseCase(invoiceRepository, productRepository, customerRepository);
  }
}
