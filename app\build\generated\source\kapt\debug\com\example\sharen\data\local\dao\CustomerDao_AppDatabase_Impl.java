package com.example.sharen.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharen.data.local.entity.CustomerEntity;
import com.example.sharen.data.local.entity.UserEntity;
import com.example.sharen.data.local.relation.CustomerWithUser;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class CustomerDao_AppDatabase_Impl implements CustomerDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<CustomerEntity> __insertionAdapterOfCustomerEntity;

  private final EntityDeletionOrUpdateAdapter<CustomerEntity> __deletionAdapterOfCustomerEntity;

  private final EntityDeletionOrUpdateAdapter<CustomerEntity> __updateAdapterOfCustomerEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteCustomer;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCustomerCredit;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCustomerDebt;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllCustomers;

  public CustomerDao_AppDatabase_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfCustomerEntity = new EntityInsertionAdapter<CustomerEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `customers` (`id`,`userId`,`name`,`phone`,`address`,`notes`,`creditLimit`,`totalPurchases`,`totalPayments`,`totalDebt`,`lastPurchaseDate`,`lastPaymentDate`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CustomerEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUserId());
        }
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhone());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAddress());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getNotes());
        }
        statement.bindLong(7, entity.getCreditLimit());
        statement.bindLong(8, entity.getTotalPurchases());
        statement.bindLong(9, entity.getTotalPayments());
        statement.bindLong(10, entity.getTotalDebt());
        if (entity.getLastPurchaseDate() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getLastPurchaseDate());
        }
        if (entity.getLastPaymentDate() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getLastPaymentDate());
        }
        statement.bindLong(13, entity.getCreatedAt());
        statement.bindLong(14, entity.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfCustomerEntity = new EntityDeletionOrUpdateAdapter<CustomerEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `customers` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CustomerEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfCustomerEntity = new EntityDeletionOrUpdateAdapter<CustomerEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `customers` SET `id` = ?,`userId` = ?,`name` = ?,`phone` = ?,`address` = ?,`notes` = ?,`creditLimit` = ?,`totalPurchases` = ?,`totalPayments` = ?,`totalDebt` = ?,`lastPurchaseDate` = ?,`lastPaymentDate` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final CustomerEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUserId());
        }
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhone());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAddress());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getNotes());
        }
        statement.bindLong(7, entity.getCreditLimit());
        statement.bindLong(8, entity.getTotalPurchases());
        statement.bindLong(9, entity.getTotalPayments());
        statement.bindLong(10, entity.getTotalDebt());
        if (entity.getLastPurchaseDate() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getLastPurchaseDate());
        }
        if (entity.getLastPaymentDate() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getLastPaymentDate());
        }
        statement.bindLong(13, entity.getCreatedAt());
        statement.bindLong(14, entity.getUpdatedAt());
        if (entity.getId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteCustomer = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM customers WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCustomerCredit = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE customers SET creditLimit = ?, updatedAt = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCustomerDebt = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE customers SET totalDebt = ?, updatedAt = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllCustomers = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM customers";
        return _query;
      }
    };
  }

  @Override
  public Object insertCustomer(final CustomerEntity customer,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCustomerEntity.insert(customer);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertCustomers(final List<CustomerEntity> customers,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfCustomerEntity.insert(customers);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCustomer(final CustomerEntity customer,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfCustomerEntity.handle(customer);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCustomer(final CustomerEntity customer,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfCustomerEntity.handle(customer);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteCustomer(final String customerId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteCustomer.acquire();
        int _argIndex = 1;
        if (customerId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, customerId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteCustomer.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCustomerCredit(final String customerId, final long creditLimit,
      final long updatedAt, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCustomerCredit.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, creditLimit);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updatedAt);
        _argIndex = 3;
        if (customerId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, customerId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCustomerCredit.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateCustomerDebt(final String customerId, final long debtAmount,
      final long updatedAt, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCustomerDebt.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, debtAmount);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, updatedAt);
        _argIndex = 3;
        if (customerId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, customerId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateCustomerDebt.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllCustomers(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllCustomers.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllCustomers.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getCustomerById(final String customerId,
      final Continuation<? super CustomerEntity> $completion) {
    final String _sql = "SELECT * FROM customers WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CustomerEntity>() {
      @Override
      @Nullable
      public CustomerEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final CustomerEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCustomerByUserId(final String userId,
      final Continuation<? super CustomerEntity> $completion) {
    final String _sql = "SELECT * FROM customers WHERE userId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (userId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, userId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<CustomerEntity>() {
      @Override
      @Nullable
      public CustomerEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final CustomerEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<CustomerEntity>> getAllCustomers() {
    final String _sql = "SELECT * FROM customers ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getCustomersBySeller(final String sellerId) {
    final String _sql = "SELECT * FROM customers WHERE userId = ? ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (sellerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, sellerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> searchCustomers(final String query) {
    final String _sql = "SELECT * FROM customers WHERE name LIKE '%' || ? || '%' OR phone LIKE '%' || ? || '%' OR address LIKE '%' || ? || '%' ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 2;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    _argIndex = 3;
    if (query == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, query);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getCustomersWithDebt() {
    final String _sql = "SELECT * FROM customers WHERE totalDebt > 0 ORDER BY totalDebt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getCustomersWithoutDebt() {
    final String _sql = "SELECT * FROM customers WHERE totalDebt = 0 ORDER BY name ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getCustomersWithLowCredit() {
    final String _sql = "SELECT * FROM customers WHERE (creditLimit - totalDebt) < (creditLimit * 0.2) ORDER BY (creditLimit - totalDebt) ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getCustomersByDateRange(final long startDate,
      final long endDate) {
    final String _sql = "SELECT * FROM customers WHERE createdAt BETWEEN ? AND ? ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getActiveCustomers(final long cutoffDate) {
    final String _sql = "SELECT * FROM customers WHERE lastPurchaseDate > ? ORDER BY lastPurchaseDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, cutoffDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getInactiveCustomers(final long cutoffDate) {
    final String _sql = "SELECT * FROM customers WHERE lastPurchaseDate < ? OR lastPurchaseDate IS NULL ORDER BY lastPurchaseDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, cutoffDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerEntity>> getTopCustomers(final int limit) {
    final String _sql = "SELECT * FROM customers WHERE totalPurchases > 0 ORDER BY totalPurchases DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"customers"}, new Callable<List<CustomerEntity>>() {
      @Override
      @NonNull
      public List<CustomerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
          final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
          final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
          final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<CustomerEntity> _result = new ArrayList<CustomerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final long _tmpCreditLimit;
            _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
            final long _tmpTotalPurchases;
            _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
            final long _tmpTotalPayments;
            _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final Long _tmpLastPurchaseDate;
            if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
              _tmpLastPurchaseDate = null;
            } else {
              _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
            }
            final Long _tmpLastPaymentDate;
            if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
              _tmpLastPaymentDate = null;
            } else {
              _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<CustomerWithUser> getCustomerWithUser(final String customerId) {
    final String _sql = "SELECT * FROM customers WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    return CoroutinesRoom.createFlow(__db, true, new String[] {"users",
        "customers"}, new Callable<CustomerWithUser>() {
      @Override
      @NonNull
      public CustomerWithUser call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
            final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
            final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
            final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
            final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
            final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
            final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
            final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
            final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
            final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
            final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
            final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
            final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
            final ArrayMap<String, UserEntity> _collectionUser = new ArrayMap<String, UserEntity>();
            while (_cursor.moveToNext()) {
              final String _tmpKey;
              if (_cursor.isNull(_cursorIndexOfUserId)) {
                _tmpKey = null;
              } else {
                _tmpKey = _cursor.getString(_cursorIndexOfUserId);
              }
              if (_tmpKey != null) {
                _collectionUser.put(_tmpKey, null);
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipusersAscomExampleSharenDataLocalEntityUserEntity(_collectionUser);
            final CustomerWithUser _result;
            if (_cursor.moveToFirst()) {
              final CustomerEntity _tmpCustomer;
              final String _tmpId;
              if (_cursor.isNull(_cursorIndexOfId)) {
                _tmpId = null;
              } else {
                _tmpId = _cursor.getString(_cursorIndexOfId);
              }
              final String _tmpUserId;
              if (_cursor.isNull(_cursorIndexOfUserId)) {
                _tmpUserId = null;
              } else {
                _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
              }
              final String _tmpName;
              if (_cursor.isNull(_cursorIndexOfName)) {
                _tmpName = null;
              } else {
                _tmpName = _cursor.getString(_cursorIndexOfName);
              }
              final String _tmpPhone;
              if (_cursor.isNull(_cursorIndexOfPhone)) {
                _tmpPhone = null;
              } else {
                _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
              }
              final String _tmpAddress;
              if (_cursor.isNull(_cursorIndexOfAddress)) {
                _tmpAddress = null;
              } else {
                _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
              }
              final String _tmpNotes;
              if (_cursor.isNull(_cursorIndexOfNotes)) {
                _tmpNotes = null;
              } else {
                _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
              }
              final long _tmpCreditLimit;
              _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
              final long _tmpTotalPurchases;
              _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
              final long _tmpTotalPayments;
              _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
              final long _tmpTotalDebt;
              _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
              final Long _tmpLastPurchaseDate;
              if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
                _tmpLastPurchaseDate = null;
              } else {
                _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
              }
              final Long _tmpLastPaymentDate;
              if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
                _tmpLastPaymentDate = null;
              } else {
                _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
              }
              final long _tmpCreatedAt;
              _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
              final long _tmpUpdatedAt;
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
              _tmpCustomer = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
              final UserEntity _tmpUser;
              final String _tmpKey_1;
              if (_cursor.isNull(_cursorIndexOfUserId)) {
                _tmpKey_1 = null;
              } else {
                _tmpKey_1 = _cursor.getString(_cursorIndexOfUserId);
              }
              if (_tmpKey_1 != null) {
                _tmpUser = _collectionUser.get(_tmpKey_1);
              } else {
                _tmpUser = null;
              }
              _result = new CustomerWithUser(_tmpCustomer,_tmpUser);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerWithUser>> getCustomersWithUsers() {
    final String _sql = "SELECT * FROM customers";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, true, new String[] {"users",
        "customers"}, new Callable<List<CustomerWithUser>>() {
      @Override
      @NonNull
      public List<CustomerWithUser> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
            final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
            final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
            final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
            final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
            final int _cursorIndexOfCreditLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "creditLimit");
            final int _cursorIndexOfTotalPurchases = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPurchases");
            final int _cursorIndexOfTotalPayments = CursorUtil.getColumnIndexOrThrow(_cursor, "totalPayments");
            final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
            final int _cursorIndexOfLastPurchaseDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPurchaseDate");
            final int _cursorIndexOfLastPaymentDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastPaymentDate");
            final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
            final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
            final ArrayMap<String, UserEntity> _collectionUser = new ArrayMap<String, UserEntity>();
            while (_cursor.moveToNext()) {
              final String _tmpKey;
              if (_cursor.isNull(_cursorIndexOfUserId)) {
                _tmpKey = null;
              } else {
                _tmpKey = _cursor.getString(_cursorIndexOfUserId);
              }
              if (_tmpKey != null) {
                _collectionUser.put(_tmpKey, null);
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipusersAscomExampleSharenDataLocalEntityUserEntity(_collectionUser);
            final List<CustomerWithUser> _result = new ArrayList<CustomerWithUser>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final CustomerWithUser _item;
              final CustomerEntity _tmpCustomer;
              final String _tmpId;
              if (_cursor.isNull(_cursorIndexOfId)) {
                _tmpId = null;
              } else {
                _tmpId = _cursor.getString(_cursorIndexOfId);
              }
              final String _tmpUserId;
              if (_cursor.isNull(_cursorIndexOfUserId)) {
                _tmpUserId = null;
              } else {
                _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
              }
              final String _tmpName;
              if (_cursor.isNull(_cursorIndexOfName)) {
                _tmpName = null;
              } else {
                _tmpName = _cursor.getString(_cursorIndexOfName);
              }
              final String _tmpPhone;
              if (_cursor.isNull(_cursorIndexOfPhone)) {
                _tmpPhone = null;
              } else {
                _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
              }
              final String _tmpAddress;
              if (_cursor.isNull(_cursorIndexOfAddress)) {
                _tmpAddress = null;
              } else {
                _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
              }
              final String _tmpNotes;
              if (_cursor.isNull(_cursorIndexOfNotes)) {
                _tmpNotes = null;
              } else {
                _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
              }
              final long _tmpCreditLimit;
              _tmpCreditLimit = _cursor.getLong(_cursorIndexOfCreditLimit);
              final long _tmpTotalPurchases;
              _tmpTotalPurchases = _cursor.getLong(_cursorIndexOfTotalPurchases);
              final long _tmpTotalPayments;
              _tmpTotalPayments = _cursor.getLong(_cursorIndexOfTotalPayments);
              final long _tmpTotalDebt;
              _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
              final Long _tmpLastPurchaseDate;
              if (_cursor.isNull(_cursorIndexOfLastPurchaseDate)) {
                _tmpLastPurchaseDate = null;
              } else {
                _tmpLastPurchaseDate = _cursor.getLong(_cursorIndexOfLastPurchaseDate);
              }
              final Long _tmpLastPaymentDate;
              if (_cursor.isNull(_cursorIndexOfLastPaymentDate)) {
                _tmpLastPaymentDate = null;
              } else {
                _tmpLastPaymentDate = _cursor.getLong(_cursorIndexOfLastPaymentDate);
              }
              final long _tmpCreatedAt;
              _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
              final long _tmpUpdatedAt;
              _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
              _tmpCustomer = new CustomerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpNotes,_tmpCreditLimit,_tmpTotalPurchases,_tmpTotalPayments,_tmpTotalDebt,_tmpLastPurchaseDate,_tmpLastPaymentDate,_tmpCreatedAt,_tmpUpdatedAt);
              final UserEntity _tmpUser;
              final String _tmpKey_1;
              if (_cursor.isNull(_cursorIndexOfUserId)) {
                _tmpKey_1 = null;
              } else {
                _tmpKey_1 = _cursor.getString(_cursorIndexOfUserId);
              }
              if (_tmpKey_1 != null) {
                _tmpUser = _collectionUser.get(_tmpKey_1);
              } else {
                _tmpUser = null;
              }
              _item = new CustomerWithUser(_tmpCustomer,_tmpUser);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<CustomerPurchaseEntity>> getCustomerPurchaseHistory(final String customerId) {
    final String _sql = "SELECT i.id as invoiceId, i.finalAmount as amount, i.createdAt as date, i.status as status FROM invoices i WHERE i.customerId = ? ORDER BY i.createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (customerId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, customerId);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"invoices"}, new Callable<List<CustomerPurchaseEntity>>() {
      @Override
      @NonNull
      public List<CustomerPurchaseEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfInvoiceId = 0;
          final int _cursorIndexOfAmount = 1;
          final int _cursorIndexOfDate = 2;
          final int _cursorIndexOfStatus = 3;
          final List<CustomerPurchaseEntity> _result = new ArrayList<CustomerPurchaseEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CustomerPurchaseEntity _item;
            final String _tmpInvoiceId;
            if (_cursor.isNull(_cursorIndexOfInvoiceId)) {
              _tmpInvoiceId = null;
            } else {
              _tmpInvoiceId = _cursor.getString(_cursorIndexOfInvoiceId);
            }
            final long _tmpAmount;
            _tmpAmount = _cursor.getLong(_cursorIndexOfAmount);
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            _item = new CustomerPurchaseEntity(_tmpInvoiceId,_tmpAmount,_tmpDate,_tmpStatus);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshipusersAscomExampleSharenDataLocalEntityUserEntity(
      @NonNull final ArrayMap<String, UserEntity> _map) {
    final Set<String> __mapKeySet = _map.keySet();
    if (__mapKeySet.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchArrayMap(_map, false, (map) -> {
        __fetchRelationshipusersAscomExampleSharenDataLocalEntityUserEntity(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`name`,`email`,`phone`,`role`,`isApproved`,`is_current`,`createdAt`,`updatedAt`,`imageUrl`,`referrerId`,`referrerCode` FROM `users` WHERE `id` IN (");
    final int _inputSize = __mapKeySet == null ? 1 : __mapKeySet.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    if (__mapKeySet == null) {
      _stmt.bindNull(_argIndex);
    } else {
      for (String _item : __mapKeySet) {
        if (_item == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _item);
        }
        _argIndex++;
      }
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "id");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfName = 1;
      final int _cursorIndexOfEmail = 2;
      final int _cursorIndexOfPhone = 3;
      final int _cursorIndexOfRole = 4;
      final int _cursorIndexOfIsApproved = 5;
      final int _cursorIndexOfIsCurrent = 6;
      final int _cursorIndexOfCreatedAt = 7;
      final int _cursorIndexOfUpdatedAt = 8;
      final int _cursorIndexOfImageUrl = 9;
      final int _cursorIndexOfReferrerId = 10;
      final int _cursorIndexOfReferrerCode = 11;
      while (_cursor.moveToNext()) {
        final String _tmpKey;
        if (_cursor.isNull(_itemKeyIndex)) {
          _tmpKey = null;
        } else {
          _tmpKey = _cursor.getString(_itemKeyIndex);
        }
        if (_tmpKey != null) {
          if (_map.containsKey(_tmpKey)) {
            final UserEntity _item_1;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpRole;
            if (_cursor.isNull(_cursorIndexOfRole)) {
              _tmpRole = null;
            } else {
              _tmpRole = _cursor.getString(_cursorIndexOfRole);
            }
            final boolean _tmpIsApproved;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsApproved);
            _tmpIsApproved = _tmp != 0;
            final boolean _tmpIsCurrent;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCurrent);
            _tmpIsCurrent = _tmp_1 != 0;
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpReferrerId;
            if (_cursor.isNull(_cursorIndexOfReferrerId)) {
              _tmpReferrerId = null;
            } else {
              _tmpReferrerId = _cursor.getString(_cursorIndexOfReferrerId);
            }
            final String _tmpReferrerCode;
            if (_cursor.isNull(_cursorIndexOfReferrerCode)) {
              _tmpReferrerCode = null;
            } else {
              _tmpReferrerCode = _cursor.getString(_cursorIndexOfReferrerCode);
            }
            _item_1 = new UserEntity(_tmpId,_tmpName,_tmpEmail,_tmpPhone,_tmpRole,_tmpIsApproved,_tmpIsCurrent,_tmpCreatedAt,_tmpUpdatedAt,_tmpImageUrl,_tmpReferrerId,_tmpReferrerCode);
            _map.put(_tmpKey, _item_1);
          }
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
