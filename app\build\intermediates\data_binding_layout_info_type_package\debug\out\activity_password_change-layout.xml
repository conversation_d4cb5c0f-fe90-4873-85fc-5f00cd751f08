<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_password_change" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_password_change.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_password_change_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="17" endOffset="43"/></Target><Target id="@+id/tilCurrentPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="32" startOffset="12" endLine="49" endOffset="67"/></Target><Target id="@+id/etCurrentPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="16" endLine="48" endOffset="54"/></Target><Target id="@+id/tilNewPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="51" startOffset="12" endLine="68" endOffset="67"/></Target><Target id="@+id/etNewPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="63" startOffset="16" endLine="67" endOffset="54"/></Target><Target id="@+id/tilConfirmPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="70" startOffset="12" endLine="87" endOffset="67"/></Target><Target id="@+id/etConfirmPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="82" startOffset="16" endLine="86" endOffset="54"/></Target><Target id="@+id/btnChangePassword" view="Button"><Expressions/><location startLine="89" startOffset="12" endLine="97" endOffset="79"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="99" startOffset="12" endLine="108" endOffset="78"/></Target></Targets></Layout>