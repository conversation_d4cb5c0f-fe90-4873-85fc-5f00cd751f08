package com.example.sharen.ui.product;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductListViewModel_Factory implements Factory<ProductListViewModel> {
  private final Provider<NonExistentClass> productRepositoryProvider;

  public ProductListViewModel_Factory(Provider<NonExistentClass> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public ProductListViewModel get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static ProductListViewModel_Factory create(
      Provider<NonExistentClass> productRepositoryProvider) {
    return new ProductListViewModel_Factory(productRepositoryProvider);
  }

  public static ProductListViewModel newInstance(NonExistentClass productRepository) {
    return new ProductListViewModel(productRepository);
  }
}
