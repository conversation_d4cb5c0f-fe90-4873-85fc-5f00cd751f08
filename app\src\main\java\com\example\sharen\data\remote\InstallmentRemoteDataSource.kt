package com.example.sharen.data.remote

import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InstallmentRemoteDataSource @Inject constructor(
    private val apiService: SupabaseApiService
) {
    suspend fun createInstallment(installment: Installment): Result<Installment> = runCatching {
        apiService.createInstallment(installment).body() ?: throw Exception("Failed to create installment")
    }
    
    suspend fun updateInstallment(installment: Installment): Result<Installment> = runCatching {
        apiService.updateInstallment(installment.id, installment).body() ?: throw Exception("Failed to update installment")
    }
    
    suspend fun deleteInstallment(installmentId: String): Result<Unit> = runCatching {
        apiService.deleteInstallment(installmentId).body() ?: throw Exception("Failed to delete installment")
    }
    
    suspend fun getInstallment(installmentId: String): Result<Installment> = runCatching {
        apiService.getInstallment(installmentId).body() ?: throw Exception("Installment not found")
    }
    
    fun getAllInstallments(): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            emit(response.body() ?: emptyList())
        } else {
            throw Exception("Failed to fetch installments")
        }
    }
    
    fun getInstallmentsByCustomer(customerId: String): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            val installments = response.body() ?: emptyList()
            emit(installments.filter { it.customerId == customerId })
        } else {
            throw Exception("Failed to fetch customer installments")
        }
    }
    
    fun getInstallmentsByDateRange(startDate: Date, endDate: Date): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            val installments = response.body() ?: emptyList()
            emit(installments.filter { 
                it.dueDate in startDate..endDate 
            })
        } else {
            throw Exception("Failed to fetch installments by date range")
        }
    }
    
    fun getInstallmentsByStatus(status: InstallmentStatus): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            val installments = response.body() ?: emptyList()
            emit(installments.filter { it.status == status })
        } else {
            throw Exception("Failed to fetch installments by status")
        }
    }
    
    suspend fun payInstallment(installmentId: String, amount: Double): Result<Installment> = runCatching {
        val installment = getInstallment(installmentId).getOrThrow()
        val updatedInstallment = installment.copy(
            paidAmount = installment.paidAmount + amount,
            status = if (installment.paidAmount + amount >= installment.totalAmount) {
                InstallmentStatus.PAID
            } else {
                InstallmentStatus.PARTIALLY_PAID
            }
        )
        updateInstallment(updatedInstallment).getOrThrow()
    }
    
    suspend fun getUpcomingInstallments(customerId: String): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            val installments = response.body() ?: emptyList()
            val now = Date()
            emit(installments.filter { 
                it.customerId == customerId && 
                it.dueDate > now && 
                it.status != InstallmentStatus.PAID 
            })
        } else {
            throw Exception("Failed to fetch upcoming installments")
        }
    }
    
    suspend fun getOverdueInstallments(): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            val installments = response.body() ?: emptyList()
            val now = Date()
            emit(installments.filter { 
                it.dueDate < now && 
                it.status != InstallmentStatus.PAID 
            })
        } else {
            throw Exception("Failed to fetch overdue installments")
        }
    }
    
    suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>> = runCatching {
        val installments = getInstallmentsByDateRange(startDate, endDate).collect { installmentList ->
            mapOf(
                "totalAmount" to installmentList.sumOf { it.totalAmount },
                "totalPaid" to installmentList.sumOf { it.paidAmount },
                "totalRemaining" to installmentList.sumOf { it.totalAmount - it.paidAmount },
                "totalCount" to installmentList.size,
                "paidCount" to installmentList.count { it.status == InstallmentStatus.PAID },
                "partiallyPaidCount" to installmentList.count { it.status == InstallmentStatus.PARTIALLY_PAID },
                "unpaidCount" to installmentList.count { it.status == InstallmentStatus.UNPAID }
            )
        }
        installments
    }
    
    suspend fun calculateRemainingAmount(installmentId: String): Result<Double> = runCatching {
        val installment = getInstallment(installmentId).getOrThrow()
        installment.totalAmount - installment.paidAmount
    }
    
    suspend fun getCustomerInstallmentHistory(customerId: String): Flow<List<Installment>> = flow {
        val response = apiService.getInstallments()
        if (response.isSuccessful) {
            val installments = response.body() ?: emptyList()
            emit(installments.filter { it.customerId == customerId }
                .sortedByDescending { it.dueDate })
        } else {
            throw Exception("Failed to fetch customer installment history")
        }
    }
    
    suspend fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        interestRate: Double
    ): Result<List<Installment>> = runCatching {
        val installmentAmount = totalAmount / numberOfInstallments
        val installments = mutableListOf<Installment>()
        
        for (i in 0 until numberOfInstallments) {
            val dueDate = Date(startDate.time + (i * 30L * 24 * 60 * 60 * 1000)) // 30 days between installments
            val installment = Installment(
                id = "", // Will be set by the server
                customerId = "", // Will be set when creating the installment
                totalAmount = installmentAmount,
                paidAmount = 0.0,
                dueDate = dueDate,
                status = InstallmentStatus.UNPAID,
                interestRate = interestRate,
                notes = "Installment ${i + 1} of $numberOfInstallments"
            )
            installments.add(installment)
        }
        
        installments
    }
} 