package com.example.sharen.ui.payment;

import com.example.sharen.data.repository.PaymentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PaymentListViewModel_Factory implements Factory<PaymentListViewModel> {
  private final Provider<PaymentRepository> paymentRepositoryProvider;

  public PaymentListViewModel_Factory(Provider<PaymentRepository> paymentRepositoryProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public PaymentListViewModel get() {
    return newInstance(paymentRepositoryProvider.get());
  }

  public static PaymentListViewModel_Factory create(
      Provider<PaymentRepository> paymentRepositoryProvider) {
    return new PaymentListViewModel_Factory(paymentRepositoryProvider);
  }

  public static PaymentListViewModel newInstance(PaymentRepository paymentRepository) {
    return new PaymentListViewModel(paymentRepository);
  }
}
