package com.example.sharen.ui.payment;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PaymentListViewModel_Factory implements Factory<PaymentListViewModel> {
  private final Provider<NonExistentClass> paymentRepositoryProvider;

  public PaymentListViewModel_Factory(Provider<NonExistentClass> paymentRepositoryProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public PaymentListViewModel get() {
    return newInstance(paymentRepositoryProvider.get());
  }

  public static PaymentListViewModel_Factory create(
      Provider<NonExistentClass> paymentRepositoryProvider) {
    return new PaymentListViewModel_Factory(paymentRepositoryProvider);
  }

  public static PaymentListViewModel newInstance(NonExistentClass paymentRepository) {
    return new PaymentListViewModel(paymentRepository);
  }
}
