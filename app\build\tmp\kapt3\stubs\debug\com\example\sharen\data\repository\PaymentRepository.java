package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\t\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rJ*\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\u000f\u001a\u00020\tH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u0019\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\tH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0011J*\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\b2\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\rJ\u0014\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00170\u0016H&J*\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\b2\u0006\u0010\u001a\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001b\u0010\rJ*\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\rJ\u0016\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u00162\u0006\u0010\u001f\u001a\u00020\u000bJ>\u0010 \u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010!0\b2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020#H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b%\u0010&J\u0012\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00170\u0016J\u001a\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00170\u00162\u0006\u0010\u001a\u001a\u00020\u000bJ$\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00170\u00162\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020#H&J\u001a\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00170\u00162\u0006\u0010+\u001a\u00020\u000bJ\u001c\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00170\u00162\u0006\u0010-\u001a\u00020.H&J2\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00190\b2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020#H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b0\u0010&J\u0019\u00101\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\tH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0011J2\u00102\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u00103\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u00105J\u0019\u00106\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\tH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0011R\u0012\u0010\u0003\u001a\u00020\u0004X\u00a6\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0005\u0010\u0006\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00067"}, d2 = {"Lcom/example/sharen/data/repository/PaymentRepository;", "", "()V", "paymentDao", "Lcom/example/sharen/data/local/dao/PaymentDao;", "getPaymentDao", "()Lcom/example/sharen/data/local/dao/PaymentDao;", "confirmPayment", "Lkotlin/Result;", "Lcom/example/sharen/data/model/Payment;", "paymentId", "", "confirmPayment-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPayment", "payment", "createPayment-gIAlu-s", "(Lcom/example/sharen/data/model/Payment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePayment", "", "deletePayment-gIAlu-s", "getAllPayments", "Lkotlinx/coroutines/flow/Flow;", "", "getCustomerTotalPayments", "", "customerId", "getCustomerTotalPayments-gIAlu-s", "getPayment", "getPayment-gIAlu-s", "getPaymentById", "id", "getPaymentStatistics", "", "startDate", "Ljava/util/Date;", "endDate", "getPaymentStatistics-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPayments", "getPaymentsByCustomerId", "getPaymentsByDateRange", "getPaymentsByOrderId", "orderId", "getPaymentsByStatus", "status", "Lcom/example/sharen/data/model/PaymentStatus;", "getTotalPaymentsByDateRange", "getTotalPaymentsByDateRange-0E7RQCE", "insertPayment", "rejectPayment", "reason", "rejectPayment-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePayment", "app_debug"})
public abstract class PaymentRepository {
    
    public PaymentRepository() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.PaymentDao getPaymentDao();
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPayments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Payment> getPaymentById(@org.jetbrains.annotations.NotNull
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByCustomerId(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByOrderId(@org.jetbrains.annotations.NotNull
    java.lang.String orderId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertPayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updatePayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deletePayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getAllPayments();
    
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate);
    
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentStatus status);
}