<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.notification.NotificationActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/notifications"
        app:titleTextColor="@color/white">

        <Button
            android:id="@+id/btnClearAll"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:text="@string/clear_all"
            android:textColor="@color/white" />
    </androidx.appcompat.widget.Toolbar>

    <com.google.android.material.chip.ChipGroup
        android:id="@+id/chipGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:singleSelection="true">

        <com.google.android.material.chip.Chip
            android:id="@+id/chipAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="@string/all" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chipSystem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/system" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chipInvoice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/invoices" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chipPayment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/payments" />

        <com.google.android.material.chip.Chip
            android:id="@+id/chipInstallment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/installments" />
    </com.google.android.material.chip.ChipGroup>

    <LinearLayout
        android:id="@+id/statsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/light_gray"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/chipGroup">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/unread_notifications"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvUnreadCount"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/circle_background"
            android:backgroundTint="@color/red"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            tools:text="5" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statsLayout"
        tools:listitem="@layout/item_notification" />

    <TextView
        android:id="@+id/tvEmptyState"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="32dp"
        android:text="@string/no_notifications"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statsLayout" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/statsLayout" />

</androidx.constraintlayout.widget.ConstraintLayout> 