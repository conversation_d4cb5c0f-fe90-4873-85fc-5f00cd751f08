package com.example.sharen.presentation.viewmodel;

import com.example.sharen.domain.usecase.customer.AddCustomerUseCase;
import com.example.sharen.domain.usecase.customer.DeleteCustomerUseCase;
import com.example.sharen.domain.usecase.customer.GetAllCustomersUseCase;
import com.example.sharen.domain.usecase.customer.GetCustomerByIdUseCase;
import com.example.sharen.domain.usecase.customer.GetDebtorCustomersUseCase;
import com.example.sharen.domain.usecase.customer.SearchCustomersUseCase;
import com.example.sharen.domain.usecase.customer.UpdateCustomerCreditUseCase;
import com.example.sharen.domain.usecase.customer.UpdateCustomerUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerViewModel_Factory implements Factory<CustomerViewModel> {
  private final Provider<GetAllCustomersUseCase> getAllCustomersUseCaseProvider;

  private final Provider<GetCustomerByIdUseCase> getCustomerByIdUseCaseProvider;

  private final Provider<SearchCustomersUseCase> searchCustomersUseCaseProvider;

  private final Provider<AddCustomerUseCase> addCustomerUseCaseProvider;

  private final Provider<UpdateCustomerUseCase> updateCustomerUseCaseProvider;

  private final Provider<DeleteCustomerUseCase> deleteCustomerUseCaseProvider;

  private final Provider<GetDebtorCustomersUseCase> getDebtorCustomersUseCaseProvider;

  private final Provider<UpdateCustomerCreditUseCase> updateCustomerCreditUseCaseProvider;

  public CustomerViewModel_Factory(Provider<GetAllCustomersUseCase> getAllCustomersUseCaseProvider,
      Provider<GetCustomerByIdUseCase> getCustomerByIdUseCaseProvider,
      Provider<SearchCustomersUseCase> searchCustomersUseCaseProvider,
      Provider<AddCustomerUseCase> addCustomerUseCaseProvider,
      Provider<UpdateCustomerUseCase> updateCustomerUseCaseProvider,
      Provider<DeleteCustomerUseCase> deleteCustomerUseCaseProvider,
      Provider<GetDebtorCustomersUseCase> getDebtorCustomersUseCaseProvider,
      Provider<UpdateCustomerCreditUseCase> updateCustomerCreditUseCaseProvider) {
    this.getAllCustomersUseCaseProvider = getAllCustomersUseCaseProvider;
    this.getCustomerByIdUseCaseProvider = getCustomerByIdUseCaseProvider;
    this.searchCustomersUseCaseProvider = searchCustomersUseCaseProvider;
    this.addCustomerUseCaseProvider = addCustomerUseCaseProvider;
    this.updateCustomerUseCaseProvider = updateCustomerUseCaseProvider;
    this.deleteCustomerUseCaseProvider = deleteCustomerUseCaseProvider;
    this.getDebtorCustomersUseCaseProvider = getDebtorCustomersUseCaseProvider;
    this.updateCustomerCreditUseCaseProvider = updateCustomerCreditUseCaseProvider;
  }

  @Override
  public CustomerViewModel get() {
    return newInstance(getAllCustomersUseCaseProvider.get(), getCustomerByIdUseCaseProvider.get(), searchCustomersUseCaseProvider.get(), addCustomerUseCaseProvider.get(), updateCustomerUseCaseProvider.get(), deleteCustomerUseCaseProvider.get(), getDebtorCustomersUseCaseProvider.get(), updateCustomerCreditUseCaseProvider.get());
  }

  public static CustomerViewModel_Factory create(
      Provider<GetAllCustomersUseCase> getAllCustomersUseCaseProvider,
      Provider<GetCustomerByIdUseCase> getCustomerByIdUseCaseProvider,
      Provider<SearchCustomersUseCase> searchCustomersUseCaseProvider,
      Provider<AddCustomerUseCase> addCustomerUseCaseProvider,
      Provider<UpdateCustomerUseCase> updateCustomerUseCaseProvider,
      Provider<DeleteCustomerUseCase> deleteCustomerUseCaseProvider,
      Provider<GetDebtorCustomersUseCase> getDebtorCustomersUseCaseProvider,
      Provider<UpdateCustomerCreditUseCase> updateCustomerCreditUseCaseProvider) {
    return new CustomerViewModel_Factory(getAllCustomersUseCaseProvider, getCustomerByIdUseCaseProvider, searchCustomersUseCaseProvider, addCustomerUseCaseProvider, updateCustomerUseCaseProvider, deleteCustomerUseCaseProvider, getDebtorCustomersUseCaseProvider, updateCustomerCreditUseCaseProvider);
  }

  public static CustomerViewModel newInstance(GetAllCustomersUseCase getAllCustomersUseCase,
      GetCustomerByIdUseCase getCustomerByIdUseCase, SearchCustomersUseCase searchCustomersUseCase,
      AddCustomerUseCase addCustomerUseCase, UpdateCustomerUseCase updateCustomerUseCase,
      DeleteCustomerUseCase deleteCustomerUseCase,
      GetDebtorCustomersUseCase getDebtorCustomersUseCase,
      UpdateCustomerCreditUseCase updateCustomerCreditUseCase) {
    return new CustomerViewModel(getAllCustomersUseCase, getCustomerByIdUseCase, searchCustomersUseCase, addCustomerUseCase, updateCustomerUseCase, deleteCustomerUseCase, getDebtorCustomersUseCase, updateCustomerCreditUseCase);
  }
}
