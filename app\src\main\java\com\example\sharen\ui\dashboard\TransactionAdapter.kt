package com.example.sharen.ui.dashboard

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.sharen.R
import com.example.sharen.data.model.Transaction
import com.example.sharen.data.model.TransactionStatus
import com.example.sharen.databinding.ItemTransactionBinding
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

class TransactionAdapter(
    private val onItemClick: (Transaction) -> Unit
) : ListAdapter<Transaction, TransactionAdapter.TransactionViewHolder>(TransactionDiffCallback()) {

    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val binding = ItemTransactionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        val transaction = getItem(position)
        holder.bind(transaction)
    }

    inner class TransactionViewHolder(
        private val binding: ItemTransactionBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onItemClick(getItem(position))
                }
            }
        }

        fun bind(transaction: Transaction) {
            binding.tvTransactionNumber.text = transaction.invoiceNumber
            binding.tvCustomerName.text = transaction.customerName
            binding.tvTransactionDate.text = dateFormatter.format(transaction.date)
            binding.tvTransactionAmount.text = 
                "${numberFormatter.format(transaction.amount)} تومان"

            // Set transaction status text and background
            val context = binding.root.context
            when (transaction.status) {
                TransactionStatus.PAID -> {
                    binding.tvTransactionStatus.text = context.getString(R.string.transaction_status_paid)
                    binding.tvTransactionStatus.setBackgroundResource(R.drawable.bg_status_paid)
                }
                TransactionStatus.PENDING -> {
                    binding.tvTransactionStatus.text = context.getString(R.string.transaction_status_pending)
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.warning)
                }
                TransactionStatus.PARTIAL -> {
                    binding.tvTransactionStatus.text = context.getString(R.string.transaction_status_partial)
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.info)
                }
                TransactionStatus.CANCELLED -> {
                    binding.tvTransactionStatus.text = context.getString(R.string.transaction_status_cancelled)
                    binding.tvTransactionStatus.backgroundTintList = 
                        ContextCompat.getColorStateList(context, R.color.error)
                }
            }
        }
    }

    class TransactionDiffCallback : DiffUtil.ItemCallback<Transaction>() {
        override fun areItemsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem == newItem
        }
    }
} 