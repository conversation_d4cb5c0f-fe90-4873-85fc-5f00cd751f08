package com.example.sharen.core.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\u0010\b\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0003\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0004\u001a\u00020\u0005*\u00020\u0002\u001a\u0016\u0010\u0006\u001a\u00020\u0005\"\u0004\b\u0000\u0010\u0007*\b\u0012\u0004\u0012\u0002H\u00070\b\u001a\n\u0010\t\u001a\u00020\u0005*\u00020\n\u001a\n\u0010\u000b\u001a\u00020\u0005*\u00020\n\u001a\n\u0010\f\u001a\u00020\u0005*\u00020\u0002\u001a\u0012\u0010\r\u001a\u00020\u0001*\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\n\u001a\u0012\u0010\r\u001a\u00020\u0001*\u00020\u00102\u0006\u0010\u000f\u001a\u00020\n\u001a\u001e\u0010\u0011\u001a\b\u0012\u0004\u0012\u0002H\u00070\b\"\u0004\b\u0000\u0010\u0007*\n\u0012\u0004\u0012\u0002H\u0007\u0018\u00010\b\u001a\u001e\u0010\u0012\u001a\u0004\u0018\u0001H\u0007\"\u0006\b\u0000\u0010\u0007\u0018\u0001*\u0004\u0018\u00010\u0013H\u0086\b\u00a2\u0006\u0002\u0010\u0014\u001a\n\u0010\u0015\u001a\u00020\n*\u00020\u0016\u001a\n\u0010\u0015\u001a\u00020\n*\u00020\u0017\u001a\n\u0010\u0015\u001a\u00020\n*\u00020\u0018\u001a\n\u0010\u0019\u001a\u00020\u001a*\u00020\u0018\u001a\n\u0010\u001b\u001a\u00020\n*\u00020\n\u001a\n\u0010\u001c\u001a\u00020\n*\u00020\u0016\u001a\n\u0010\u001c\u001a\u00020\n*\u00020\u0018\u001a\n\u0010\u001d\u001a\u00020\n*\u00020\u001a\u001a\n\u0010\u001e\u001a\u00020\n*\u00020\u001a\u001a\n\u0010\u001f\u001a\u00020\n*\u00020\n\u001a\n\u0010 \u001a\u00020\n*\u00020\u001a\u001a\n\u0010!\u001a\u00020\u0018*\u00020\u001a\u001a\u001c\u0010\"\u001a\u00020\u0001*\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\n2\b\b\u0002\u0010#\u001a\u00020\u0017\u001a\u001c\u0010\"\u001a\u00020\u0001*\u00020\u00102\u0006\u0010\u000f\u001a\u00020\n2\b\b\u0002\u0010#\u001a\u00020\u0017\u001a\n\u0010$\u001a\u00020\u0001*\u00020\u0002\u00a8\u0006%"}, d2 = {"gone", "", "Landroid/view/View;", "invisible", "isGone", "", "isNotNullOrEmpty", "T", "", "isValidEmail", "", "isValidPhone", "isVisible", "longToast", "Landroid/content/Context;", "message", "Landroidx/fragment/app/Fragment;", "orEmpty", "safeCast", "", "(Ljava/lang/Object;)Ljava/lang/Object;", "toCurrency", "", "", "", "toDate", "Ljava/util/Date;", "toEnglishDigits", "toFormattedNumber", "toPersianDate", "toPersianDateTime", "toPersianDigits", "toTime", "toTimestamp", "toast", "duration", "visible", "app_debug"})
public final class ExtensionsKt {
    
    /**
     * Extension Functions برای راحتی کار
     */
    public static final void visible(@org.jetbrains.annotations.NotNull
    android.view.View $this$visible) {
    }
    
    public static final void invisible(@org.jetbrains.annotations.NotNull
    android.view.View $this$invisible) {
    }
    
    public static final void gone(@org.jetbrains.annotations.NotNull
    android.view.View $this$gone) {
    }
    
    public static final boolean isVisible(@org.jetbrains.annotations.NotNull
    android.view.View $this$isVisible) {
        return false;
    }
    
    public static final boolean isGone(@org.jetbrains.annotations.NotNull
    android.view.View $this$isGone) {
        return false;
    }
    
    public static final void toast(@org.jetbrains.annotations.NotNull
    android.content.Context $this$toast, @org.jetbrains.annotations.NotNull
    java.lang.String message, int duration) {
    }
    
    public static final void longToast(@org.jetbrains.annotations.NotNull
    android.content.Context $this$longToast, @org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    public static final void toast(@org.jetbrains.annotations.NotNull
    androidx.fragment.app.Fragment $this$toast, @org.jetbrains.annotations.NotNull
    java.lang.String message, int duration) {
    }
    
    public static final void longToast(@org.jetbrains.annotations.NotNull
    androidx.fragment.app.Fragment $this$longToast, @org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toCurrency(long $this$toCurrency) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toCurrency(double $this$toCurrency) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toCurrency(int $this$toCurrency) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toFormattedNumber(long $this$toFormattedNumber) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toFormattedNumber(double $this$toFormattedNumber) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toPersianDate(@org.jetbrains.annotations.NotNull
    java.util.Date $this$toPersianDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toPersianDateTime(@org.jetbrains.annotations.NotNull
    java.util.Date $this$toPersianDateTime) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toTime(@org.jetbrains.annotations.NotNull
    java.util.Date $this$toTime) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.util.Date toDate(long $this$toDate) {
        return null;
    }
    
    public static final long toTimestamp(@org.jetbrains.annotations.NotNull
    java.util.Date $this$toTimestamp) {
        return 0L;
    }
    
    public static final boolean isValidEmail(@org.jetbrains.annotations.NotNull
    java.lang.String $this$isValidEmail) {
        return false;
    }
    
    public static final boolean isValidPhone(@org.jetbrains.annotations.NotNull
    java.lang.String $this$isValidPhone) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toEnglishDigits(@org.jetbrains.annotations.NotNull
    java.lang.String $this$toEnglishDigits) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String toPersianDigits(@org.jetbrains.annotations.NotNull
    java.lang.String $this$toPersianDigits) {
        return null;
    }
    
    public static final <T extends java.lang.Object>boolean isNotNullOrEmpty(@org.jetbrains.annotations.NotNull
    java.util.List<? extends T> $this$isNotNullOrEmpty) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final <T extends java.lang.Object>java.util.List<T> orEmpty(@org.jetbrains.annotations.Nullable
    java.util.List<? extends T> $this$orEmpty) {
        return null;
    }
}