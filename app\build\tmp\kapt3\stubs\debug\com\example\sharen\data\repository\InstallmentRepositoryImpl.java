package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0096@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ*\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0010\u0010\u0011J*\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\r2\u0006\u0010\t\u001a\u00020\nH\u0096@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0014\u0010\u000bJ7\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00162\u0006\u0010\u0017\u001a\u00020\b2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u0019H\u0096@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001dJ\u0014\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001fH\u0016J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001f2\u0006\u0010!\u001a\u00020\nH\u0016J,\u0010\"\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\r2\u0006\u0010\t\u001a\u00020\nH\u0096@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b#\u0010\u000bJ-\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\b0%2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010&\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\'J\u001c\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001f2\u0006\u0010!\u001a\u00020\nH\u0016J$\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001f2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010&\u001a\u00020\u001bH\u0016J\u001c\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001f2\u0006\u0010+\u001a\u00020,H\u0016J\u0014\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001fH\u0016J\u0014\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00160\u001fH\u0016J2\u0010/\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\t\u001a\u00020\n2\u0006\u00100\u001a\u00020\bH\u0096@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b1\u00102J*\u00103\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u000f\u001a\u00020\u000eH\u0096@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b4\u0010\u0011R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b\u0019\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00065"}, d2 = {"Lcom/example/sharen/data/repository/InstallmentRepositoryImpl;", "Lcom/example/sharen/data/repository/InstallmentRepository;", "remoteDataSource", "Lcom/example/sharen/data/remote/InstallmentRemoteDataSource;", "localDataSource", "Lcom/example/sharen/data/local/dao/InstallmentDao;", "(Lcom/example/sharen/data/remote/InstallmentRemoteDataSource;Lcom/example/sharen/data/local/dao/InstallmentDao;)V", "calculateRemainingAmount", "", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInstallment", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Installment;", "installment", "createInstallment-gIAlu-s", "(Lcom/example/sharen/domain/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInstallment", "", "deleteInstallment-gIAlu-s", "generateInstallmentSchedule", "", "totalAmount", "numberOfInstallments", "", "startDate", "Ljava/util/Date;", "intervalInDays", "(DILjava/util/Date;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllInstallments", "Lkotlinx/coroutines/flow/Flow;", "getCustomerInstallmentHistory", "customerId", "getInstallment", "getInstallment-gIAlu-s", "getInstallmentStatistics", "", "endDate", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstallmentsByCustomer", "getInstallmentsByDateRange", "getInstallmentsByStatus", "status", "Lcom/example/sharen/domain/model/InstallmentStatus;", "getOverdueInstallments", "getUpcomingInstallments", "payInstallment", "amount", "payInstallment-0E7RQCE", "(Ljava/lang/String;DLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInstallment", "updateInstallment-gIAlu-s", "app_debug"})
public final class InstallmentRepositoryImpl implements com.example.sharen.data.repository.InstallmentRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.InstallmentRemoteDataSource remoteDataSource = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.InstallmentDao localDataSource = null;
    
    @javax.inject.Inject
    public InstallmentRepositoryImpl(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.InstallmentRemoteDataSource remoteDataSource, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.InstallmentDao localDataSource) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getAllInstallments() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getInstallmentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getInstallmentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getInstallmentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.InstallmentStatus status) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getUpcomingInstallments() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getOverdueInstallments() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getInstallmentStatistics(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Double>> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object calculateRemainingAmount(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Installment>> getCustomerInstallmentHistory(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object generateInstallmentSchedule(double totalAmount, int numberOfInstallments, @org.jetbrains.annotations.NotNull
    java.util.Date startDate, int intervalInDays, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.sharen.domain.model.Installment>> $completion) {
        return null;
    }
}