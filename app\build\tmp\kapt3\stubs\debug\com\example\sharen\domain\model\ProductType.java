package com.example.sharen.domain.model;

/**
 * نوع محصول
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011\u00a8\u0006\u0012"}, d2 = {"Lcom/example/sharen/domain/model/ProductType;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "CLOTHING", "SHOES", "ACCESSORIES", "BAGS", "JEWELRY", "COSMETICS", "ELECTRONICS", "HOME", "SPORTS", "BOOKS", "OTHER", "app_debug"})
public enum ProductType {
    /*public static final*/ CLOTHING /* = new CLOTHING(null) */,
    /*public static final*/ SHOES /* = new SHOES(null) */,
    /*public static final*/ ACCESSORIES /* = new ACCESSORIES(null) */,
    /*public static final*/ BAGS /* = new BAGS(null) */,
    /*public static final*/ JEWELRY /* = new JEWELRY(null) */,
    /*public static final*/ COSMETICS /* = new COSMETICS(null) */,
    /*public static final*/ ELECTRONICS /* = new ELECTRONICS(null) */,
    /*public static final*/ HOME /* = new HOME(null) */,
    /*public static final*/ SPORTS /* = new SPORTS(null) */,
    /*public static final*/ BOOKS /* = new BOOKS(null) */,
    /*public static final*/ OTHER /* = new OTHER(null) */;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String displayName = null;
    
    ProductType(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.domain.model.ProductType> getEntries() {
        return null;
    }
}