package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCustomersUseCase_Factory implements Factory<GetCustomersUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public GetCustomersUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public GetCustomersUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static GetCustomersUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new GetCustomersUseCase_Factory(customerRepositoryProvider);
  }

  public static GetCustomersUseCase newInstance(CustomerRepository customerRepository) {
    return new GetCustomersUseCase(customerRepository);
  }
}
