package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetCustomerByIdUseCase_Factory implements Factory<GetCustomerByIdUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public GetCustomerByIdUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public GetCustomerByIdUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static GetCustomerByIdUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new GetCustomerByIdUseCase_Factory(customerRepositoryProvider);
  }

  public static GetCustomerByIdUseCase newInstance(CustomerRepository customerRepository) {
    return new GetCustomerByIdUseCase(customerRepository);
  }
}
