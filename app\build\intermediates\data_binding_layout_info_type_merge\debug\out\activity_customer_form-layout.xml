<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customer_form" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_customer_form.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_customer_form_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="115" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="15" endOffset="43"/></Target><Target id="@+id/til_customer_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="29" startOffset="12" endLine="42" endOffset="67"/></Target><Target id="@+id/et_customer_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="37" startOffset="16" endLine="41" endOffset="56"/></Target><Target id="@+id/til_phone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="44" startOffset="12" endLine="57" endOffset="67"/></Target><Target id="@+id/et_phone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="52" startOffset="16" endLine="56" endOffset="47"/></Target><Target id="@+id/til_address" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="59" startOffset="12" endLine="73" endOffset="67"/></Target><Target id="@+id/et_address" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="67" startOffset="16" endLine="72" endOffset="42"/></Target><Target id="@+id/til_notes" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="75" startOffset="12" endLine="89" endOffset="67"/></Target><Target id="@+id/et_notes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="83" startOffset="16" endLine="88" endOffset="42"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="96" startOffset="12" endLine="103" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="108" startOffset="4" endLine="113" endOffset="35"/></Target></Targets></Layout>