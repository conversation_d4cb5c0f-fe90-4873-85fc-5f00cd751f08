package com.example.sharen.data.repository

import com.example.sharen.data.model.Customer
import kotlinx.coroutines.flow.Flow

interface CustomerRepository {
    suspend fun getCustomers(): Flow<List<Customer>>
    
    suspend fun searchCustomers(query: String): Flow<List<Customer>>
    
    suspend fun getCustomerById(customerId: String): Flow<Customer?>
    
    suspend fun createCustomer(customer: Customer): Result<Customer>
    
    suspend fun updateCustomer(customer: Customer): Result<Customer>
    
    suspend fun deleteCustomer(customerId: String): Result<Boolean>
    
    suspend fun getCustomerCount(): Flow<Int>
} 