// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInvoiceProductBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final View divider;

  @NonNull
  public final ImageView ivProduct;

  @NonNull
  public final TextView tvDiscount;

  @NonNull
  public final TextView tvProductCode;

  @NonNull
  public final TextView tvProductName;

  @NonNull
  public final TextView tvQuantity;

  @NonNull
  public final TextView tvTotalPrice;

  @NonNull
  public final TextView tvUnitPrice;

  private ItemInvoiceProductBinding(@NonNull CardView rootView, @NonNull View divider,
      @NonNull ImageView ivProduct, @NonNull TextView tvDiscount, @NonNull TextView tvProductCode,
      @NonNull TextView tvProductName, @NonNull TextView tvQuantity, @NonNull TextView tvTotalPrice,
      @NonNull TextView tvUnitPrice) {
    this.rootView = rootView;
    this.divider = divider;
    this.ivProduct = ivProduct;
    this.tvDiscount = tvDiscount;
    this.tvProductCode = tvProductCode;
    this.tvProductName = tvProductName;
    this.tvQuantity = tvQuantity;
    this.tvTotalPrice = tvTotalPrice;
    this.tvUnitPrice = tvUnitPrice;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInvoiceProductBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInvoiceProductBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_invoice_product, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInvoiceProductBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.ivProduct;
      ImageView ivProduct = ViewBindings.findChildViewById(rootView, id);
      if (ivProduct == null) {
        break missingId;
      }

      id = R.id.tvDiscount;
      TextView tvDiscount = ViewBindings.findChildViewById(rootView, id);
      if (tvDiscount == null) {
        break missingId;
      }

      id = R.id.tvProductCode;
      TextView tvProductCode = ViewBindings.findChildViewById(rootView, id);
      if (tvProductCode == null) {
        break missingId;
      }

      id = R.id.tvProductName;
      TextView tvProductName = ViewBindings.findChildViewById(rootView, id);
      if (tvProductName == null) {
        break missingId;
      }

      id = R.id.tvQuantity;
      TextView tvQuantity = ViewBindings.findChildViewById(rootView, id);
      if (tvQuantity == null) {
        break missingId;
      }

      id = R.id.tvTotalPrice;
      TextView tvTotalPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalPrice == null) {
        break missingId;
      }

      id = R.id.tvUnitPrice;
      TextView tvUnitPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvUnitPrice == null) {
        break missingId;
      }

      return new ItemInvoiceProductBinding((CardView) rootView, divider, ivProduct, tvDiscount,
          tvProductCode, tvProductName, tvQuantity, tvTotalPrice, tvUnitPrice);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
