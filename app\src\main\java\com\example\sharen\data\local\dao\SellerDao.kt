package com.example.sharen.data.local.dao

import androidx.room.*
import com.example.sharen.data.local.entity.SellerEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface SellerDao {
    @Query("SELECT * FROM sellers")
    fun getAllSellers(): Flow<List<SellerEntity>>

    @Query("SELECT * FROM sellers WHERE id = :id")
    suspend fun getSellerById(id: String): SellerEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSeller(seller: SellerEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSellers(sellers: List<SellerEntity>)

    @Update
    suspend fun updateSeller(seller: SellerEntity)

    @Delete
    suspend fun deleteSeller(seller: SellerEntity)

    @Query("DELETE FROM sellers")
    suspend fun deleteAllSellers()
} 