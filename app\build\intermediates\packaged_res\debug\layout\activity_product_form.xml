<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.product.ProductFormActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/add_new_product" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- بخش تصویر محصول -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/product_image"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:id="@+id/ivProductImage"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:contentDescription="@string/product_image"
                            android:scaleType="centerCrop"
                            android:background="@android:color/darker_gray"
                            app:srcCompat="@drawable/ic_product_placeholder" />

                        <com.google.android.material.floatingactionbutton.FloatingActionButton
                            android:id="@+id/fabAddImage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom|end"
                            android:layout_margin="8dp"
                            android:contentDescription="@string/add_image"
                            app:fabSize="mini"
                            app:srcCompat="@android:drawable/ic_menu_camera" />
                            
                        <ProgressBar
                            android:id="@+id/imageLoadingProgressBar"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_gravity="center"
                            android:visibility="gone" />
                            
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="bottom"
                            android:gravity="center"
                            android:padding="4dp"
                            android:textSize="12sp"
                            android:textColor="@color/white"
                            android:background="#88000000"
                            android:text="@string/long_press_to_remove" />

                    </FrameLayout>
                    
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- اطلاعات پایه محصول -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="اطلاعات پایه"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/product_name"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_info_details">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etProductName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/category"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_sort_by_size">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCategory"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/description"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_edit">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etDescription"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:minLines="2"
                            android:maxLines="5" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- اطلاعات شناسایی محصول -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="اطلاعات شناسایی"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/product_code"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_sort_alphabetically">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etProductCode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/barcode"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_recent_history">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etBarcode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- اطلاعات قیمت و موجودی -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="قیمت و موجودی"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/purchase_price"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_slideshow">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etPurchasePrice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/selling_price"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_slideshow">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etSellingPrice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/stock"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_agenda">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etStock"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="@string/minimum_stock"
                        android:layout_marginBottom="8dp"
                        app:startIconDrawable="@android:drawable/ic_menu_agenda">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etMinimumStock"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:padding="12dp"
                android:text="@string/save"
                android:textSize="16sp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 