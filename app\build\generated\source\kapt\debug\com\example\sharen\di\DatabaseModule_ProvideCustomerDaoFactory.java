package com.example.sharen.di;

import com.example.sharen.data.local.SharenDatabase;
import com.example.sharen.data.local.dao.CustomerDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideCustomerDaoFactory implements Factory<CustomerDao> {
  private final Provider<SharenDatabase> databaseProvider;

  public DatabaseModule_ProvideCustomerDaoFactory(Provider<SharenDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public CustomerDao get() {
    return provideCustomerDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideCustomerDaoFactory create(
      Provider<SharenDatabase> databaseProvider) {
    return new DatabaseModule_ProvideCustomerDaoFactory(databaseProvider);
  }

  public static CustomerDao provideCustomerDao(SharenDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideCustomerDao(database));
  }
}
