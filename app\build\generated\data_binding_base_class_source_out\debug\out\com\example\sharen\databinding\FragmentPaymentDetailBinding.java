// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPaymentDetailBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Chip chipStatus;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView textViewAmount;

  @NonNull
  public final TextView textViewDate;

  @NonNull
  public final TextView textViewNotes;

  @NonNull
  public final TextView textViewPaymentMethod;

  @NonNull
  public final TextView textViewReference;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentPaymentDetailBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Chip chipStatus, @NonNull ProgressBar progressBar, @NonNull TextView textViewAmount,
      @NonNull TextView textViewDate, @NonNull TextView textViewNotes,
      @NonNull TextView textViewPaymentMethod, @NonNull TextView textViewReference,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.chipStatus = chipStatus;
    this.progressBar = progressBar;
    this.textViewAmount = textViewAmount;
    this.textViewDate = textViewDate;
    this.textViewNotes = textViewNotes;
    this.textViewPaymentMethod = textViewPaymentMethod;
    this.textViewReference = textViewReference;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPaymentDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPaymentDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_payment_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPaymentDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chipStatus;
      Chip chipStatus = ViewBindings.findChildViewById(rootView, id);
      if (chipStatus == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.textViewAmount;
      TextView textViewAmount = ViewBindings.findChildViewById(rootView, id);
      if (textViewAmount == null) {
        break missingId;
      }

      id = R.id.textViewDate;
      TextView textViewDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewDate == null) {
        break missingId;
      }

      id = R.id.textViewNotes;
      TextView textViewNotes = ViewBindings.findChildViewById(rootView, id);
      if (textViewNotes == null) {
        break missingId;
      }

      id = R.id.textViewPaymentMethod;
      TextView textViewPaymentMethod = ViewBindings.findChildViewById(rootView, id);
      if (textViewPaymentMethod == null) {
        break missingId;
      }

      id = R.id.textViewReference;
      TextView textViewReference = ViewBindings.findChildViewById(rootView, id);
      if (textViewReference == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentPaymentDetailBinding((CoordinatorLayout) rootView, chipStatus, progressBar,
          textViewAmount, textViewDate, textViewNotes, textViewPaymentMethod, textViewReference,
          toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
