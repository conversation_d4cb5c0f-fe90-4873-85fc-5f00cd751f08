package com.example.sharen.ui.profile;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DisplaySettingsViewModel_Factory implements Factory<DisplaySettingsViewModel> {
  @Override
  public DisplaySettingsViewModel get() {
    return newInstance();
  }

  public static DisplaySettingsViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DisplaySettingsViewModel newInstance() {
    return new DisplaySettingsViewModel();
  }

  private static final class InstanceHolder {
    private static final DisplaySettingsViewModel_Factory INSTANCE = new DisplaySettingsViewModel_Factory();
  }
}
