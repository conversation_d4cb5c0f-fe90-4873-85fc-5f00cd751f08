// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProductFormBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final TextInputEditText etBarcode;

  @NonNull
  public final TextInputEditText etCategory;

  @NonNull
  public final TextInputEditText etDescription;

  @NonNull
  public final TextInputEditText etMinimumStock;

  @NonNull
  public final TextInputEditText etProductCode;

  @NonNull
  public final TextInputEditText etProductName;

  @NonNull
  public final TextInputEditText etPurchasePrice;

  @NonNull
  public final TextInputEditText etSellingPrice;

  @NonNull
  public final TextInputEditText etStock;

  @NonNull
  public final FloatingActionButton fabAddImage;

  @NonNull
  public final ProgressBar imageLoadingProgressBar;

  @NonNull
  public final ImageView ivProductImage;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  private ActivityProductFormBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull MaterialButton btnSave,
      @NonNull TextInputEditText etBarcode, @NonNull TextInputEditText etCategory,
      @NonNull TextInputEditText etDescription, @NonNull TextInputEditText etMinimumStock,
      @NonNull TextInputEditText etProductCode, @NonNull TextInputEditText etProductName,
      @NonNull TextInputEditText etPurchasePrice, @NonNull TextInputEditText etSellingPrice,
      @NonNull TextInputEditText etStock, @NonNull FloatingActionButton fabAddImage,
      @NonNull ProgressBar imageLoadingProgressBar, @NonNull ImageView ivProductImage,
      @NonNull ProgressBar progressBar, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.btnSave = btnSave;
    this.etBarcode = etBarcode;
    this.etCategory = etCategory;
    this.etDescription = etDescription;
    this.etMinimumStock = etMinimumStock;
    this.etProductCode = etProductCode;
    this.etProductName = etProductName;
    this.etPurchasePrice = etPurchasePrice;
    this.etSellingPrice = etSellingPrice;
    this.etStock = etStock;
    this.fabAddImage = fabAddImage;
    this.imageLoadingProgressBar = imageLoadingProgressBar;
    this.ivProductImage = ivProductImage;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProductFormBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProductFormBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_product_form, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProductFormBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.etBarcode;
      TextInputEditText etBarcode = ViewBindings.findChildViewById(rootView, id);
      if (etBarcode == null) {
        break missingId;
      }

      id = R.id.etCategory;
      TextInputEditText etCategory = ViewBindings.findChildViewById(rootView, id);
      if (etCategory == null) {
        break missingId;
      }

      id = R.id.etDescription;
      TextInputEditText etDescription = ViewBindings.findChildViewById(rootView, id);
      if (etDescription == null) {
        break missingId;
      }

      id = R.id.etMinimumStock;
      TextInputEditText etMinimumStock = ViewBindings.findChildViewById(rootView, id);
      if (etMinimumStock == null) {
        break missingId;
      }

      id = R.id.etProductCode;
      TextInputEditText etProductCode = ViewBindings.findChildViewById(rootView, id);
      if (etProductCode == null) {
        break missingId;
      }

      id = R.id.etProductName;
      TextInputEditText etProductName = ViewBindings.findChildViewById(rootView, id);
      if (etProductName == null) {
        break missingId;
      }

      id = R.id.etPurchasePrice;
      TextInputEditText etPurchasePrice = ViewBindings.findChildViewById(rootView, id);
      if (etPurchasePrice == null) {
        break missingId;
      }

      id = R.id.etSellingPrice;
      TextInputEditText etSellingPrice = ViewBindings.findChildViewById(rootView, id);
      if (etSellingPrice == null) {
        break missingId;
      }

      id = R.id.etStock;
      TextInputEditText etStock = ViewBindings.findChildViewById(rootView, id);
      if (etStock == null) {
        break missingId;
      }

      id = R.id.fabAddImage;
      FloatingActionButton fabAddImage = ViewBindings.findChildViewById(rootView, id);
      if (fabAddImage == null) {
        break missingId;
      }

      id = R.id.imageLoadingProgressBar;
      ProgressBar imageLoadingProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (imageLoadingProgressBar == null) {
        break missingId;
      }

      id = R.id.ivProductImage;
      ImageView ivProductImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProductImage == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityProductFormBinding((CoordinatorLayout) rootView, appBarLayout, btnSave,
          etBarcode, etCategory, etDescription, etMinimumStock, etProductCode, etProductName,
          etPurchasePrice, etSellingPrice, etStock, fabAddImage, imageLoadingProgressBar,
          ivProductImage, progressBar, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
