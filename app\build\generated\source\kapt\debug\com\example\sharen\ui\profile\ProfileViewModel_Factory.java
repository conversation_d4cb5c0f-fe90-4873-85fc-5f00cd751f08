package com.example.sharen.ui.profile;

import com.example.sharen.data.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProfileViewModel_Factory implements Factory<ProfileViewModel> {
  private final Provider<NonExistentClass> userRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  public ProfileViewModel_Factory(Provider<NonExistentClass> userRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public ProfileViewModel get() {
    return newInstance(userRepositoryProvider.get(), authRepositoryProvider.get());
  }

  public static ProfileViewModel_Factory create(Provider<NonExistentClass> userRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    return new ProfileViewModel_Factory(userRepositoryProvider, authRepositoryProvider);
  }

  public static ProfileViewModel newInstance(NonExistentClass userRepository,
      AuthRepository authRepository) {
    return new ProfileViewModel(userRepository, authRepository);
  }
}
