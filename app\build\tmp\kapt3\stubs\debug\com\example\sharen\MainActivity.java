package com.example.sharen;

/**
 * Activity اصلی - Splash Screen
 */
@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\n\u001a\u00020\u000bH\u0002J\b\u0010\f\u001a\u00020\u0002H\u0016J\b\u0010\r\u001a\u00020\u000bH\u0002J\b\u0010\u000e\u001a\u00020\u000bH\u0002J\b\u0010\u000f\u001a\u00020\u000bH\u0016J\b\u0010\u0010\u001a\u00020\u000bH\u0002J\b\u0010\u0011\u001a\u00020\u000bH\u0016R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/example/sharen/MainActivity;", "Lcom/example/sharen/core/base/BaseActivity;", "Lcom/example/sharen/databinding/ActivityMainBinding;", "()V", "authViewModel", "Lcom/example/sharen/presentation/viewmodel/AuthViewModel;", "getAuthViewModel", "()Lcom/example/sharen/presentation/viewmodel/AuthViewModel;", "authViewModel$delegate", "Lkotlin/Lazy;", "checkUserLoginStatus", "", "getViewBinding", "navigateToDashboard", "navigateToLogin", "observeData", "setupSplashAnimation", "setupViews", "app_debug"})
public final class MainActivity extends com.example.sharen.core.base.BaseActivity<com.example.sharen.databinding.ActivityMainBinding> {
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy authViewModel$delegate = null;
    
    public MainActivity() {
        super();
    }
    
    private final com.example.sharen.presentation.viewmodel.AuthViewModel getAuthViewModel() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.databinding.ActivityMainBinding getViewBinding() {
        return null;
    }
    
    @java.lang.Override
    public void setupViews() {
    }
    
    @java.lang.Override
    public void observeData() {
    }
    
    private final void setupSplashAnimation() {
    }
    
    private final void checkUserLoginStatus() {
    }
    
    private final void navigateToLogin() {
    }
    
    private final void navigateToDashboard() {
    }
}