package com.example.sharen.util;

/**
 * کلاس کمکی برای مدیریت تصاویر
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u00072\b\b\u0002\u0010\u000b\u001a\u00020\u0004J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\b\u001a\u00020\tJ\u0016\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\rJ\u0006\u0010\u0011\u001a\u00020\u000fJ\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00132\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0014\u001a\u00020\u0007J \u0010\u0015\u001a\u00020\u00162\u0006\u0010\b\u001a\u00020\t2\b\u0010\u0017\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u0018\u001a\u00020\u0019J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0002J*\u0010\u001d\u001a\u00020\u00162\u0006\u0010\b\u001a\u00020\t2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00160\u001f2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00160\u001fJ\u001c\u0010!\u001a\u00020\u00162\u0006\u0010\b\u001a\u00020\t2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00160\u001fR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/example/sharen/util/ImageUtils;", "", "()V", "DEFAULT_COMPRESSION_QUALITY", "", "MAX_IMAGE_DIMENSION", "compressImage", "Landroid/net/Uri;", "context", "Landroid/content/Context;", "imageUri", "quality", "createImageFile", "Ljava/io/File;", "getCameraIntent", "Landroid/content/Intent;", "photoFile", "getGalleryIntent", "getPathFromUri", "", "uri", "loadImage", "", "imageUrl", "imageView", "Landroid/widget/ImageView;", "resizeBitmap", "Landroid/graphics/Bitmap;", "bitmap", "showImagePickerDialog", "onCameraSelected", "Lkotlin/Function0;", "onGallerySelected", "showRemoveImageDialog", "onRemoveConfirmed", "app_debug"})
public final class ImageUtils {
    private static final int MAX_IMAGE_DIMENSION = 1200;
    private static final int DEFAULT_COMPRESSION_QUALITY = 80;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.util.ImageUtils INSTANCE = null;
    
    private ImageUtils() {
        super();
    }
    
    public final void loadImage(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    java.lang.String imageUrl, @org.jetbrains.annotations.NotNull
    android.widget.ImageView imageView) {
    }
    
    @kotlin.jvm.Throws(exceptionClasses = {java.io.IOException.class})
    @org.jetbrains.annotations.NotNull
    public final java.io.File createImageFile(@org.jetbrains.annotations.NotNull
    android.content.Context context) throws java.io.IOException {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.content.Intent getCameraIntent(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    java.io.File photoFile) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final android.content.Intent getGalleryIntent() {
        return null;
    }
    
    public final void showImagePickerDialog(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onCameraSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onGallerySelected) {
    }
    
    public final void showRemoveImageDialog(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onRemoveConfirmed) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPathFromUri(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    android.net.Uri uri) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.net.Uri compressImage(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    android.net.Uri imageUri, int quality) {
        return null;
    }
    
    private final android.graphics.Bitmap resizeBitmap(android.graphics.Bitmap bitmap) {
        return null;
    }
}