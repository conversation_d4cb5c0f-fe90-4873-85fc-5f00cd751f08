package com.example.sharen.di;

import com.example.sharen.data.local.AppDatabase;
import com.example.sharen.data.local.dao.InstallmentDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideInstallmentDaoFactory implements Factory<InstallmentDao> {
  private final Provider<AppDatabase> databaseProvider;

  public DatabaseModule_ProvideInstallmentDaoFactory(Provider<AppDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public InstallmentDao get() {
    return provideInstallmentDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideInstallmentDaoFactory create(
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseModule_ProvideInstallmentDaoFactory(databaseProvider);
  }

  public static InstallmentDao provideInstallmentDao(AppDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideInstallmentDao(database));
  }
}
