<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_transaction" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_transaction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_transaction_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="35"/></Target><Target id="@+id/tv_transaction_number" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="24" endOffset="36"/></Target><Target id="@+id/tv_transaction_date" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="34" endOffset="37"/></Target><Target id="@+id/tv_customer_name" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="45" endOffset="36"/></Target><Target id="@+id/tv_transaction_amount" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="57" endOffset="43"/></Target><Target id="@+id/tv_transaction_status" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="74" endOffset="37"/></Target></Targets></Layout>