package com.example.sharen.data.remote

import com.example.sharen.data.model.Report
import com.example.sharen.data.model.ReportData
import com.example.sharen.data.model.ReportType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReportRemoteDataSource @Inject constructor(
    private val apiService: SupabaseApiService
) {
    suspend fun getReportData(startDate: Long, endDate: Long): ReportData {
        return apiService.getReportData(startDate, endDate).body() ?: ReportData()
    }

    // Financial Reports
    suspend fun generateDailyFinancialReport(date: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Daily Financial Report - ${date}",
            content = generateFinancialReportContent(date, date),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create daily financial report")
    }
    
    suspend fun generateMonthlyFinancialReport(year: Int, month: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfMonth(year, month)
        val endDate = getLastDayOfMonth(year, month)
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Monthly Financial Report - $year/$month",
            content = generateFinancialReportContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create monthly financial report")
    }
    
    suspend fun generateYearlyFinancialReport(year: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfYear(year)
        val endDate = getLastDayOfYear(year)
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Yearly Financial Report - $year",
            content = generateFinancialReportContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create yearly financial report")
    }
    
    suspend fun generateProfitLossReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Profit & Loss Report",
            content = generateProfitLossContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create profit & loss report")
    }
    
    suspend fun generateDebtReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.FINANCIAL,
            title = "Debt Report",
            content = generateDebtReportContent(),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create debt report")
    }
    
    // Sales Reports
    suspend fun generateDailySalesReport(date: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Daily Sales Report - ${date}",
            content = generateSalesReportContent(date, date),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create daily sales report")
    }
    
    suspend fun generateMonthlySalesReport(year: Int, month: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfMonth(year, month)
        val endDate = getLastDayOfMonth(year, month)
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Monthly Sales Report - $year/$month",
            content = generateSalesReportContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create monthly sales report")
    }
    
    suspend fun generateYearlySalesReport(year: Int): Result<Report> = runCatching {
        val startDate = getFirstDayOfYear(year)
        val endDate = getLastDayOfYear(year)
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Yearly Sales Report - $year",
            content = generateSalesReportContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create yearly sales report")
    }
    
    suspend fun generateSalesByProductReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Sales by Product Report",
            content = generateSalesByProductContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create sales by product report")
    }
    
    suspend fun generateSalesByCustomerReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Sales by Customer Report",
            content = generateSalesByCustomerContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create sales by customer report")
    }
    
    suspend fun generateSalesByCategoryReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.SALES,
            title = "Sales by Category Report",
            content = generateSalesByCategoryContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create sales by category report")
    }
    
    // Inventory Reports
    suspend fun generateCurrentInventoryReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Current Inventory Report",
            content = generateCurrentInventoryContent(),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create current inventory report")
    }
    
    suspend fun generateLowStockReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Low Stock Report",
            content = generateLowStockContent(),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create low stock report")
    }
    
    suspend fun generateInventoryMovementReport(startDate: Date, endDate: Date): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Inventory Movement Report",
            content = generateInventoryMovementContent(startDate, endDate),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create inventory movement report")
    }
    
    suspend fun generateInventoryValueReport(): Result<Report> = runCatching {
        val report = Report(
            id = "",
            type = ReportType.INVENTORY,
            title = "Inventory Value Report",
            content = generateInventoryValueContent(),
            createdAt = Date(),
            createdBy = "" // Will be set by the server
        )
        apiService.createReport(report).body() ?: throw Exception("Failed to create inventory value report")
    }
    
    // General Report Management
    suspend fun saveReport(report: Report): Result<Report> = runCatching {
        apiService.createReport(report).body() ?: throw Exception("Failed to save report")
    }
    
    suspend fun deleteReport(reportId: String): Result<Unit> = runCatching {
        apiService.deleteReport(reportId).body() ?: throw Exception("Failed to delete report")
    }
    
    suspend fun getReport(reportId: String): Result<Report> = runCatching {
        apiService.getReport(reportId).body() ?: throw Exception("Report not found")
    }
    
    fun getAllReports(): Flow<List<Report>> = flow {
        val response = apiService.getReports()
        if (response.isSuccessful) {
            emit(response.body() ?: emptyList())
        } else {
            throw Exception("Failed to fetch reports")
        }
    }
    
    fun getReportsByType(type: ReportType): Flow<List<Report>> = flow {
        val response = apiService.getReports()
        if (response.isSuccessful) {
            val reports = response.body() ?: emptyList()
            emit(reports.filter { it.type == type })
        } else {
            throw Exception("Failed to fetch reports by type")
        }
    }
    
    fun getReportsByDateRange(startDate: Date, endDate: Date): Flow<List<Report>> = flow {
        val response = apiService.getReports()
        if (response.isSuccessful) {
            val reports = response.body() ?: emptyList()
            emit(reports.filter { 
                it.createdAt in startDate..endDate 
            })
        } else {
            throw Exception("Failed to fetch reports by date range")
        }
    }
    
    // Helper functions for report content generation
    private fun generateFinancialReportContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "totalRevenue" to 0.0,
            "totalExpenses" to 0.0,
            "netProfit" to 0.0,
            "details" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateProfitLossContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "revenue" to mapOf(
                "total" to 0.0,
                "details" to emptyList<Map<String, Any>>()
            ),
            "expenses" to mapOf(
                "total" to 0.0,
                "details" to emptyList<Map<String, Any>>()
            ),
            "netProfit" to 0.0
        )
    }
    
    private fun generateDebtReportContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "totalDebt" to 0.0,
            "customers" to emptyList<Map<String, Any>>(),
            "installments" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateSalesReportContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "totalSales" to 0.0,
            "totalOrders" to 0,
            "averageOrderValue" to 0.0,
            "details" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateSalesByProductContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "products" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateSalesByCustomerContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "customers" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateSalesByCategoryContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "categories" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateCurrentInventoryContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "totalItems" to 0,
            "totalValue" to 0.0,
            "products" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateLowStockContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "threshold" to 10,
            "products" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateInventoryMovementContent(startDate: Date, endDate: Date): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "startDate" to startDate,
            "endDate" to endDate,
            "movements" to emptyList<Map<String, Any>>()
        )
    }
    
    private fun generateInventoryValueContent(): Map<String, Any> {
        // Implementation will depend on the actual data structure and requirements
        return mapOf(
            "totalValue" to 0.0,
            "categories" to emptyList<Map<String, Any>>()
        )
    }
    
    // Helper functions for date calculations
    private fun getFirstDayOfMonth(year: Int, month: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, month - 1, 1, 0, 0, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.time
    }
    
    private fun getLastDayOfMonth(year: Int, month: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, month, 0, 23, 59, 59)
        calendar.set(java.util.Calendar.MILLISECOND, 999)
        return calendar.time
    }
    
    private fun getFirstDayOfYear(year: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, 0, 1, 0, 0, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        return calendar.time
    }
    
    private fun getLastDayOfYear(year: Int): Date {
        val calendar = java.util.Calendar.getInstance()
        calendar.set(year, 11, 31, 23, 59, 59)
        calendar.set(java.util.Calendar.MILLISECOND, 999)
        return calendar.time
    }

    // Report Export
    suspend fun exportReportToPdf(reportId: String): Result<String> = runCatching {
        apiService.exportReportToPdf(reportId).body() ?: throw Exception("Failed to export report to PDF")
    }
    
    suspend fun exportReportToExcel(reportId: String): Result<String> = runCatching {
        apiService.exportReportToExcel(reportId).body() ?: throw Exception("Failed to export report to Excel")
    }
    
    suspend fun exportReportToCsv(reportId: String): Result<String> = runCatching {
        apiService.exportReportToCsv(reportId).body() ?: throw Exception("Failed to export report to CSV")
    }
    
    // Report Templates
    suspend fun saveReportTemplate(template: Report): Result<Report> = runCatching {
        apiService.saveReportTemplate(template).body() ?: throw Exception("Failed to save report template")
    }
    
    suspend fun getReportTemplate(templateId: String): Result<Report> = runCatching {
        apiService.getReportTemplate(templateId).body() ?: throw Exception("Report template not found")
    }
    
    fun getAllReportTemplates(): Flow<List<Report>> = flow {
        val response = apiService.getAllReportTemplates()
        if (response.isSuccessful) {
            emit(response.body() ?: emptyList())
        } else {
            throw Exception("Failed to fetch report templates")
        }
    }
    
    suspend fun deleteReportTemplate(templateId: String): Result<Unit> = runCatching {
        apiService.deleteReportTemplate(templateId).body() ?: throw Exception("Failed to delete report template")
    }
    
    suspend fun updateReport(report: Report): Result<Report> = runCatching {
        apiService.updateReport(report).body() ?: throw Exception("Failed to update report")
    }
} 