package com.example.sharen.domain.usecase.invoice;

import com.example.sharen.domain.repository.InvoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetInvoicesByCustomerUseCase_Factory implements Factory<GetInvoicesByCustomerUseCase> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  public GetInvoicesByCustomerUseCase_Factory(
      Provider<InvoiceRepository> invoiceRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
  }

  @Override
  public GetInvoicesByCustomerUseCase get() {
    return newInstance(invoiceRepositoryProvider.get());
  }

  public static GetInvoicesByCustomerUseCase_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider) {
    return new GetInvoicesByCustomerUseCase_Factory(invoiceRepositoryProvider);
  }

  public static GetInvoicesByCustomerUseCase newInstance(InvoiceRepository invoiceRepository) {
    return new GetInvoicesByCustomerUseCase(invoiceRepository);
  }
}
