package com.example.sharen.ui.report;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0014J \u0010\u0013\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0019H\u0016J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\b\u0010\u001e\u001a\u00020\u001bH\u0016J\b\u0010\u001f\u001a\u00020\u0010H\u0002J\b\u0010 \u001a\u00020\u0010H\u0002J\b\u0010!\u001a\u00020\u0010H\u0002J\b\u0010\"\u001a\u00020\u0010H\u0002J\b\u0010#\u001a\u00020\u0010H\u0002J\b\u0010$\u001a\u00020\u0010H\u0002J\u0010\u0010%\u001a\u00020\u00102\u0006\u0010&\u001a\u00020\'H\u0002J\u0010\u0010(\u001a\u00020\u00102\u0006\u0010&\u001a\u00020\'H\u0002J\u0010\u0010)\u001a\u00020\u00102\u0006\u0010&\u001a\u00020\'H\u0002J\b\u0010*\u001a\u00020\u0010H\u0002J\u0010\u0010+\u001a\u00020\u00102\u0006\u0010&\u001a\u00020\'H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\t\u001a\u00020\n8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000b\u0010\f\u00a8\u0006,"}, d2 = {"Lcom/example/sharen/ui/report/ReportActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "Lcom/example/sharen/databinding/ActivityReportBinding;", "currentReportType", "Lcom/example/sharen/data/model/ReportType;", "dateFormatter", "Ljava/text/SimpleDateFormat;", "viewModel", "Lcom/example/sharen/ui/report/ReportViewModel;", "getViewModel", "()Lcom/example/sharen/ui/report/ReportViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onDateSet", "id", "", "calendar", "Ljava/util/Calendar;", "date", "", "onNavigationItemSelected", "", "item", "Landroid/view/MenuItem;", "onSupportNavigateUp", "setupDateRangeButtons", "setupExportButton", "setupObservers", "setupTabLayout", "setupToolbar", "showDatePicker", "updateCustomerChart", "data", "Lcom/example/sharen/data/model/ReportData;", "updateFinancialChart", "updateInventoryChart", "updateReportView", "updateSalesChart", "app_debug"})
public final class ReportActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.example.sharen.databinding.ActivityReportBinding binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private java.text.SimpleDateFormat dateFormatter;
    @org.jetbrains.annotations.NotNull
    private com.example.sharen.data.model.ReportType currentReportType = com.example.sharen.data.model.ReportType.SALES;
    
    public ReportActivity() {
        super();
    }
    
    private final com.example.sharen.ui.report.ReportViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupTabLayout() {
    }
    
    private final void setupObservers() {
    }
    
    private final void setupDateRangeButtons() {
    }
    
    private final void setupExportButton() {
    }
    
    private final void showDatePicker() {
    }
    
    private final void updateReportView() {
    }
    
    private final void updateSalesChart(com.example.sharen.data.model.ReportData data) {
    }
    
    private final void updateFinancialChart(com.example.sharen.data.model.ReportData data) {
    }
    
    private final void updateInventoryChart(com.example.sharen.data.model.ReportData data) {
    }
    
    private final void updateCustomerChart(com.example.sharen.data.model.ReportData data) {
    }
    
    public void onDateSet(int id, @org.jetbrains.annotations.NotNull
    java.util.Calendar calendar, @org.jetbrains.annotations.NotNull
    java.lang.String date) {
    }
    
    private final boolean onNavigationItemSelected(android.view.MenuItem item) {
        return false;
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
}