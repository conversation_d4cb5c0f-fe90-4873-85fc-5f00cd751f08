<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_payment_detail" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_payment_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_payment_detail_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="206" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="9" startOffset="8" endLine="17" endOffset="47"/></Target><Target id="@+id/textViewAmount" view="TextView"><Expressions/><location startLine="51" startOffset="20" endLine="57" endOffset="46"/></Target><Target id="@+id/chipStatus" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="59" startOffset="20" endLine="64" endOffset="48"/></Target><Target id="@+id/textViewDate" view="TextView"><Expressions/><location startLine="102" startOffset="24" endLine="109" endOffset="53"/></Target><Target id="@+id/textViewPaymentMethod" view="TextView"><Expressions/><location startLine="126" startOffset="24" endLine="133" endOffset="47"/></Target><Target id="@+id/textViewReference" view="TextView"><Expressions/><location startLine="150" startOffset="24" endLine="157" endOffset="49"/></Target><Target id="@+id/textViewNotes" view="TextView"><Expressions/><location startLine="183" startOffset="20" endLine="189" endOffset="61"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="199" startOffset="4" endLine="204" endOffset="35"/></Target></Targets></Layout>