package com.example.sharen.ui.dashboard;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u00012\u00020\u0002B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\u0011\u001a\u00020\u0012H\u0002J\u0012\u0010\u0013\u001a\u00020\u00122\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u0014J\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0019H\u0016J\u0010\u0010\u001a\u001a\u00020\u00122\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\b\u0010\u001d\u001a\u00020\u0012H\u0002J\b\u0010\u001e\u001a\u00020\u0012H\u0002J\b\u0010\u001f\u001a\u00020\u0012H\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u000b\u001a\u00020\f8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000f\u0010\u0010\u001a\u0004\b\r\u0010\u000e\u00a8\u0006 "}, d2 = {"Lcom/example/sharen/ui/dashboard/DashboardActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/google/android/material/navigation/NavigationBarView$OnItemSelectedListener;", "()V", "binding", "Lcom/example/sharen/databinding/ActivityDashboardBinding;", "numberFormatter", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "transactionAdapter", "Lcom/example/sharen/ui/dashboard/TransactionAdapter;", "viewModel", "Lcom/example/sharen/ui/dashboard/DashboardViewModel;", "getViewModel", "()Lcom/example/sharen/ui/dashboard/DashboardViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "logout", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onNavigationItemSelected", "", "item", "Landroid/view/MenuItem;", "onTransactionClicked", "transaction", "Lcom/example/sharen/data/model/Transaction;", "setupObservers", "setupTransactionsRecyclerView", "setupUI", "app_debug"})
public final class DashboardActivity extends androidx.appcompat.app.AppCompatActivity implements com.google.android.material.navigation.NavigationBarView.OnItemSelectedListener {
    private com.example.sharen.databinding.ActivityDashboardBinding binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private com.example.sharen.ui.dashboard.TransactionAdapter transactionAdapter;
    private final java.text.NumberFormat numberFormatter = null;
    
    public DashboardActivity() {
        super();
    }
    
    private final com.example.sharen.ui.dashboard.DashboardViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupTransactionsRecyclerView() {
    }
    
    private final void setupObservers() {
    }
    
    private final void onTransactionClicked(com.example.sharen.data.model.Transaction transaction) {
    }
    
    @java.lang.Override
    public boolean onNavigationItemSelected(@org.jetbrains.annotations.NotNull
    android.view.MenuItem item) {
        return false;
    }
    
    private final void logout() {
    }
}