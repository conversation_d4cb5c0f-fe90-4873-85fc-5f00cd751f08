<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_profile" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_profile_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/ivProfileImage" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="37" startOffset="12" endLine="45" endOffset="44"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="48" startOffset="12" endLine="63" endOffset="67"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="16" endLine="61" endOffset="56"/></Target><Target id="@+id/tilEmail" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="66" startOffset="12" endLine="82" endOffset="67"/></Target><Target id="@+id/etEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="75" startOffset="16" endLine="80" endOffset="58"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="85" startOffset="12" endLine="100" endOffset="67"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="94" startOffset="16" endLine="98" endOffset="47"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="103" startOffset="12" endLine="110" endOffset="58"/></Target><Target id="@+id/btnChangePassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="113" startOffset="12" endLine="120" endOffset="58"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="123" startOffset="12" endLine="129" endOffset="58"/></Target></Targets></Layout>