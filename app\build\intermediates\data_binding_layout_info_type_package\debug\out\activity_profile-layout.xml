<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_profile" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_profile_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="136" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/ivProfileImage" view="ImageView"><Expressions/><location startLine="37" startOffset="12" endLine="46" endOffset="74"/></Target><Target id="@+id/tilName" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="49" startOffset="12" endLine="64" endOffset="67"/></Target><Target id="@+id/etName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="58" startOffset="16" endLine="62" endOffset="56"/></Target><Target id="@+id/tilEmail" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="67" startOffset="12" endLine="83" endOffset="67"/></Target><Target id="@+id/etEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="16" endLine="81" endOffset="58"/></Target><Target id="@+id/tilPhone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="86" startOffset="12" endLine="101" endOffset="67"/></Target><Target id="@+id/etPhone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="95" startOffset="16" endLine="99" endOffset="47"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="104" startOffset="12" endLine="111" endOffset="58"/></Target><Target id="@+id/btnChangePassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="114" startOffset="12" endLine="121" endOffset="58"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="124" startOffset="12" endLine="130" endOffset="58"/></Target></Targets></Layout>