package com.example.sharen.ui.invoice;

import com.example.sharen.data.repository.InvoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceDetailsViewModel_Factory implements Factory<InvoiceDetailsViewModel> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  public InvoiceDetailsViewModel_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
  }

  @Override
  public InvoiceDetailsViewModel get() {
    return newInstance(invoiceRepositoryProvider.get());
  }

  public static InvoiceDetailsViewModel_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider) {
    return new InvoiceDetailsViewModel_Factory(invoiceRepositoryProvider);
  }

  public static InvoiceDetailsViewModel newInstance(InvoiceRepository invoiceRepository) {
    return new InvoiceDetailsViewModel(invoiceRepository);
  }
}
