package com.example.sharen.ui.invoice;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceDetailsViewModel_Factory implements Factory<InvoiceDetailsViewModel> {
  private final Provider<NonExistentClass> invoiceRepositoryProvider;

  public InvoiceDetailsViewModel_Factory(Provider<NonExistentClass> invoiceRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
  }

  @Override
  public InvoiceDetailsViewModel get() {
    return newInstance(invoiceRepositoryProvider.get());
  }

  public static InvoiceDetailsViewModel_Factory create(
      Provider<NonExistentClass> invoiceRepositoryProvider) {
    return new InvoiceDetailsViewModel_Factory(invoiceRepositoryProvider);
  }

  public static InvoiceDetailsViewModel newInstance(NonExistentClass invoiceRepository) {
    return new InvoiceDetailsViewModel(invoiceRepository);
  }
}
