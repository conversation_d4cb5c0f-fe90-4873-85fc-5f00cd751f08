package com.example.sharen.ui.product;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductDetailsViewModel_Factory implements Factory<ProductDetailsViewModel> {
  private final Provider<NonExistentClass> productRepositoryProvider;

  public ProductDetailsViewModel_Factory(Provider<NonExistentClass> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public ProductDetailsViewModel get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static ProductDetailsViewModel_Factory create(
      Provider<NonExistentClass> productRepositoryProvider) {
    return new ProductDetailsViewModel_Factory(productRepositoryProvider);
  }

  public static ProductDetailsViewModel newInstance(NonExistentClass productRepository) {
    return new ProductDetailsViewModel(productRepository);
  }
}
