<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".presentation.ui.customer.CustomerActivity">

    <!-- AppBar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="مشتریان"
            app:titleTextColor="@android:color/white"
            app:layout_scrollFlags="scroll|enterAlways" />

        <!-- جستجو -->
        <androidx.appcompat.widget.SearchView
            android:id="@+id/search_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/search_background"
            app:queryHint="جستجوی مشتریان..."
            app:iconifiedByDefault="false" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- محتوای اصلی -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- لیست مشتریان -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp"
                android:clipToPadding="false"
                tools:listitem="@layout/item_customer" />

            <!-- حالت خالی -->
            <LinearLayout
                android:id="@+id/empty_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@drawable/ic_customers_empty"
                    android:alpha="0.5"
                    android:contentDescription="مشتری وجود ندارد" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="مشتری‌ای یافت نشد"
                    android:textSize="18sp"
                    android:textColor="@color/text_secondary"
                    android:fontFamily="@font/iransans_bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="برای افزودن مشتری جدید روی دکمه + کلیک کنید"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:gravity="center" />

            </LinearLayout>

            <!-- نوار پیشرفت -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- دکمه افزودن -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="افزودن مشتری جدید"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
