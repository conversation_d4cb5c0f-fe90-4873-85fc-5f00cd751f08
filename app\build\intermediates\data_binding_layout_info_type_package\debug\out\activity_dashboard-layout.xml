<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_dashboard" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_dashboard_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="318" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="33" endOffset="43"/></Target><Target id="@+id/iv_notifications" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="31" endOffset="67"/></Target><Target id="@+id/layout_stats" view="LinearLayout"><Expressions/><location startLine="49" startOffset="16" endLine="139" endOffset="30"/></Target><Target id="@+id/tv_total_sales_value" view="TextView"><Expressions/><location startLine="79" startOffset="28" endLine="87" endOffset="58"/></Target><Target id="@+id/tv_total_customers_value" view="TextView"><Expressions/><location startLine="120" startOffset="28" endLine="128" endOffset="58"/></Target><Target id="@+id/tv_recent_transactions_header" view="TextView"><Expressions/><location startLine="142" startOffset="16" endLine="153" endOffset="77"/></Target><Target id="@+id/tv_view_all" view="TextView"><Expressions/><location startLine="155" startOffset="16" endLine="162" endOffset="63"/></Target><Target id="@+id/rv_recent_transactions" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="164" startOffset="16" endLine="173" endOffset="63"/></Target><Target id="@+id/tv_quick_actions_header" view="TextView"><Expressions/><location startLine="176" startOffset="16" endLine="187" endOffset="87"/></Target><Target id="@+id/layout_quick_actions" view="LinearLayout"><Expressions/><location startLine="189" startOffset="16" endLine="294" endOffset="30"/></Target><Target id="@+id/card_new_invoice" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="199" startOffset="20" endLine="229" endOffset="55"/></Target><Target id="@+id/card_add_customer" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="231" startOffset="20" endLine="261" endOffset="55"/></Target><Target id="@+id/card_test_image_upload" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="263" startOffset="20" endLine="293" endOffset="55"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="301" startOffset="4" endLine="306" endOffset="49"/></Target><Target id="@+id/fab_add" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="308" startOffset="4" endLine="316" endOffset="30"/></Target></Targets></Layout>