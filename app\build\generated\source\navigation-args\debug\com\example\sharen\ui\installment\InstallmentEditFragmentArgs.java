package com.example.sharen.ui.installment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.lifecycle.SavedStateHandle;
import androidx.navigation.NavArgs;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class InstallmentEditFragmentArgs implements NavArgs {
  private final HashMap arguments = new HashMap();

  private InstallmentEditFragmentArgs() {
  }

  @SuppressWarnings("unchecked")
  private InstallmentEditFragmentArgs(HashMap argumentsMap) {
    this.arguments.putAll(argumentsMap);
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static InstallmentEditFragmentArgs fromBundle(@NonNull Bundle bundle) {
    InstallmentEditFragmentArgs __result = new InstallmentEditFragmentArgs();
    bundle.setClassLoader(InstallmentEditFragmentArgs.class.getClassLoader());
    if (bundle.containsKey("installmentId")) {
      long installmentId;
      installmentId = bundle.getLong("installmentId");
      __result.arguments.put("installmentId", installmentId);
    } else {
      __result.arguments.put("installmentId", 0L);
    }
    return __result;
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static InstallmentEditFragmentArgs fromSavedStateHandle(
      @NonNull SavedStateHandle savedStateHandle) {
    InstallmentEditFragmentArgs __result = new InstallmentEditFragmentArgs();
    if (savedStateHandle.contains("installmentId")) {
      long installmentId;
      installmentId = savedStateHandle.get("installmentId");
      __result.arguments.put("installmentId", installmentId);
    } else {
      __result.arguments.put("installmentId", 0L);
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  public long getInstallmentId() {
    return (long) arguments.get("installmentId");
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public Bundle toBundle() {
    Bundle __result = new Bundle();
    if (arguments.containsKey("installmentId")) {
      long installmentId = (long) arguments.get("installmentId");
      __result.putLong("installmentId", installmentId);
    } else {
      __result.putLong("installmentId", 0L);
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public SavedStateHandle toSavedStateHandle() {
    SavedStateHandle __result = new SavedStateHandle();
    if (arguments.containsKey("installmentId")) {
      long installmentId = (long) arguments.get("installmentId");
      __result.set("installmentId", installmentId);
    } else {
      __result.set("installmentId", 0L);
    }
    return __result;
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
        return true;
    }
    if (object == null || getClass() != object.getClass()) {
        return false;
    }
    InstallmentEditFragmentArgs that = (InstallmentEditFragmentArgs) object;
    if (arguments.containsKey("installmentId") != that.arguments.containsKey("installmentId")) {
      return false;
    }
    if (getInstallmentId() != that.getInstallmentId()) {
      return false;
    }
    return true;
  }

  @Override
  public int hashCode() {
    int result = 1;
    result = 31 * result + (int)(getInstallmentId() ^ (getInstallmentId() >>> 32));
    return result;
  }

  @Override
  public String toString() {
    return "InstallmentEditFragmentArgs{"
        + "installmentId=" + getInstallmentId()
        + "}";
  }

  public static final class Builder {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    public Builder(@NonNull InstallmentEditFragmentArgs original) {
      this.arguments.putAll(original.arguments);
    }

    public Builder() {
    }

    @NonNull
    public InstallmentEditFragmentArgs build() {
      InstallmentEditFragmentArgs result = new InstallmentEditFragmentArgs(arguments);
      return result;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public Builder setInstallmentId(long installmentId) {
      this.arguments.put("installmentId", installmentId);
      return this;
    }

    @SuppressWarnings({"unchecked","GetterOnBuilder"})
    public long getInstallmentId() {
      return (long) arguments.get("installmentId");
    }
  }
}
