package com.example.sharen.data.repository

import com.example.sharen.data.model.Product
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Calendar
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * پیاده‌سازی رپوزیتوری محصولات با داده‌های آزمایشی
 */
@Singleton
class ProductRepositoryImpl @Inject constructor() : ProductRepository {

    // لیست محصولات آزمایشی
    private val mockProducts = mutableListOf<Product>().apply {
        // ایجاد چند محصول آزمایشی
        add(
            Product(
                id = "p1",
                name = "پیراهن مردانه",
                code = "SH-001",
                barcode = "12345678",
                description = "پیراهن مردانه ساده با کیفیت عالی",
                category = "پوشاک مردانه",
                purchasePrice = 150000,
                sellingPrice = 250000,
                stock = 25,
                minimumStock = 5,
                imageUrl = null,
                isActive = true,
                lastUpdated = getDateBefore(5),
                createdAt = getDateBefore(30)
            )
        )
        add(
            Product(
                id = "p2",
                name = "شلوار جین زنانه",
                code = "PA-001",
                barcode = "87654321",
                description = "شلوار جین زنانه با طراحی شیک",
                category = "پوشاک زنانه",
                purchasePrice = 200000,
                sellingPrice = 350000,
                stock = 15,
                minimumStock = 3,
                imageUrl = null,
                isActive = true,
                lastUpdated = getDateBefore(2),
                createdAt = getDateBefore(45)
            )
        )
        add(
            Product(
                id = "p3",
                name = "کفش ورزشی",
                code = "SH-002",
                barcode = "56781234",
                description = "کفش ورزشی با کیفیت بالا",
                category = "کفش",
                purchasePrice = 350000,
                sellingPrice = 580000,
                stock = 10,
                minimumStock = 2,
                imageUrl = null,
                isActive = true,
                lastUpdated = getDateBefore(1),
                createdAt = getDateBefore(15)
            )
        )
        add(
            Product(
                id = "p4",
                name = "کیف دستی زنانه",
                code = "BG-001",
                barcode = "43218765",
                description = "کیف دستی زنانه چرم مصنوعی",
                category = "کیف",
                purchasePrice = 120000,
                sellingPrice = 230000,
                stock = 8,
                minimumStock = 3,
                imageUrl = null,
                isActive = true,
                lastUpdated = getDateBefore(3),
                createdAt = getDateBefore(50)
            )
        )
        add(
            Product(
                id = "p5",
                name = "لباس بچگانه",
                code = "KD-001",
                barcode = "13572468",
                description = "لباس راحتی بچگانه",
                category = "پوشاک بچگانه",
                purchasePrice = 85000,
                sellingPrice = 140000,
                stock = 2,
                minimumStock = 5,
                imageUrl = null,
                isActive = true,
                lastUpdated = getDateBefore(7),
                createdAt = getDateBefore(60)
            )
        )
    }

    override fun getAllProducts(): Flow<List<Product>> = flow {
        emit(mockProducts.filter { it.isActive })
    }

    override fun getProductById(productId: String): Flow<Product> = flow {
        mockProducts.find { it.id == productId }?.let {
            emit(it)
        } ?: throw NoSuchElementException("محصول با شناسه $productId یافت نشد")
    }

    override fun searchProducts(query: String): Flow<List<Product>> = flow {
        val searchQuery = query.trim().lowercase()
        val result = mockProducts.filter { product ->
            product.isActive && (
                product.name.lowercase().contains(searchQuery) ||
                (product.code?.lowercase()?.contains(searchQuery) ?: false) ||
                (product.description?.lowercase()?.contains(searchQuery) ?: false) ||
                (product.barcode?.lowercase()?.contains(searchQuery) ?: false) ||
                (product.category?.lowercase()?.contains(searchQuery) ?: false)
            )
        }
        emit(result)
    }

    override suspend fun createProduct(product: Product): Result<Product> {
        return try {
            val newProduct = product.copy(
                id = UUID.randomUUID().toString(),
                createdAt = Date(),
                lastUpdated = Date()
            )
            mockProducts.add(newProduct)
            Result.success(newProduct)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateProduct(product: Product): Result<Product> {
        return try {
            val index = mockProducts.indexOfFirst { it.id == product.id }
            if (index != -1) {
                val updatedProduct = product.copy(lastUpdated = Date())
                mockProducts[index] = updatedProduct
                Result.success(updatedProduct)
            } else {
                Result.failure(NoSuchElementException("محصول با شناسه ${product.id} یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteProduct(productId: String): Result<Unit> {
        return try {
            val index = mockProducts.indexOfFirst { it.id == productId }
            if (index != -1) {
                // به جای حذف فیزیکی، محصول را غیرفعال می‌کنیم
                val product = mockProducts[index]
                mockProducts[index] = product.copy(isActive = false, lastUpdated = Date())
                Result.success(Unit)
            } else {
                Result.failure(NoSuchElementException("محصول با شناسه $productId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateProductStock(productId: String, newStock: Int): Result<Product> {
        return try {
            val index = mockProducts.indexOfFirst { it.id == productId }
            if (index != -1) {
                val product = mockProducts[index]
                val updatedProduct = product.copy(stock = newStock, lastUpdated = Date())
                mockProducts[index] = updatedProduct
                Result.success(updatedProduct)
            } else {
                Result.failure(NoSuchElementException("محصول با شناسه $productId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getProductCount(): Flow<Int> = flow {
        emit(mockProducts.count { it.isActive })
    }

    override fun getLowStockProducts(): Flow<List<Product>> = flow {
        val lowStockProducts = mockProducts.filter { it.isActive && it.isLowStock }
        emit(lowStockProducts)
    }

    override fun getProductsByCategory(category: String): Flow<List<Product>> = flow {
        val products = mockProducts.filter { it.isActive && it.category == category }
        emit(products)
    }

    // تابع کمکی برای ایجاد تاریخ‌های قبل
    private fun getDateBefore(days: Int): Date {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -days)
        return calendar.time
    }
} 