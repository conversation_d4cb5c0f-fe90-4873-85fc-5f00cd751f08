package com.example.sharen.data.repository

import com.example.sharen.data.local.dao.ProductDao
import com.example.sharen.data.local.entity.ProductEntity
import com.example.sharen.domain.model.Product
import com.example.sharen.domain.model.Season
import com.example.sharen.domain.model.Gender
import com.example.sharen.domain.repository.ProductRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * پیاده‌سازی Repository برای مدیریت محصولات
 */
@Singleton
class ProductRepositoryImpl @Inject constructor(
    private val productDao: ProductDao
) : ProductRepository {

    override fun getAllProducts(): Flow<List<Product>> {
        return productDao.getAllProducts().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun getProductById(productId: String): Result<Product?> {
        return try {
            val productEntity = productDao.getProductById(productId)
            val product = productEntity?.toDomainModel()
            Result.success(product)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getProductsByCategory(categoryId: String): Flow<List<Product>> {
        return productDao.getProductsByCategory(categoryId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getProductsByBrand(brandId: String): Flow<List<Product>> {
        return productDao.getProductsByBrand(brandId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun searchProducts(query: String): Flow<List<Product>> {
        return productDao.searchProducts(query).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getProductsByStockStatus(inStock: Boolean): Flow<List<Product>> {
        return if (inStock) {
            productDao.getProductsInStock().map { entities ->
                entities.map { it.toDomainModel() }
            }
        } else {
            productDao.getProductsOutOfStock().map { entities ->
                entities.map { it.toDomainModel() }
            }
        }
    }

    override fun getLowStockProducts(): Flow<List<Product>> {
        return productDao.getLowStockProducts().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getOutOfStockProducts(): Flow<List<Product>> {
        return productDao.getProductsOutOfStock().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun addProduct(product: Product): Result<Product> {
        return try {
            val productEntity = ProductEntity.fromDomainModel(product)
            productDao.insertProduct(productEntity)
            Result.success(product)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateProduct(product: Product): Result<Product> {
        return try {
            val productEntity = ProductEntity.fromDomainModel(product)
            productDao.updateProduct(productEntity)
            Result.success(product)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteProduct(productId: String): Result<Unit> {
        return try {
            productDao.deleteProduct(productId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateProductStock(productId: String, newStock: Int): Result<Unit> {
        return try {
            productDao.updateProductStock(productId, newStock)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun decreaseStock(productId: String, quantity: Int): Result<Unit> {
        return try {
            val product = productDao.getProductById(productId)
            if (product != null) {
                val newStock = maxOf(0, product.stock - quantity)
                productDao.updateProductStock(productId, newStock)
                Result.success(Unit)
            } else {
                Result.failure(Exception("محصول یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun increaseStock(productId: String, quantity: Int): Result<Unit> {
        return try {
            val product = productDao.getProductById(productId)
            if (product != null) {
                val newStock = product.stock + quantity
                productDao.updateProductStock(productId, newStock)
                Result.success(Unit)
            } else {
                Result.failure(Exception("محصول یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun getProductsByPriceRange(minPrice: Long, maxPrice: Long): Flow<List<Product>> {
        return productDao.getProductsByPriceRange(minPrice, maxPrice).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getActiveProducts(): Flow<List<Product>> {
        return productDao.getActiveProducts().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getInactiveProducts(): Flow<List<Product>> {
        return productDao.getInactiveProducts().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getProductsBySeason(season: Season): Flow<List<Product>> {
        return productDao.getProductsBySeason(season.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getProductsByGender(gender: Gender): Flow<List<Product>> {
        return productDao.getProductsByGender(gender.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getBestSellingProducts(limit: Int): Flow<List<Product>> {
        return productDao.getBestSellingProducts(limit).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getNewProducts(daysSinceAdded: Int): Flow<List<Product>> {
        val cutoffDate = System.currentTimeMillis() - (daysSinceAdded * 24 * 60 * 60 * 1000)
        return productDao.getNewProducts(cutoffDate).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
}