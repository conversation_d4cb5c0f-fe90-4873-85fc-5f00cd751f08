<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_details" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_product_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_details_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="360" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="41" endOffset="53"/></Target><Target id="@+id/collapsingToolbar" view="com.google.android.material.appbar.CollapsingToolbarLayout"><Expressions/><location startLine="14" startOffset="8" endLine="39" endOffset="68"/></Target><Target id="@+id/ivProductImage" view="ImageView"><Expressions/><location startLine="24" startOffset="12" endLine="31" endOffset="67"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="33" startOffset="12" endLine="37" endOffset="47"/></Target><Target id="@+id/tvProductName" view="TextView"><Expressions/><location startLine="74" startOffset="20" endLine="82" endOffset="48"/></Target><Target id="@+id/tvProductCode" view="TextView"><Expressions/><location startLine="112" startOffset="24" endLine="119" endOffset="49"/></Target><Target id="@+id/tvBarcode" view="TextView"><Expressions/><location startLine="136" startOffset="24" endLine="143" endOffset="51"/></Target><Target id="@+id/tvCategory" view="TextView"><Expressions/><location startLine="169" startOffset="20" endLine="176" endOffset="51"/></Target><Target id="@+id/tvPurchasePrice" view="TextView"><Expressions/><location startLine="206" startOffset="24" endLine="213" endOffset="56"/></Target><Target id="@+id/tvSellingPrice" view="TextView"><Expressions/><location startLine="230" startOffset="24" endLine="238" endOffset="56"/></Target><Target id="@+id/tvStock" view="TextView"><Expressions/><location startLine="270" startOffset="24" endLine="277" endOffset="49"/></Target><Target id="@+id/tvMinimumStock" view="TextView"><Expressions/><location startLine="294" startOffset="24" endLine="301" endOffset="48"/></Target><Target id="@+id/tvDescription" view="TextView"><Expressions/><location startLine="327" startOffset="20" endLine="334" endOffset="71"/></Target><Target id="@+id/fabEdit" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="344" startOffset="4" endLine="351" endOffset="56"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="353" startOffset="4" endLine="358" endOffset="35"/></Target></Targets></Layout>