package com.example.sharen.data.repository;

import com.example.sharen.data.remote.InstallmentRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InstallmentRepositoryImpl_Factory implements Factory<InstallmentRepositoryImpl> {
  private final Provider<InstallmentRemoteDataSource> remoteDataSourceProvider;

  public InstallmentRepositoryImpl_Factory(
      Provider<InstallmentRemoteDataSource> remoteDataSourceProvider) {
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public InstallmentRepositoryImpl get() {
    return newInstance(remoteDataSourceProvider.get());
  }

  public static InstallmentRepositoryImpl_Factory create(
      Provider<InstallmentRemoteDataSource> remoteDataSourceProvider) {
    return new InstallmentRepositoryImpl_Factory(remoteDataSourceProvider);
  }

  public static InstallmentRepositoryImpl newInstance(
      InstallmentRemoteDataSource remoteDataSource) {
    return new InstallmentRepositoryImpl(remoteDataSource);
  }
}
