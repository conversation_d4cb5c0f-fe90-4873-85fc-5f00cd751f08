package com.example.sharen.ui.invoices;

import androidx.annotation.NonNull;
import androidx.navigation.ActionOnlyNavDirections;
import androidx.navigation.NavDirections;
import com.example.sharen.R;

public class InvoicesFragmentDirections {
  private InvoicesFragmentDirections() {
  }

  @NonNull
  public static NavDirections actionInvoicesToAddInvoice() {
    return new ActionOnlyNavDirections(R.id.action_invoices_to_addInvoice);
  }

  @NonNull
  public static NavDirections actionInvoicesToInvoiceDetails() {
    return new ActionOnlyNavDirections(R.id.action_invoices_to_invoiceDetails);
  }
}
