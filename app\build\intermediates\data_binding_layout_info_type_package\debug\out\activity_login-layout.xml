<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/tv_login_title" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="32" endOffset="60"/></Target><Target id="@+id/til_email" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="34" startOffset="4" endLine="50" endOffset="59"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="45" startOffset="8" endLine="49" endOffset="50"/></Target><Target id="@+id/til_password" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="52" startOffset="4" endLine="69" endOffset="59"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="64" startOffset="8" endLine="68" endOffset="46"/></Target><Target id="@+id/tv_forgot_password" view="TextView"><Expressions/><location startLine="71" startOffset="4" endLine="79" endOffset="65"/></Target><Target id="@+id/btn_login" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="81" startOffset="4" endLine="91" endOffset="71"/></Target><Target id="@+id/layout_register" view="LinearLayout"><Expressions/><location startLine="93" startOffset="4" endLine="118" endOffset="18"/></Target><Target id="@+id/tv_no_account" view="TextView"><Expressions/><location startLine="103" startOffset="8" endLine="108" endOffset="55"/></Target><Target id="@+id/tv_register" view="TextView"><Expressions/><location startLine="110" startOffset="8" endLine="117" endOffset="38"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="120" startOffset="4" endLine="128" endOffset="51"/></Target></Targets></Layout>