<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_sales_invoice" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_sales_invoice.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_sales_invoice_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="352" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="55"/></Target><Target id="@+id/tvInvoiceNumber" view="TextView"><Expressions/><location startLine="74" startOffset="24" endLine="79" endOffset="51"/></Target><Target id="@+id/tvCustomerName" view="TextView"><Expressions/><location startLine="96" startOffset="24" endLine="102" endOffset="71"/></Target><Target id="@+id/btnSelectCustomer" view="Button"><Expressions/><location startLine="104" startOffset="24" endLine="110" endOffset="64"/></Target><Target id="@+id/btnAddItem" view="Button"><Expressions/><location startLine="143" startOffset="24" endLine="151" endOffset="59"/></Target><Target id="@+id/rvInvoiceItems" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="161" startOffset="20" endLine="166" endOffset="77"/></Target><Target id="@+id/emptyView" view="TextView"><Expressions/><location startLine="168" startOffset="20" endLine="175" endOffset="54"/></Target><Target id="@+id/tvSubtotal" view="TextView"><Expressions/><location startLine="221" startOffset="24" endLine="227" endOffset="56"/></Target><Target id="@+id/etDiscount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="251" startOffset="28" endLine="257" endOffset="50"/></Target><Target id="@+id/etTax" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="282" startOffset="28" endLine="288" endOffset="50"/></Target><Target id="@+id/btnRecalculate" view="Button"><Expressions/><location startLine="292" startOffset="20" endLine="298" endOffset="60"/></Target><Target id="@+id/tvFinalAmount" view="TextView"><Expressions/><location startLine="320" startOffset="24" endLine="328" endOffset="56"/></Target><Target id="@+id/btnSave" view="Button"><Expressions/><location startLine="334" startOffset="12" endLine="340" endOffset="42"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="345" startOffset="4" endLine="350" endOffset="35"/></Target></Targets></Layout>