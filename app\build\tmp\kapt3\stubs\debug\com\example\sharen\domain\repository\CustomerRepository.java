package com.example.sharen.domain.repository;

/**
 * Repository Interface برای مدیریت مشتریان
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0011\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J*\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000fH&J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000fH&J,\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0013\u0010\rJ\u001c\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00100\u000f2\u0006\u0010\n\u001a\u00020\u000bH&J*\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0018\u0010\rJ$\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH&J\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010\u001e\u001a\u00020\u001fH&J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010!\u001a\u00020\u000bH&J\u0014\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000fH&J\u001e\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\b\b\u0002\u0010$\u001a\u00020%H&J:\u0010&\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\u001bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b*\u0010+J:\u0010,\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\u001bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b-\u0010+J\u001c\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010/\u001a\u00020\u000bH&J*\u00100\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b1\u0010\u0007J2\u00102\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u00103\u001a\u00020(H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u00105J2\u00106\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u00107\u001a\u00020(H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b8\u00105\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00069"}, d2 = {"Lcom/example/sharen/domain/repository/CustomerRepository;", "", "addCustomer", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Customer;", "customer", "addCustomer-gIAlu-s", "(Lcom/example/sharen/domain/model/Customer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCustomer", "", "customerId", "", "deleteCustomer-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveCustomers", "Lkotlinx/coroutines/flow/Flow;", "", "getAllCustomers", "getCustomerById", "getCustomerById-gIAlu-s", "getCustomerPurchaseHistory", "Lcom/example/sharen/domain/repository/CustomerPurchase;", "getCustomerStatistics", "Lcom/example/sharen/domain/repository/CustomerStatistics;", "getCustomerStatistics-gIAlu-s", "getCustomersByDateRange", "startDate", "Ljava/util/Date;", "endDate", "getCustomersByDebtStatus", "hasDebt", "", "getCustomersBySeller", "sellerId", "getCustomersWithLowCredit", "getInactiveCustomers", "daysSinceLastPurchase", "", "recordPayment", "amount", "", "date", "recordPayment-BWLJW6A", "(Ljava/lang/String;JLjava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recordPurchase", "recordPurchase-BWLJW6A", "searchCustomers", "query", "updateCustomer", "updateCustomer-gIAlu-s", "updateCustomerCredit", "creditLimit", "updateCustomerCredit-0E7RQCE", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCustomerDebt", "debtAmount", "updateCustomerDebt-0E7RQCE", "app_debug"})
public abstract interface CustomerRepository {
    
    /**
     * دریافت تمام مشتریان
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getAllCustomers();
    
    /**
     * دریافت مشتریان فروشنده
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getCustomersBySeller(@org.jetbrains.annotations.NotNull
    java.lang.String sellerId);
    
    /**
     * جستجوی مشتریان
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> searchCustomers(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * فیلتر مشتریان بر اساس بدهی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getCustomersByDebtStatus(boolean hasDebt);
    
    /**
     * دریافت مشتریان با اعتبار کم
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getCustomersWithLowCredit();
    
    /**
     * دریافت تاریخچه خرید مشتری
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.repository.CustomerPurchase>> getCustomerPurchaseHistory(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    /**
     * دریافت مشتریان بر اساس بازه زمانی ثبت‌نام
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getCustomersByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate);
    
    /**
     * دریافت مشتریان فعال (خرید در 30 روز گذشته)
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getActiveCustomers();
    
    /**
     * دریافت مشتریان غیرفعال
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Customer>> getInactiveCustomers(int daysSinceLastPurchase);
    
    /**
     * Repository Interface برای مدیریت مشتریان
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}