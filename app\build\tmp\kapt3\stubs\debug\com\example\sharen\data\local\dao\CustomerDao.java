package com.example.sharen.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\b\n\u0002\b\u000e\bg\u0018\u00002\u00020\u0001J\u0011\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u0019\u0010\u0005\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\nH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u001c\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\r2\u0006\u0010\u000f\u001a\u00020\u0010H\'J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\rH\'J\u001b\u0010\u0012\u001a\u0004\u0018\u00010\u00072\u0006\u0010\t\u001a\u00020\nH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u001b\u0010\u0013\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0014\u001a\u00020\nH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u000e0\r2\u0006\u0010\t\u001a\u00020\nH\'J\u0016\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\r2\u0006\u0010\t\u001a\u00020\nH\'J$\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\r2\u0006\u0010\u001a\u001a\u00020\u00102\u0006\u0010\u001b\u001a\u00020\u0010H\'J\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\r2\u0006\u0010\u001d\u001a\u00020\nH\'J\u0014\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\rH\'J\u0014\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\rH\'J\u0014\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u000e0\rH\'J\u0014\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\rH\'J\u001c\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\r2\u0006\u0010\u000f\u001a\u00020\u0010H\'J\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\r2\u0006\u0010$\u001a\u00020%H\'J\u0019\u0010&\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u001f\u0010\'\u001a\u00020\u00032\f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00070\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010)J\u001c\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000e0\r2\u0006\u0010+\u001a\u00020\nH\'J\u0019\u0010,\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ+\u0010-\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\n2\u0006\u0010.\u001a\u00020\u00102\b\b\u0002\u0010/\u001a\u00020\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00100J+\u00101\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\n2\u0006\u00102\u001a\u00020\u00102\b\b\u0002\u0010/\u001a\u00020\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u00100\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u00063"}, d2 = {"Lcom/example/sharen/data/local/dao/CustomerDao;", "", "deleteAllCustomers", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCustomer", "customer", "Lcom/example/sharen/data/local/entity/CustomerEntity;", "(Lcom/example/sharen/data/local/entity/CustomerEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "customerId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveCustomers", "Lkotlinx/coroutines/flow/Flow;", "", "cutoffDate", "", "getAllCustomers", "getCustomerById", "getCustomerByUserId", "userId", "getCustomerPurchaseHistory", "Lcom/example/sharen/data/local/dao/CustomerPurchaseEntity;", "getCustomerWithUser", "Lcom/example/sharen/data/local/relation/CustomerWithUser;", "getCustomersByDateRange", "startDate", "endDate", "getCustomersBySeller", "sellerId", "getCustomersWithDebt", "getCustomersWithLowCredit", "getCustomersWithUsers", "getCustomersWithoutDebt", "getInactiveCustomers", "getTopCustomers", "limit", "", "insertCustomer", "insertCustomers", "customers", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchCustomers", "query", "updateCustomer", "updateCustomerCredit", "creditLimit", "updatedAt", "(Ljava/lang/String;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCustomerDebt", "debtAmount", "app_debug"})
@androidx.room.Dao
public abstract interface CustomerDao {
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.CustomerEntity customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertCustomers(@org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.data.local.entity.CustomerEntity> customers, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.CustomerEntity customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.CustomerEntity customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM customers WHERE id = :customerId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE customers SET creditLimit = :creditLimit, updatedAt = :updatedAt WHERE id = :customerId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCustomerCredit(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, long creditLimit, long updatedAt, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE customers SET totalDebt = :debtAmount, updatedAt = :updatedAt WHERE id = :customerId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCustomerDebt(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, long debtAmount, long updatedAt, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE id = :customerId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomerById(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.CustomerEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE userId = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomerByUserId(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.CustomerEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customers ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getAllCustomers();
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE userId = :sellerId ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getCustomersBySeller(@org.jetbrains.annotations.NotNull
    java.lang.String sellerId);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE name LIKE \'%\' || :query || \'%\' OR phone LIKE \'%\' || :query || \'%\' OR address LIKE \'%\' || :query || \'%\' ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> searchCustomers(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE totalDebt > 0 ORDER BY totalDebt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getCustomersWithDebt();
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE totalDebt = 0 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getCustomersWithoutDebt();
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE (creditLimit - totalDebt) < (creditLimit * 0.2) ORDER BY (creditLimit - totalDebt) ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getCustomersWithLowCredit();
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getCustomersByDateRange(long startDate, long endDate);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE lastPurchaseDate > :cutoffDate ORDER BY lastPurchaseDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getActiveCustomers(long cutoffDate);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE lastPurchaseDate < :cutoffDate OR lastPurchaseDate IS NULL ORDER BY lastPurchaseDate ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getInactiveCustomers(long cutoffDate);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE totalPurchases > 0 ORDER BY totalPurchases DESC LIMIT :limit")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getTopCustomers(int limit);
    
    @androidx.room.Transaction
    @androidx.room.Query(value = "SELECT * FROM customers WHERE id = :customerId")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharen.data.local.relation.CustomerWithUser> getCustomerWithUser(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    @androidx.room.Transaction
    @androidx.room.Query(value = "SELECT * FROM customers")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.relation.CustomerWithUser>> getCustomersWithUsers();
    
    @androidx.room.Query(value = "DELETE FROM customers")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAllCustomers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT i.id as invoiceId, i.finalAmount as amount, i.createdAt as date, i.status as status FROM invoices i WHERE i.customerId = :customerId ORDER BY i.createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.dao.CustomerPurchaseEntity>> getCustomerPurchaseHistory(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}