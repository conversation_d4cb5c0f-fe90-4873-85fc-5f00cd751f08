package com.example.sharen.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u0011\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u001b\u0010\f\u001a\u0004\u0018\u00010\u00072\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ\u001b\u0010\u0010\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0011\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\n2\u0006\u0010\r\u001a\u00020\u000eH\'J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u0014\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u000b0\nH\'J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u0017\u001a\u00020\u0018H\'J\u0019\u0010\u0019\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u001f\u0010\u001a\u001a\u00020\u00032\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001cJ\u0019\u0010\u001d\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\b\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u001e"}, d2 = {"Lcom/example/sharen/data/local/dao/CustomerDao;", "", "deleteAllCustomers", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCustomer", "customer", "Lcom/example/sharen/data/local/entity/CustomerEntity;", "(Lcom/example/sharen/data/local/entity/CustomerEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllCustomers", "Lkotlinx/coroutines/flow/Flow;", "", "getCustomerById", "customerId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCustomerByUserId", "userId", "getCustomerWithUser", "Lcom/example/sharen/data/local/relation/CustomerWithUser;", "getCustomersWithDebt", "getCustomersWithUsers", "getTopCustomers", "limit", "", "insertCustomer", "insertCustomers", "customers", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCustomer", "app_debug"})
@androidx.room.Dao
public abstract interface CustomerDao {
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.CustomerEntity customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertCustomers(@org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.data.local.entity.CustomerEntity> customers, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.CustomerEntity customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.CustomerEntity customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE id = :customerId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomerById(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.CustomerEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE userId = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomerByUserId(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.CustomerEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM customers")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getAllCustomers();
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE debtAmount > 0")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getCustomersWithDebt();
    
    @androidx.room.Query(value = "SELECT * FROM customers WHERE totalPurchases > 0 ORDER BY totalPurchases DESC LIMIT :limit")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.CustomerEntity>> getTopCustomers(int limit);
    
    @androidx.room.Transaction
    @androidx.room.Query(value = "SELECT * FROM customers WHERE id = :customerId")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharen.data.local.relation.CustomerWithUser> getCustomerWithUser(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    @androidx.room.Transaction
    @androidx.room.Query(value = "SELECT * FROM customers")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.relation.CustomerWithUser>> getCustomersWithUsers();
    
    @androidx.room.Query(value = "DELETE FROM customers")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAllCustomers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}