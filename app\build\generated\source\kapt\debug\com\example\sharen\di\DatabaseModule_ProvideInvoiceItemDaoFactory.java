package com.example.sharen.di;

import com.example.sharen.data.local.SharenDatabase;
import com.example.sharen.data.local.dao.InvoiceItemDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideInvoiceItemDaoFactory implements Factory<InvoiceItemDao> {
  private final Provider<SharenDatabase> databaseProvider;

  public DatabaseModule_ProvideInvoiceItemDaoFactory(Provider<SharenDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public InvoiceItemDao get() {
    return provideInvoiceItemDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideInvoiceItemDaoFactory create(
      Provider<SharenDatabase> databaseProvider) {
    return new DatabaseModule_ProvideInvoiceItemDaoFactory(databaseProvider);
  }

  public static InvoiceItemDao provideInvoiceItemDao(SharenDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideInvoiceItemDao(database));
  }
}
