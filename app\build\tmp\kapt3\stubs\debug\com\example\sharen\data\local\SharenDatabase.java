package com.example.sharen.data.local;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&\u00a8\u0006\u0007"}, d2 = {"Lcom/example/sharen/data/local/SharenDatabase;", "Landroidx/room/RoomDatabase;", "()V", "customerDao", "Lcom/example/sharen/data/local/dao/CustomerDao;", "userDao", "Lcom/example/sharen/data/local/dao/UserDao;", "app_debug"})
@androidx.room.Database(entities = {com.example.sharen.data.local.entity.UserEntity.class, com.example.sharen.data.local.entity.CustomerEntity.class, com.example.sharen.data.local.entity.SellerEntity.class, com.example.sharen.data.local.entity.ProductEntity.class, com.example.sharen.data.local.entity.CategoryEntity.class, com.example.sharen.data.local.entity.InvoiceEntity.class, com.example.sharen.data.local.entity.InvoiceItemEntity.class}, version = 1, exportSchema = true)
@androidx.room.TypeConverters(value = {com.example.sharen.data.local.converter.DateConverter.class})
public abstract class SharenDatabase extends androidx.room.RoomDatabase {
    
    public SharenDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.UserDao userDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.CustomerDao customerDao();
}