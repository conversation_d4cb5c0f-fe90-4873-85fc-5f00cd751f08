package com.example.sharen.data.local;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00152\u00020\u0001:\u0001\u0015B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&J\b\u0010\r\u001a\u00020\u000eH&J\b\u0010\u000f\u001a\u00020\u0010H&J\b\u0010\u0011\u001a\u00020\u0012H&J\b\u0010\u0013\u001a\u00020\u0014H&\u00a8\u0006\u0016"}, d2 = {"Lcom/example/sharen/data/local/SharenDatabase;", "Landroidx/room/RoomDatabase;", "()V", "categoryDao", "Lcom/example/sharen/data/local/dao/CategoryDao;", "customerDao", "Lcom/example/sharen/data/local/dao/CustomerDao;", "installmentDao", "Lcom/example/sharen/data/local/dao/InstallmentDao;", "invoiceDao", "Lcom/example/sharen/data/local/dao/InvoiceDao;", "invoiceItemDao", "Lcom/example/sharen/data/local/dao/InvoiceItemDao;", "paymentDao", "Lcom/example/sharen/data/local/dao/PaymentDao;", "productDao", "Lcom/example/sharen/data/local/dao/ProductDao;", "sellerDao", "Lcom/example/sharen/data/local/dao/SellerDao;", "userDao", "Lcom/example/sharen/data/local/dao/UserDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.example.sharen.data.local.entity.UserEntity.class, com.example.sharen.data.local.entity.CustomerEntity.class, com.example.sharen.data.local.entity.SellerEntity.class, com.example.sharen.data.local.entity.ProductEntity.class, com.example.sharen.data.local.entity.CategoryEntity.class, com.example.sharen.data.local.entity.InvoiceEntity.class, com.example.sharen.data.local.entity.InvoiceItemEntity.class, com.example.sharen.data.local.entity.PaymentEntity.class, com.example.sharen.data.local.entity.InstallmentEntity.class, com.example.sharen.data.local.entity.TransactionEntity.class, com.example.sharen.data.local.entity.OrderEntity.class, com.example.sharen.data.local.entity.OrderItemEntity.class}, version = 2, exportSchema = true)
@androidx.room.TypeConverters(value = {com.example.sharen.data.local.converter.DateConverter.class})
public abstract class SharenDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DATABASE_NAME = "sharen_database";
    @kotlin.jvm.Volatile
    @org.jetbrains.annotations.Nullable
    private static volatile com.example.sharen.data.local.SharenDatabase INSTANCE;
    
    /**
     * Migration از version 1 به 2
     */
    @org.jetbrains.annotations.NotNull
    private static final androidx.room.migration.Migration MIGRATION_1_2 = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.data.local.SharenDatabase.Companion Companion = null;
    
    public SharenDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.UserDao userDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.CustomerDao customerDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.SellerDao sellerDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.ProductDao productDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.CategoryDao categoryDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.InvoiceDao invoiceDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.InvoiceItemDao invoiceItemDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.PaymentDao paymentDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.example.sharen.data.local.dao.InstallmentDao installmentDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u000e"}, d2 = {"Lcom/example/sharen/data/local/SharenDatabase$Companion;", "", "()V", "DATABASE_NAME", "", "INSTANCE", "Lcom/example/sharen/data/local/SharenDatabase;", "MIGRATION_1_2", "Landroidx/room/migration/Migration;", "getMIGRATION_1_2", "()Landroidx/room/migration/Migration;", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.sharen.data.local.SharenDatabase getDatabase(@org.jetbrains.annotations.NotNull
        android.content.Context context) {
            return null;
        }
        
        /**
         * Migration از version 1 به 2
         */
        @org.jetbrains.annotations.NotNull
        public final androidx.room.migration.Migration getMIGRATION_1_2() {
            return null;
        }
    }
}