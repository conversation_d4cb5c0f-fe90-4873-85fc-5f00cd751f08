package com.example.sharen.domain.repository;

/**
 * Repository Interface برای مدیریت پرداخت‌ها
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J*\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000b\u0010\fJ*\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000f\u0010\fJ\u0014\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u0011H&J,\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\fJ>\u0010\u0015\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u00160\u00032\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001a\u0010\u001bJ\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u00112\u0006\u0010\u001d\u001a\u00020\nH&J$\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u00112\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018H&J\u001c\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u00112\u0006\u0010 \u001a\u00020\nH&J\u001c\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u00112\u0006\u0010\"\u001a\u00020#H&J\u001c\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u00112\u0006\u0010%\u001a\u00020&H&J\u001e\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00120\u00112\b\b\u0002\u0010(\u001a\u00020)H&J*\u0010*\u001a\b\u0012\u0004\u0012\u00020+0\u00032\u0006\u0010\u001d\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b,\u0010\fJ2\u0010-\u001a\b\u0012\u0004\u0012\u00020+0\u00032\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b.\u0010\u001bJ2\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\t\u001a\u00020\n2\u0006\u00100\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b1\u00102J*\u00103\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u0010\u0007\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00065"}, d2 = {"Lcom/example/sharen/domain/repository/PaymentRepository;", "", "addPayment", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Payment;", "payment", "addPayment-gIAlu-s", "(Lcom/example/sharen/domain/model/Payment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "confirmPayment", "paymentId", "", "confirmPayment-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePayment", "", "deletePayment-gIAlu-s", "getAllPayments", "Lkotlinx/coroutines/flow/Flow;", "", "getPaymentById", "getPaymentById-gIAlu-s", "getPaymentStatistics", "", "startDate", "Ljava/util/Date;", "endDate", "getPaymentStatistics-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPaymentsByCustomer", "customerId", "getPaymentsByDateRange", "getPaymentsByInvoice", "invoiceId", "getPaymentsByMethod", "method", "Lcom/example/sharen/domain/model/PaymentMethod;", "getPaymentsByStatus", "status", "Lcom/example/sharen/domain/model/PaymentStatus;", "getRecentPayments", "limit", "", "getTotalPaymentsByCustomer", "", "getTotalPaymentsByCustomer-gIAlu-s", "getTotalPaymentsByDateRange", "getTotalPaymentsByDateRange-0E7RQCE", "rejectPayment", "reason", "rejectPayment-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePayment", "updatePayment-gIAlu-s", "app_debug"})
public abstract interface PaymentRepository {
    
    /**
     * دریافت تمام پرداخت‌ها
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getAllPayments();
    
    /**
     * دریافت پرداخت‌های مشتری
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getPaymentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    /**
     * دریافت پرداخت‌های فاکتور
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getPaymentsByInvoice(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId);
    
    /**
     * دریافت پرداخت‌ها در بازه زمانی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getPaymentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate);
    
    /**
     * دریافت پرداخت‌ها بر اساس روش پرداخت
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getPaymentsByMethod(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.PaymentMethod method);
    
    /**
     * دریافت پرداخت‌ها بر اساس وضعیت
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getPaymentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.PaymentStatus status);
    
    /**
     * دریافت پرداخت‌های اخیر
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Payment>> getRecentPayments(int limit);
    
    /**
     * Repository Interface برای مدیریت پرداخت‌ها
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}