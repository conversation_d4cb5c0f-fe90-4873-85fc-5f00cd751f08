<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_installment" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_installment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_installment_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="51"/></Target><Target id="@+id/textViewTotalAmount" view="TextView"><Expressions/><location startLine="21" startOffset="12" endLine="27" endOffset="39"/></Target><Target id="@+id/chipStatus" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="29" startOffset="12" endLine="34" endOffset="35"/></Target><Target id="@+id/textViewDueDate" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="41"/></Target><Target id="@+id/textViewPaidAmount" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="57" endOffset="44"/></Target><Target id="@+id/textViewNotes" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="69" endOffset="54"/></Target></Targets></Layout>