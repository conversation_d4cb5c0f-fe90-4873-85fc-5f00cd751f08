<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_register" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_register.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_register_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="202" endOffset="12"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="14" startOffset="8" endLine="23" endOffset="55"/></Target><Target id="@+id/tv_register_title" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="37" endOffset="64"/></Target><Target id="@+id/til_name" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="39" startOffset="8" endLine="55" endOffset="63"/></Target><Target id="@+id/et_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="50" startOffset="12" endLine="54" endOffset="52"/></Target><Target id="@+id/til_email" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="57" startOffset="8" endLine="73" endOffset="63"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="68" startOffset="12" endLine="72" endOffset="54"/></Target><Target id="@+id/til_phone" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="75" startOffset="8" endLine="91" endOffset="63"/></Target><Target id="@+id/et_phone" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="86" startOffset="12" endLine="90" endOffset="43"/></Target><Target id="@+id/til_password" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="93" startOffset="8" endLine="110" endOffset="63"/></Target><Target id="@+id/et_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="105" startOffset="12" endLine="109" endOffset="50"/></Target><Target id="@+id/til_confirm_password" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="112" startOffset="8" endLine="129" endOffset="63"/></Target><Target id="@+id/et_confirm_password" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="124" startOffset="12" endLine="128" endOffset="50"/></Target><Target id="@+id/til_referrer_code" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="131" startOffset="8" endLine="147" endOffset="63"/></Target><Target id="@+id/et_referrer_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="142" startOffset="12" endLine="146" endOffset="42"/></Target><Target id="@+id/btn_register" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="149" startOffset="8" endLine="159" endOffset="74"/></Target><Target id="@+id/layout_login" view="LinearLayout"><Expressions/><location startLine="161" startOffset="8" endLine="189" endOffset="22"/></Target><Target id="@+id/tv_have_account" view="TextView"><Expressions/><location startLine="174" startOffset="12" endLine="179" endOffset="59"/></Target><Target id="@+id/tv_login" view="TextView"><Expressions/><location startLine="181" startOffset="12" endLine="188" endOffset="42"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="191" startOffset="8" endLine="199" endOffset="55"/></Target></Targets></Layout>