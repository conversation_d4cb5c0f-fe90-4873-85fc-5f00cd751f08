package com.example.sharen.data.local.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.sharen.data.model.User
import com.example.sharen.data.model.UserRole
import java.util.Date

@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val email: String,
    val phone: String,
    val role: String,
    val isApproved: Boolean = false,
    @ColumnInfo(name = "is_current")
    val isCurrent: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val imageUrl: String? = null,
    val referrerId: String? = null,
    val referrerCode: String? = null
) {
    fun toUser(): User = User(
        id = id,
        email = email,
        name = name,
        phone = phone,
        role = UserRole.valueOf(role),
        isApproved = isApproved,
        imageUrl = imageUrl,
        referrerId = referrerId,
        referrerCode = referrerCode,
        createdAt = Date(createdAt),
        updatedAt = Date(updatedAt)
    )
    
    companion object {
        fun fromUser(user: User): UserEntity = UserEntity(
            id = user.id,
            email = user.email,
            name = user.name,
            phone = user.phone,
            role = user.role.name,
            isApproved = user.isApproved,
            imageUrl = user.imageUrl,
            referrerId = user.referrerId,
            referrerCode = user.referrerCode,
            createdAt = user.createdAt.time,
            updatedAt = user.updatedAt.time
        )
    }
} 