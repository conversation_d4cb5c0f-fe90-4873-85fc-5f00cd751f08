package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Payment
import com.example.sharen.domain.model.PaymentMethod
import com.example.sharen.domain.model.PaymentStatus
import java.util.Date

@Entity(
    tableName = "payments",
    foreignKeys = [
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = InvoiceEntity::class,
            parentColumns = ["id"],
            childColumns = ["invoiceId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index("customerId"),
        Index("invoiceId")
    ]
)
data class PaymentEntity(
    @PrimaryKey
    val id: String,
    val customerId: String,
    val invoiceId: String? = null,
    val amount: Long,
    val method: String,
    val status: String,
    val description: String? = null,
    val referenceNumber: String? = null,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toDomainModel(): Payment {
        return Payment(
            id = id,
            customerId = customerId,
            invoiceId = invoiceId,
            amount = amount,
            method = PaymentMethod.valueOf(method),
            status = PaymentStatus.valueOf(status),
            description = description,
            referenceNumber = referenceNumber,
            createdAt = Date(createdAt),
            updatedAt = Date(updatedAt)
        )
    }

    companion object {
        fun fromDomainModel(payment: Payment): PaymentEntity {
            return PaymentEntity(
                id = payment.id,
                customerId = payment.customerId,
                invoiceId = payment.invoiceId,
                amount = payment.amount,
                method = payment.method.name,
                status = payment.status.name,
                description = payment.description,
                referenceNumber = payment.referenceNumber,
                createdAt = payment.createdAt.time,
                updatedAt = payment.updatedAt.time
            )
        }
    }
}