package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentMethod
import com.example.sharen.data.model.PaymentStatus
import java.util.Date

@Entity(tableName = "payments")
data class PaymentEntity(
    @PrimaryKey
    val id: String,
    val customerId: String,
    val orderId: String?,
    val amount: Double,
    val date: Long,
    val status: String,
    val method: String,
    val referenceNumber: String?,
    val notes: String?,
    val createdAt: Long,
    val updatedAt: Long,
    val createdBy: String?,
    val updatedBy: String?
) {
    fun toPayment() = Payment(
        id = id,
        customerId = customerId,
        orderId = orderId,
        amount = amount,
        date = Date(date),
        status = PaymentStatus.valueOf(status),
        method = PaymentMethod.valueOf(method),
        referenceNumber = referenceNumber,
        notes = notes,
        createdAt = Date(this.createdAt),
        updatedAt = Date(this.updatedAt),
        createdBy = createdBy,
        updatedBy = updatedBy
    )

    companion object {
        fun fromPayment(payment: Payment) = PaymentEntity(
            id = payment.id,
            customerId = payment.customerId,
            orderId = payment.orderId,
            amount = payment.amount,
            date = payment.date.time,
            status = payment.status.name,
            method = payment.method.name,
            referenceNumber = payment.referenceNumber,
            notes = payment.notes,
            createdAt = payment.createdAt.time,
            updatedAt = payment.updatedAt.time,
            createdBy = payment.createdBy,
            updatedBy = payment.updatedBy
        )
    }
}