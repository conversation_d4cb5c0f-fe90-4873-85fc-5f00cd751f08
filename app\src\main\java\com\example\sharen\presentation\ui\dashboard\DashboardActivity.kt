package com.example.sharen.presentation.ui.dashboard

import android.content.Intent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.sharen.core.base.BaseActivity
import com.example.sharen.databinding.ActivityDashboardBinding
import com.example.sharen.presentation.ui.customer.CustomerActivity
import com.example.sharen.presentation.ui.invoice.InvoiceActivity
import com.example.sharen.presentation.ui.product.ProductActivity
import com.example.sharen.presentation.ui.payment.PaymentActivity
import com.example.sharen.presentation.viewmodel.DashboardViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه داشبورد اصلی
 */
@AndroidEntryPoint
class DashboardActivity : BaseActivity<ActivityDashboardBinding>() {

    private val dashboardViewModel: DashboardViewModel by viewModels()

    override fun getViewBinding(): ActivityDashboardBinding {
        return ActivityDashboardBinding.inflate(layoutInflater)
    }

    override fun setupViews() {
        setupClickListeners()
        setupUI()
    }

    override fun observeData() {
        observeDashboardData()
    }

    private fun setupClickListeners() {
        binding.apply {
            // کارت‌های سریع
            cardCustomers.setOnClickListener {
                navigateToCustomers()
            }

            cardProducts.setOnClickListener {
                navigateToProducts()
            }

            cardInvoices.setOnClickListener {
                navigateToInvoices()
            }

            cardPayments.setOnClickListener {
                navigateToPayments()
            }

            // دکمه‌های عملیات سریع
            btnNewInvoice.setOnClickListener {
                navigateToNewInvoice()
            }

            btnNewCustomer.setOnClickListener {
                navigateToNewCustomer()
            }

            btnNewProduct.setOnClickListener {
                navigateToNewProduct()
            }

            btnReports.setOnClickListener {
                navigateToReports()
            }
        }
    }

    private fun setupUI() {
        // تنظیم عنوان
        binding.tvWelcome.text = "خوش آمدید"
        
        // تنظیم تاریخ امروز
        val today = java.text.SimpleDateFormat("yyyy/MM/dd", java.util.Locale("fa", "IR"))
            .format(java.util.Date())
        binding.tvDate.text = today
    }

    private fun observeDashboardData() {
        lifecycleScope.launch {
            dashboardViewModel.dashboardStats.collect { stats ->
                updateStatsCards(stats)
            }
        }

        lifecycleScope.launch {
            dashboardViewModel.isLoading.collect { isLoading ->
                if (isLoading) {
                    showLoading()
                } else {
                    hideLoading()
                }
            }
        }
    }

    private fun updateStatsCards(stats: DashboardStats) {
        binding.apply {
            // آمار مشتریان
            tvCustomersCount.text = stats.customersCount.toString()
            tvCustomersLabel.text = "مشتری"

            // آمار محصولات
            tvProductsCount.text = stats.productsCount.toString()
            tvProductsLabel.text = "محصول"

            // آمار فاکتورها
            tvInvoicesCount.text = stats.invoicesCount.toString()
            tvInvoicesLabel.text = "فاکتور"

            // آمار پرداخت‌ها
            tvPaymentsCount.text = stats.paymentsCount.toString()
            tvPaymentsLabel.text = "پرداخت"

            // آمار مالی
            tvTotalSales.text = stats.totalSales.toString()
            tvTotalDebt.text = stats.totalDebt.toString()
        }
    }

    private fun navigateToCustomers() {
        val intent = Intent(this, CustomerActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToProducts() {
        val intent = Intent(this, ProductActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToInvoices() {
        val intent = Intent(this, InvoiceActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToPayments() {
        val intent = Intent(this, PaymentActivity::class.java)
        startActivity(intent)
    }

    private fun navigateToNewInvoice() {
        val intent = Intent(this, InvoiceActivity::class.java)
        intent.putExtra("action", "new")
        startActivity(intent)
    }

    private fun navigateToNewCustomer() {
        val intent = Intent(this, CustomerActivity::class.java)
        intent.putExtra("action", "new")
        startActivity(intent)
    }

    private fun navigateToNewProduct() {
        val intent = Intent(this, ProductActivity::class.java)
        intent.putExtra("action", "new")
        startActivity(intent)
    }

    private fun navigateToReports() {
        // TODO: پیاده‌سازی صفحه گزارش‌ها
        showSuccess("صفحه گزارش‌ها به زودی اضافه می‌شود")
    }
}

/**
 * آمار داشبورد
 */
data class DashboardStats(
    val customersCount: Int = 0,
    val productsCount: Int = 0,
    val invoicesCount: Int = 0,
    val paymentsCount: Int = 0,
    val totalSales: Long = 0,
    val totalDebt: Long = 0,
    val todaySales: Long = 0,
    val todayPayments: Long = 0
)
