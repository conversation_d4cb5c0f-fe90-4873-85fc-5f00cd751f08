<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_payment" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_payment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_payment_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="81" endOffset="51"/></Target><Target id="@+id/amountTextView" view="TextView"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="47"/></Target><Target id="@+id/statusChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="25" startOffset="8" endLine="33" endOffset="36"/></Target><Target id="@+id/dateTextView" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="44" endOffset="44"/></Target><Target id="@+id/methodTextView" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="55" endOffset="43"/></Target><Target id="@+id/referenceTextView" view="TextView"><Expressions/><location startLine="57" startOffset="8" endLine="66" endOffset="45"/></Target><Target id="@+id/notesTextView" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="50"/></Target></Targets></Layout>