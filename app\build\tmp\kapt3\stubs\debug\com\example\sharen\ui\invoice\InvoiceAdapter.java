package com.example.sharen.ui.invoice;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u0012\u0012\u0004\u0012\u00020\u0002\u0012\b\u0012\u00060\u0003R\u00020\u00000\u0001:\u0002\u0014\u0015B#\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\f\u001a\u00020\u00062\n\u0010\r\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u000e\u001a\u00020\u000fH\u0016J\u001c\u0010\u0010\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u000fH\u0016R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/sharen/ui/invoice/InvoiceAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/sharen/data/model/Invoice;", "Lcom/example/sharen/ui/invoice/InvoiceAdapter$InvoiceViewHolder;", "onInvoiceClick", "Lkotlin/Function1;", "", "numberFormatter", "Ljava/text/NumberFormat;", "(Lkotlin/jvm/functions/Function1;Ljava/text/NumberFormat;)V", "dateFormatter", "Ljava/text/SimpleDateFormat;", "onBindViewHolder", "holder", "position", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "InvoiceDiffCallback", "InvoiceViewHolder", "app_debug"})
public final class InvoiceAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.sharen.data.model.Invoice, com.example.sharen.ui.invoice.InvoiceAdapter.InvoiceViewHolder> {
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function1<com.example.sharen.data.model.Invoice, kotlin.Unit> onInvoiceClick = null;
    @org.jetbrains.annotations.NotNull
    private final java.text.NumberFormat numberFormatter = null;
    @org.jetbrains.annotations.NotNull
    private final java.text.SimpleDateFormat dateFormatter = null;
    
    public InvoiceAdapter(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.example.sharen.data.model.Invoice, kotlin.Unit> onInvoiceClick, @org.jetbrains.annotations.NotNull
    java.text.NumberFormat numberFormatter) {
        super(null);
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.ui.invoice.InvoiceAdapter.InvoiceViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.example.sharen.ui.invoice.InvoiceAdapter.InvoiceViewHolder holder, int position) {
    }
    
    /**
     * برای مقایسه آیتم‌های لیست و بهینه‌سازی رندرینگ
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/example/sharen/ui/invoice/InvoiceAdapter$InvoiceDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/sharen/data/model/Invoice;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    public static final class InvoiceDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.sharen.data.model.Invoice> {
        
        public InvoiceDiffCallback() {
            super();
        }
        
        @java.lang.Override
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Invoice oldItem, @org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Invoice newItem) {
            return false;
        }
        
        @java.lang.Override
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Invoice oldItem, @org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Invoice newItem) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0002J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0010\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/sharen/ui/invoice/InvoiceAdapter$InvoiceViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "Lcom/example/sharen/databinding/ItemInvoiceBinding;", "(Lcom/example/sharen/ui/invoice/InvoiceAdapter;Lcom/example/sharen/databinding/ItemInvoiceBinding;)V", "bind", "", "invoice", "Lcom/example/sharen/data/model/Invoice;", "getPaymentTypeText", "", "paymentType", "Lcom/example/sharen/data/model/PaymentType;", "getStatusColor", "", "status", "Lcom/example/sharen/data/model/InvoiceStatus;", "getStatusText", "app_debug"})
    public final class InvoiceViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final com.example.sharen.databinding.ItemInvoiceBinding binding = null;
        
        public InvoiceViewHolder(@org.jetbrains.annotations.NotNull
        com.example.sharen.databinding.ItemInvoiceBinding binding) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Invoice invoice) {
        }
        
        private final java.lang.String getStatusText(com.example.sharen.data.model.InvoiceStatus status) {
            return null;
        }
        
        private final int getStatusColor(com.example.sharen.data.model.InvoiceStatus status) {
            return 0;
        }
        
        private final java.lang.String getPaymentTypeText(com.example.sharen.data.model.PaymentType paymentType) {
            return null;
        }
    }
}