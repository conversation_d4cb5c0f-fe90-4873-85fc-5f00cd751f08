package com.example.sharen.data.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.example.sharen.data.local.entity.CustomerEntity
import com.example.sharen.data.local.relation.CustomerWithUser
import kotlinx.coroutines.flow.Flow

@Dao
interface CustomerDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomer(customer: CustomerEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCustomers(customers: List<CustomerEntity>)
    
    @Update
    suspend fun updateCustomer(customer: CustomerEntity)
    
    @Delete
    suspend fun deleteCustomer(customer: CustomerEntity)
    
    @Query("SELECT * FROM customers WHERE id = :customerId")
    suspend fun getCustomerById(customerId: String): CustomerEntity?
    
    @Query("SELECT * FROM customers WHERE userId = :userId")
    suspend fun getCustomerByUserId(userId: String): CustomerEntity?
    
    @Query("SELECT * FROM customers")
    fun getAllCustomers(): Flow<List<CustomerEntity>>
    
    @Query("SELECT * FROM customers WHERE debtAmount > 0")
    fun getCustomersWithDebt(): Flow<List<CustomerEntity>>
    
    @Query("SELECT * FROM customers WHERE totalPurchases > 0 ORDER BY totalPurchases DESC LIMIT :limit")
    fun getTopCustomers(limit: Int): Flow<List<CustomerEntity>>
    
    @Transaction
    @Query("SELECT * FROM customers WHERE id = :customerId")
    fun getCustomerWithUser(customerId: String): Flow<CustomerWithUser>
    
    @Transaction
    @Query("SELECT * FROM customers")
    fun getCustomersWithUsers(): Flow<List<CustomerWithUser>>
    
    @Query("DELETE FROM customers")
    suspend fun deleteAllCustomers()
} 