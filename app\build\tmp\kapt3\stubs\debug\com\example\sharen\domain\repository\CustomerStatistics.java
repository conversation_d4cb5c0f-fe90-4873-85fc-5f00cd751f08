package com.example.sharen.domain.repository;

/**
 * آمار مشتری
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BI\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\b\u0010\f\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\bH\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J]\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000bH\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020\bH\u00d6\u0001J\t\u0010&\u001a\u00020\'H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0013\u0010\f\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000f\u00a8\u0006("}, d2 = {"Lcom/example/sharen/domain/repository/CustomerStatistics;", "", "totalPurchases", "", "totalPayments", "totalDebt", "averagePurchaseAmount", "purchaseCount", "", "paymentCount", "lastPurchaseDate", "Ljava/util/Date;", "lastPaymentDate", "(JJJJIILjava/util/Date;Ljava/util/Date;)V", "getAveragePurchaseAmount", "()J", "getLastPaymentDate", "()Ljava/util/Date;", "getLastPurchaseDate", "getPaymentCount", "()I", "getPurchaseCount", "getTotalDebt", "getTotalPayments", "getTotalPurchases", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
public final class CustomerStatistics {
    private final long totalPurchases = 0L;
    private final long totalPayments = 0L;
    private final long totalDebt = 0L;
    private final long averagePurchaseAmount = 0L;
    private final int purchaseCount = 0;
    private final int paymentCount = 0;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date lastPurchaseDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date lastPaymentDate = null;
    
    public CustomerStatistics(long totalPurchases, long totalPayments, long totalDebt, long averagePurchaseAmount, int purchaseCount, int paymentCount, @org.jetbrains.annotations.Nullable
    java.util.Date lastPurchaseDate, @org.jetbrains.annotations.Nullable
    java.util.Date lastPaymentDate) {
        super();
    }
    
    public final long getTotalPurchases() {
        return 0L;
    }
    
    public final long getTotalPayments() {
        return 0L;
    }
    
    public final long getTotalDebt() {
        return 0L;
    }
    
    public final long getAveragePurchaseAmount() {
        return 0L;
    }
    
    public final int getPurchaseCount() {
        return 0;
    }
    
    public final int getPaymentCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getLastPurchaseDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getLastPaymentDate() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final long component4() {
        return 0L;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.repository.CustomerStatistics copy(long totalPurchases, long totalPayments, long totalDebt, long averagePurchaseAmount, int purchaseCount, int paymentCount, @org.jetbrains.annotations.Nullable
    java.util.Date lastPurchaseDate, @org.jetbrains.annotations.Nullable
    java.util.Date lastPaymentDate) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}