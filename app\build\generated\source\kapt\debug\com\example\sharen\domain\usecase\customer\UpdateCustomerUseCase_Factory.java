package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateCustomerUseCase_Factory implements Factory<UpdateCustomerUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public UpdateCustomerUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public UpdateCustomerUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static UpdateCustomerUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new UpdateCustomerUseCase_Factory(customerRepositoryProvider);
  }

  public static UpdateCustomerUseCase newInstance(CustomerRepository customerRepository) {
    return new UpdateCustomerUseCase(customerRepository);
  }
}
