// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPaymentListBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final TextInputEditText etSearch;

  @NonNull
  public final FloatingActionButton fabAddPayment;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final TextInputLayout tilSearch;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvEmptyState;

  private ActivityPaymentListBinding(@NonNull CoordinatorLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull TextInputEditText etSearch,
      @NonNull FloatingActionButton fabAddPayment, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerView, @NonNull TextInputLayout tilSearch,
      @NonNull Toolbar toolbar, @NonNull TextView tvEmptyState) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.etSearch = etSearch;
    this.fabAddPayment = fabAddPayment;
    this.progressBar = progressBar;
    this.recyclerView = recyclerView;
    this.tilSearch = tilSearch;
    this.toolbar = toolbar;
    this.tvEmptyState = tvEmptyState;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPaymentListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPaymentListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_payment_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPaymentListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.etSearch;
      TextInputEditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.fabAddPayment;
      FloatingActionButton fabAddPayment = ViewBindings.findChildViewById(rootView, id);
      if (fabAddPayment == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.tilSearch;
      TextInputLayout tilSearch = ViewBindings.findChildViewById(rootView, id);
      if (tilSearch == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvEmptyState;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      return new ActivityPaymentListBinding((CoordinatorLayout) rootView, bottomNavigation,
          etSearch, fabAddPayment, progressBar, recyclerView, tilSearch, toolbar, tvEmptyState);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
