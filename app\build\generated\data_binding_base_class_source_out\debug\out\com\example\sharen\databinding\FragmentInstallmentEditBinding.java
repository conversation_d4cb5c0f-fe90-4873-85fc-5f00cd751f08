// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInstallmentEditBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonSave;

  @NonNull
  public final ChipGroup chipGroupStatus;

  @NonNull
  public final Chip chipOverdue;

  @NonNull
  public final Chip chipPaid;

  @NonNull
  public final Chip chipPending;

  @NonNull
  public final TextInputEditText editTextDueDate;

  @NonNull
  public final TextInputEditText editTextNotes;

  @NonNull
  public final TextInputEditText editTextPaidAmount;

  @NonNull
  public final TextInputEditText editTextTotalAmount;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentInstallmentEditBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonSave, @NonNull ChipGroup chipGroupStatus,
      @NonNull Chip chipOverdue, @NonNull Chip chipPaid, @NonNull Chip chipPending,
      @NonNull TextInputEditText editTextDueDate, @NonNull TextInputEditText editTextNotes,
      @NonNull TextInputEditText editTextPaidAmount, @NonNull TextInputEditText editTextTotalAmount,
      @NonNull ProgressBar progressBar, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.buttonSave = buttonSave;
    this.chipGroupStatus = chipGroupStatus;
    this.chipOverdue = chipOverdue;
    this.chipPaid = chipPaid;
    this.chipPending = chipPending;
    this.editTextDueDate = editTextDueDate;
    this.editTextNotes = editTextNotes;
    this.editTextPaidAmount = editTextPaidAmount;
    this.editTextTotalAmount = editTextTotalAmount;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInstallmentEditBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInstallmentEditBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_installment_edit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInstallmentEditBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonSave;
      MaterialButton buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.chipGroupStatus;
      ChipGroup chipGroupStatus = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupStatus == null) {
        break missingId;
      }

      id = R.id.chipOverdue;
      Chip chipOverdue = ViewBindings.findChildViewById(rootView, id);
      if (chipOverdue == null) {
        break missingId;
      }

      id = R.id.chipPaid;
      Chip chipPaid = ViewBindings.findChildViewById(rootView, id);
      if (chipPaid == null) {
        break missingId;
      }

      id = R.id.chipPending;
      Chip chipPending = ViewBindings.findChildViewById(rootView, id);
      if (chipPending == null) {
        break missingId;
      }

      id = R.id.editTextDueDate;
      TextInputEditText editTextDueDate = ViewBindings.findChildViewById(rootView, id);
      if (editTextDueDate == null) {
        break missingId;
      }

      id = R.id.editTextNotes;
      TextInputEditText editTextNotes = ViewBindings.findChildViewById(rootView, id);
      if (editTextNotes == null) {
        break missingId;
      }

      id = R.id.editTextPaidAmount;
      TextInputEditText editTextPaidAmount = ViewBindings.findChildViewById(rootView, id);
      if (editTextPaidAmount == null) {
        break missingId;
      }

      id = R.id.editTextTotalAmount;
      TextInputEditText editTextTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (editTextTotalAmount == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentInstallmentEditBinding((CoordinatorLayout) rootView, buttonSave,
          chipGroupStatus, chipOverdue, chipPaid, chipPending, editTextDueDate, editTextNotes,
          editTextPaidAmount, editTextTotalAmount, progressBar, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
