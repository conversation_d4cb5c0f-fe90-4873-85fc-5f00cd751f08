package com.example.sharen.ui.profile;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationSettingsViewModel_Factory implements Factory<NotificationSettingsViewModel> {
  @Override
  public NotificationSettingsViewModel get() {
    return newInstance();
  }

  public static NotificationSettingsViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static NotificationSettingsViewModel newInstance() {
    return new NotificationSettingsViewModel();
  }

  private static final class InstanceHolder {
    private static final NotificationSettingsViewModel_Factory INSTANCE = new NotificationSettingsViewModel_Factory();
  }
}
