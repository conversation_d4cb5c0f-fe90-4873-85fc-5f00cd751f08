package com.example.sharen.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceRepositoryImpl_Factory implements Factory<InvoiceRepositoryImpl> {
  @Override
  public InvoiceRepositoryImpl get() {
    return newInstance();
  }

  public static InvoiceRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static InvoiceRepositoryImpl newInstance() {
    return new InvoiceRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final InvoiceRepositoryImpl_Factory INSTANCE = new InvoiceRepositoryImpl_Factory();
  }
}
