package com.example.sharen.ui.invoice;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u0000 ,2\u00020\u0001:\u0001,B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\"\u0010\u0016\u001a\u00020\u00112\u0006\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00152\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0014J\b\u0010\u001b\u001a\u00020\u0011H\u0016J\u0012\u0010\u001c\u001a\u00020\u00112\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0014J\u0010\u0010\u001f\u001a\u00020 2\u0006\u0010\u0012\u001a\u00020!H\u0016J\u0010\u0010\"\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010#\u001a\u00020\u0011H\u0002J\b\u0010$\u001a\u00020\u0011H\u0002J\b\u0010%\u001a\u00020\u0011H\u0002J\b\u0010&\u001a\u00020\u0011H\u0002J\b\u0010\'\u001a\u00020\u0011H\u0002J\b\u0010(\u001a\u00020\u0011H\u0002J\u0010\u0010)\u001a\u00020\u00112\u0006\u0010*\u001a\u00020+H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0007\u001a\n \t*\u0004\u0018\u00010\b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\n\u001a\u00020\u000b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\f\u0010\r\u00a8\u0006-"}, d2 = {"Lcom/example/sharen/ui/invoice/SalesInvoiceActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "Lcom/example/sharen/databinding/ActivitySalesInvoiceBinding;", "invoiceItemsAdapter", "Lcom/example/sharen/ui/invoice/SalesInvoiceItemAdapter;", "numberFormatter", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "viewModel", "Lcom/example/sharen/ui/invoice/SalesInvoiceViewModel;", "getViewModel", "()Lcom/example/sharen/ui/invoice/SalesInvoiceViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "editItemQuantity", "", "item", "Lcom/example/sharen/data/model/InvoiceItem;", "position", "", "onActivityResult", "requestCode", "resultCode", "data", "Landroid/content/Intent;", "onBackPressed", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onOptionsItemSelected", "", "Landroid/view/MenuItem;", "removeItem", "saveInvoice", "selectCustomer", "selectProduct", "setupAdapter", "setupObservers", "setupUI", "updateUI", "invoice", "Lcom/example/sharen/data/model/Invoice;", "Companion", "app_debug"})
public final class SalesInvoiceActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.example.sharen.databinding.ActivitySalesInvoiceBinding binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private final java.text.NumberFormat numberFormatter = null;
    private com.example.sharen.ui.invoice.SalesInvoiceItemAdapter invoiceItemsAdapter;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String EXTRA_INVOICE_ID = "invoice_id";
    private static final int REQUEST_CUSTOMER_SELECTION = 1001;
    private static final int REQUEST_PRODUCT_SELECTION = 1002;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.ui.invoice.SalesInvoiceActivity.Companion Companion = null;
    
    public SalesInvoiceActivity() {
        super();
    }
    
    private final com.example.sharen.ui.invoice.SalesInvoiceViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupAdapter() {
    }
    
    private final void setupObservers() {
    }
    
    private final void updateUI(com.example.sharen.data.model.Invoice invoice) {
    }
    
    private final void selectCustomer() {
    }
    
    private final void selectProduct() {
    }
    
    private final void editItemQuantity(com.example.sharen.data.model.InvoiceItem item, int position) {
    }
    
    private final void removeItem(int position) {
    }
    
    private final void saveInvoice() {
    }
    
    @java.lang.Override
    protected void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable
    android.content.Intent data) {
    }
    
    @java.lang.Override
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull
    android.view.MenuItem item) {
        return false;
    }
    
    @java.lang.Override
    public void onBackPressed() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/sharen/ui/invoice/SalesInvoiceActivity$Companion;", "", "()V", "EXTRA_INVOICE_ID", "", "REQUEST_CUSTOMER_SELECTION", "", "REQUEST_PRODUCT_SELECTION", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}