<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_invoice_list" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_invoice_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_invoice_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="55"/></Target><Target id="@+id/searchView" view="androidx.appcompat.widget.SearchView"><Expressions/><location startLine="21" startOffset="8" endLine="28" endOffset="53"/></Target><Target id="@+id/chipAll" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="41" startOffset="16" endLine="47" endOffset="48"/></Target><Target id="@+id/chipPaid" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="49" startOffset="16" endLine="54" endOffset="49"/></Target><Target id="@+id/chipPartiallyPaid" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="56" startOffset="16" endLine="61" endOffset="59"/></Target><Target id="@+id/chipPending" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="63" startOffset="16" endLine="68" endOffset="52"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="78" startOffset="8" endLine="84" endOffset="51"/></Target><Target id="@+id/emptyView" view="TextView"><Expressions/><location startLine="86" startOffset="8" endLine="96" endOffset="55"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="98" startOffset="8" endLine="106" endOffset="55"/></Target><Target id="@+id/fabAddInvoice" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="110" startOffset="4" endLine="117" endOffset="54"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="119" startOffset="4" endLine="124" endOffset="42"/></Target></Targets></Layout>