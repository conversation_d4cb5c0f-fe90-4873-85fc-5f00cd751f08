// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPaymentMethodBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView paymentMethodIcon;

  @NonNull
  public final TextView paymentMethodText;

  private ItemPaymentMethodBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView paymentMethodIcon, @NonNull TextView paymentMethodText) {
    this.rootView = rootView;
    this.paymentMethodIcon = paymentMethodIcon;
    this.paymentMethodText = paymentMethodText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPaymentMethodBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPaymentMethodBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_payment_method, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPaymentMethodBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.paymentMethodIcon;
      ImageView paymentMethodIcon = ViewBindings.findChildViewById(rootView, id);
      if (paymentMethodIcon == null) {
        break missingId;
      }

      id = R.id.paymentMethodText;
      TextView paymentMethodText = ViewBindings.findChildViewById(rootView, id);
      if (paymentMethodText == null) {
        break missingId;
      }

      return new ItemPaymentMethodBinding((LinearLayout) rootView, paymentMethodIcon,
          paymentMethodText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
