package com.example.sharen.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductRepositoryImpl_Factory implements Factory<ProductRepositoryImpl> {
  @Override
  public ProductRepositoryImpl get() {
    return newInstance();
  }

  public static ProductRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ProductRepositoryImpl newInstance() {
    return new ProductRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final ProductRepositoryImpl_Factory INSTANCE = new ProductRepositoryImpl_Factory();
  }
}
