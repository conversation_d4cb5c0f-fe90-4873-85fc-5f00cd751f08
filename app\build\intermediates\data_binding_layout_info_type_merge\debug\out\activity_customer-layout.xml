<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customer" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_customer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_customer_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="117" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="57"/></Target><Target id="@+id/search_view" view="androidx.appcompat.widget.SearchView"><Expressions/><location startLine="26" startOffset="8" endLine="33" endOffset="44"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="38" startOffset="4" endLine="104" endOffset="59"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="56"/></Target><Target id="@+id/empty_state" view="LinearLayout"><Expressions/><location startLine="58" startOffset="12" endLine="92" endOffset="26"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="95" startOffset="12" endLine="100" endOffset="43"/></Target><Target id="@+id/fab_add" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="107" startOffset="4" endLine="115" endOffset="41"/></Target></Targets></Layout>