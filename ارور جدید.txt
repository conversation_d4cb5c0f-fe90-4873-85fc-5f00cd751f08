Executing tasks: [:app:assembleDebug] in project C:\New folder\sharen

Configuration on demand is an incubating feature.
> Task :app:createDebugVariantModel
> Task :app:preBuild UP-TO-DATE
> Task :app:preDebugBuild UP-TO-DATE
> Task :app:mergeDebugNativeDebugMetadata NO-SOURCE
> Task :app:dataBindingMergeDependencyArtifactsDebug
> Task :app:generateDebugResValues FROM-CACHE
> Task :app:generateDebugResources UP-TO-DATE
> Task :app:mergeDebugResources
> Task :app:packageDebugResources
> Task :app:dataBindingTriggerDebug
> Task :app:generateSafeArgsDebug FROM-CACHE
> Task :app:parseDebugLocalResources
> Task :app:checkDebugAarMetadata
> Task :app:dataBindingGenBaseClassesDebug
> Task :app:mapDebugSourceSetPaths
> Task :app:createDebugCompatibleScreenManifests
> Task :app:extractDeepLinksDebug FROM-CACHE
> Task :app:processDebugMainManifest FROM-CACHE
> Task :app:processDebugManifest FROM-CACHE
> Task :app:processDebugManifestForPackage FROM-CACHE
> Task :app:javaPreCompileDebug FROM-CACHE
> Task :app:mergeDebugShaders FROM-CACHE
> Task :app:compileDebugShaders NO-SOURCE
> Task :app:generateDebugAssets UP-TO-DATE
> Task :app:mergeDebugAssets FROM-CACHE
> Task :app:compressDebugAssets FROM-CACHE
> Task :app:desugarDebugFileDependencies FROM-CACHE
> Task :app:checkDebugDuplicateClasses
> Task :app:mergeExtDexDebug FROM-CACHE
> Task :app:processDebugResources
> Task :app:mergeLibDexDebug FROM-CACHE
> Task :app:mergeDebugJniLibFolders FROM-CACHE
> Task :app:mergeDebugNativeLibs NO-SOURCE
> Task :app:stripDebugDebugSymbols NO-SOURCE
> Task :app:validateSigningDebug
> Task :app:writeDebugAppMetadata
> Task :app:writeDebugSigningConfigVersions
> Task :app:kaptGenerateStubsDebugKotlin
> Task :app:kaptDebugKotlin
> Task :app:compileDebugKotlin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/core/base/BaseFragment.kt:46:41 Cannot access 'showLoading': it is protected in 'BaseActivity'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/core/base/BaseFragment.kt:53:41 Cannot access 'hideLoading': it is protected in 'BaseActivity'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/core/base/BaseFragment.kt:60:41 Cannot access 'showError': it is protected in 'BaseActivity'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/core/base/BaseFragment.kt:67:41 Cannot access 'showSuccess': it is protected in 'BaseActivity'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InstallmentEntity.kt:50:13 Cannot find a parameter with this name: amount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InstallmentEntity.kt:56:13 No value passed for parameter 'installmentNumber'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InstallmentEntity.kt:56:13 No value passed for parameter 'totalAmount'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InstallmentEntity.kt:56:13 No value passed for parameter 'remainingAmount'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InstallmentEntity.kt:66:38 Unresolved reference: amount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InvoiceEntity.kt:72:9 Cannot find a parameter with this name: discount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InvoiceEntity.kt:73:9 Cannot find a parameter with this name: tax
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InvoiceEntity.kt:97:32 Unresolved reference: discount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/InvoiceEntity.kt:98:27 Unresolved reference: tax
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderEntity.kt:7:40 Unresolved reference: Order
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderEntity.kt:8:40 Unresolved reference: OrderStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderEntity.kt:45:20 Unresolved reference: Order
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderEntity.kt:45:28 Unresolved reference: Order
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderEntity.kt:52:18 Unresolved reference: OrderStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderEntity.kt:59:30 Unresolved reference: Order
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderItemEntity.kt:7:40 Unresolved reference: OrderItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderItemEntity.kt:44:24 Unresolved reference: OrderItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderItemEntity.kt:44:36 Unresolved reference: OrderItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/OrderItemEntity.kt:58:38 Unresolved reference: OrderItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/PaymentEntity.kt:51:22 Type mismatch: inferred type is Long but Double was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/PaymentEntity.kt:54:13 Cannot find a parameter with this name: description
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/PaymentEntity.kt:57:13 No value passed for parameter 'date'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/PaymentEntity.kt:67:26 Type mismatch: inferred type is Double but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/PaymentEntity.kt:70:39 Unresolved reference: description
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:8:40 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:36:24 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:64:13 Cannot find a parameter with this name: type
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:64:20 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:69:13 Cannot find a parameter with this name: materials
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:69:30 Not enough information to infer type variable T
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:69:69 Not enough information to infer type variable T
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:74:13 Cannot find a parameter with this name: imageUrls
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:74:30 Not enough information to infer type variable T
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:74:69 Not enough information to infer type variable T
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:75:13 Cannot find a parameter with this name: tags
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:75:25 Not enough information to infer type variable T
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:75:59 Not enough information to infer type variable T
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:89:24 Type mismatch: inferred type is String? but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:92:30 Type mismatch: inferred type is String? but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:94:32 Unresolved reference: type
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:95:40 Only safe (?.) or non-null asserted (!!.) calls are allowed on a nullable receiver of type Season?
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:96:40 Only safe (?.) or non-null asserted (!!.) calls are allowed on a nullable receiver of type Gender?
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:99:49 Unresolved reference: materials
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:104:49 Unresolved reference: imageUrls
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/ProductEntity.kt:105:44 Unresolved reference: tags
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/TransactionEntity.kt:7:40 Unresolved reference: Transaction
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/TransactionEntity.kt:8:40 Unresolved reference: TransactionType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/TransactionEntity.kt:51:26 Unresolved reference: Transaction
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/TransactionEntity.kt:51:40 Unresolved reference: Transaction
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/TransactionEntity.kt:57:16 Unresolved reference: TransactionType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/local/entity/TransactionEntity.kt:64:42 Unresolved reference: Transaction
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:71:13 Cannot find a parameter with this name: passwordHash
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:75:13 Cannot find a parameter with this name: isActive
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:76:13 Cannot find a parameter with this name: lastLoginAt
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:163:24 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:187:24 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:211:24 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:247:17 No value passed for parameter 'imageUrl'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:247:17 No value passed for parameter 'sortOrder'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:256:17 No value passed for parameter 'imageUrl'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:256:17 No value passed for parameter 'sortOrder'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:265:17 No value passed for parameter 'imageUrl'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/migration/DataMigrationHelper.kt:265:17 No value passed for parameter 'sortOrder'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/model/Payment.kt:28:12 Redeclaration: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/model/PaymentMethod.kt:3:12 Redeclaration: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/model/User.kt:19:12 Redeclaration: UserRole
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/model/UserRole.kt:3:12 Redeclaration: UserRole
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/AuthRemoteDataSource.kt:17:22 Type mismatch: inferred type is UUID! but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/AuthRemoteDataSource.kt:21:33 Unresolved reference: USER
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/AuthRemoteDataSource.kt:32:22 Type mismatch: inferred type is UUID! but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/AuthRemoteDataSource.kt:36:33 Unresolved reference: USER
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:20:38 Type mismatch: inferred type is Long but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:24:20 Unresolved reference: deleteInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:44:40 Operator '==' cannot be applied to 'Long' and 'String'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:91:17 Operator '==' cannot be applied to 'Long' and 'String'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:114:102 Type mismatch: inferred type is Result<Unit> but Result<Map<String, Any>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:123:89 Unresolved reference: UNPAID
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:126:9 Type mismatch: inferred type is Unit but Map<String, Any> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:138:40 Operator '==' cannot be applied to 'Long' and 'String'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:157:22 Type mismatch: inferred type is String but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:158:30 Type mismatch: inferred type is String but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:162:44 Unresolved reference: UNPAID
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/InstallmentRemoteDataSource.kt:163:17 Cannot find a parameter with this name: interestRate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:24:20 Unresolved reference: deletePayment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:87:95 Type mismatch: inferred type is Result<Unit> but Result<Double> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:91:9 Type mismatch: inferred type is Unit but Double was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:94:80 Type mismatch: inferred type is Result<Unit> but Result<Double> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:98:9 Type mismatch: inferred type is Unit but Double was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:101:98 Type mismatch: inferred type is Result<Unit> but Result<Map<String, Any>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/PaymentRemoteDataSource.kt:111:9 Type mismatch: inferred type is Unit but Map<String, Any> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:17:27 Unresolved reference: getReportData
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:26:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:28:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:28:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:28:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:28:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:40:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:42:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:42:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:42:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:42:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:54:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:56:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:56:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:56:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:56:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:66:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:68:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:68:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:68:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:68:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:78:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:80:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:80:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:80:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:80:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:91:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:93:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:93:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:93:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:93:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:105:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:107:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:107:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:107:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:107:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:119:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:121:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:121:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:121:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:121:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:131:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:133:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:133:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:133:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:133:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:143:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:145:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:145:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:145:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:145:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:155:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:157:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:157:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:157:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:157:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:168:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:170:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:170:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:170:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:170:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:180:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:182:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:182:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:182:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:182:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:192:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:194:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:194:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:194:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:194:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:204:13 Cannot find a parameter with this name: content
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:206:13 No value passed for parameter 'description'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:206:13 No value passed for parameter 'startDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:206:13 No value passed for parameter 'endDate'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:206:13 No value passed for parameter 'data'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:217:20 Unresolved reference: deleteReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:398:20 Unresolved reference: exportReportToPdf
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:402:20 Unresolved reference: exportReportToExcel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:406:20 Unresolved reference: exportReportToCsv
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:411:20 Unresolved reference: saveReportTemplate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:415:20 Unresolved reference: getReportTemplate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:419:35 Unresolved reference: getAllReportTemplates
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:428:20 Unresolved reference: deleteReportTemplate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/ReportRemoteDataSource.kt:432:20 Unresolved reference: updateReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:10:39 Unresolved reference: SignUpRequest
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:10:64 Unresolved reference: AuthResponse
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:13:39 Unresolved reference: SignInRequest
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:13:64 Unresolved reference: AuthResponse
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:111:29 Unresolved reference: MultipartBody
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:112:17 Unresolved reference: StorageResponse
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/remote/SupabaseApiService.kt:118:17 Unresolved reference: StorageResponse
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:10:38 Unresolved reference: auth
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:11:48 Unresolved reference: Email
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:22:1 Class 'AuthRepositoryImpl' is not abstract and does not implement abstract member public abstract suspend fun register(name: String, email: String, password: String, phone: String): Result<User> defined in com.example.sharen.data.repository.AuthRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:34:69 Return type of 'login' is not a subtype of the return type of the overridden member 'public abstract suspend fun login(email: String, password: String): com.example.sharen.data.model.Result<User> defined in com.example.sharen.data.repository.AuthRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:35:16 Type mismatch: inferred type is com.example.sharen.data.model.Result<User> but kotlin.Result<User> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:38:5 'register' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:39:16 Type mismatch: inferred type is com.example.sharen.data.model.Result<User> but kotlin.Result<User> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:39:42 Type mismatch: inferred type is User but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:39:42 No value passed for parameter 'email'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:39:42 No value passed for parameter 'password'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:39:42 No value passed for parameter 'phone'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:43:26 Unresolved reference: logout
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:47:16 Type mismatch: inferred type is Flow<UserEntity?> but Flow<User?> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:50:50 Return type of 'updateUser' is not a subtype of the return type of the overridden member 'public abstract suspend fun updateUser(user: User): com.example.sharen.data.model.Result<User> defined in com.example.sharen.data.repository.AuthRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:51:16 Type mismatch: inferred type is com.example.sharen.data.model.Result<User> but kotlin.Result<User> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:54:84 Return type of 'changePassword' is not a subtype of the return type of the overridden member 'public abstract suspend fun changePassword(oldPassword: String, newPassword: String): com.example.sharen.data.model.Result<Unit> defined in com.example.sharen.data.repository.AuthRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:55:16 Type mismatch: inferred type is com.example.sharen.data.model.Result<Unit> but kotlin.Result<Unit> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:58:5 'resetPassword' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:60:28 Unresolved reference: auth
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:67:5 'isUserLoggedIn' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:71:5 'getCurrentUserId' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:79:28 Type mismatch: inferred type is () -> Unit but FilterOperation was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/AuthRepositoryImpl.kt:151:28 Type mismatch: inferred type is () -> Unit but FilterOperation was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:23:54 Type mismatch: inferred type is Flow<List<Any>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:27:40 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:34:27 Type mismatch: inferred type is Unit but InstallmentEntity was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:34:71 Unresolved reference: fromInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:41:40 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:45:7 Type mismatch: inferred type is List<Any> but List<Installment> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:50:44 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:56:42 Unresolved reference: fromInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:62:44 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:69:38 Unresolved reference: fromInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:75:38 Unresolved reference: fromInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:86:38 Unresolved reference: fromInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:95:9 Type mismatch: inferred type is Flow<List<Unit>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:96:13 Type mismatch: inferred type is Unit but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:96:31 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:100:9 Type mismatch: inferred type is Flow<List<Unit>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:101:13 Type mismatch: inferred type is Unit but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:101:31 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:105:9 Type mismatch: inferred type is Flow<List<Unit>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:106:13 Type mismatch: inferred type is Unit but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:106:31 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:110:9 Type mismatch: inferred type is Flow<List<Unit>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:111:13 Type mismatch: inferred type is Unit but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:111:31 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:115:9 Type mismatch: inferred type is Flow<List<Unit>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:116:13 Type mismatch: inferred type is Unit but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:116:31 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:120:45 Type mismatch: inferred type is Date but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:120:56 Type mismatch: inferred type is Date but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:128:9 Type mismatch: inferred type is Flow<List<Unit>> but Flow<List<Installment>> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:129:13 Type mismatch: inferred type is Unit but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:129:31 Unresolved reference: toInstallment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepository.kt:141:13 Type mismatch: inferred type is Date but String was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:14:5 This type has a constructor, and thus must be initialized here
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:16:5 'createInstallment' in 'InstallmentRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:16:71 Return type of 'createInstallment' is not a subtype of the return type of the overridden member 'public final suspend fun createInstallment(installment: Installment): Installment defined in com.example.sharen.data.repository.InstallmentRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:20:5 'updateInstallment' in 'InstallmentRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:20:71 Return type of 'updateInstallment' is not a subtype of the return type of the overridden member 'public final suspend fun updateInstallment(installment: Installment): Installment defined in com.example.sharen.data.repository.InstallmentRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:24:5 'deleteInstallment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:28:5 'getInstallment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:32:5 'getAllInstallments' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:36:5 'getInstallmentsByCustomer' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:40:5 'getInstallmentsByDateRange' in 'InstallmentRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:44:5 'getInstallmentsByStatus' in 'InstallmentRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:48:5 'payInstallment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:52:5 'getUpcomingInstallments' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:56:5 'getOverdueInstallments' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:56:5 Conflicting overloads: public open suspend fun getOverdueInstallments(): Flow<List<Installment>> defined in com.example.sharen.data.repository.InstallmentRepositoryImpl, public final fun getOverdueInstallments(): Flow<List<Installment>> defined in com.example.sharen.data.repository.InstallmentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:60:5 'getInstallmentStatistics' in 'InstallmentRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:60:84 Return type of 'getInstallmentStatistics' is not a subtype of the return type of the overridden member 'public final suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Map<String, Double> defined in com.example.sharen.data.repository.InstallmentRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:64:5 'calculateRemainingAmount' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:68:5 'getCustomerInstallmentHistory' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InstallmentRepositoryImpl.kt:72:5 'generateInstallmentSchedule' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:24:1 Class 'InvoiceRepositoryImpl' is not abstract and does not implement abstract member public abstract suspend fun updateInvoice(invoice: Invoice): Result<Invoice> defined in com.example.sharen.data.repository.InvoiceRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:161:21 Cannot find a parameter with this name: invoiceId
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:162:30 Type mismatch: inferred type is Long but Double was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:166:21 Cannot find a parameter with this name: paymentDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:167:21 No value passed for parameter 'customerId'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:167:21 No value passed for parameter 'date'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:178:36 Return type of 'getAllInvoices' is not a subtype of the return type of the overridden member 'public abstract fun getAllInvoices(): Flow<List<com.example.sharen.domain.model.Invoice>> defined in com.example.sharen.data.repository.InvoiceRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:182:5 'getInvoiceById' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:182:5 Conflicting overloads: public open fun getInvoiceById(invoiceId: String): Flow<com.example.sharen.data.model.Invoice> defined in com.example.sharen.data.repository.InvoiceRepositoryImpl, public abstract suspend fun getInvoiceById(invoiceId: String): Result<com.example.sharen.domain.model.Invoice?> defined in com.example.sharen.data.repository.InvoiceRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:188:49 Return type of 'searchInvoices' is not a subtype of the return type of the overridden member 'public abstract fun searchInvoices(query: String): Flow<List<com.example.sharen.domain.model.Invoice>> defined in com.example.sharen.data.repository.InvoiceRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:200:5 'getInvoicesByCustomerId' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:205:74 Return type of 'getInvoicesByDateRange' is not a subtype of the return type of the overridden member 'public abstract fun getInvoicesByDateRange(startDate: Date, endDate: Date): Flow<List<com.example.sharen.domain.model.Invoice>> defined in com.example.sharen.data.repository.InvoiceRepository'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:213:5 'createInvoice' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:245:5 'updateInvoice' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:277:5 'updateInvoiceStatus' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:299:5 'addPayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:310:77 Unresolved reference: invoiceId
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:324:34 Type mismatch: inferred type is Double but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:325:39 Type mismatch: inferred type is Double but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:337:5 'getPaymentsByInvoiceId' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:338:49 Unresolved reference: invoiceId
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:342:5 'getInvoiceItems' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:347:5 'addInvoiceItem' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:386:5 'removeInvoiceItem' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:423:5 'updateInvoiceItem' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:460:5 'recalculateInvoice' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:498:5 'getInvoiceCount' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:502:5 'getTotalSales' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/InvoiceRepositoryImpl.kt:509:5 'getRecentInvoices' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:14:5 'paymentDao' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:16:5 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:18:5 'createPayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:22:5 'updatePayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:26:5 'deletePayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:30:5 'getPayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:34:5 'getAllPayments' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:38:5 'getPaymentsByCustomer' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:42:5 'getPaymentsByDateRange' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:46:5 'getPaymentsByStatus' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:50:5 'confirmPayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:54:5 'rejectPayment' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:58:5 'getTotalPaymentsByDateRange' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:62:5 'getCustomerTotalPayments' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/PaymentRepositoryImpl.kt:66:5 'getPaymentStatistics' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:17:5 'getReportData' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:22:5 'generateDailyFinancialReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:26:5 'generateMonthlyFinancialReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:30:5 'generateYearlyFinancialReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:34:5 'generateProfitLossReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:38:5 'generateDebtReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:43:5 'generateDailySalesReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:47:5 'generateMonthlySalesReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:51:5 'generateYearlySalesReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:55:5 'generateSalesByCustomerReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:59:5 'generateSalesByCategoryReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:64:5 'generateCurrentInventoryReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:68:5 'generateLowStockReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:72:5 'generateStockMovementReport' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:73:33 Unresolved reference: generateStockMovementReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:77:5 'generateCustomerActivityReport' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:78:33 Unresolved reference: generateCustomerActivityReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:81:5 'generateCustomerLoyaltyReport' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:82:33 Unresolved reference: generateCustomerLoyaltyReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:85:5 'generateCustomerDebtReport' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:86:33 Unresolved reference: generateCustomerDebtReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:90:5 'saveReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:94:5 'updateReport' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:98:5 'deleteReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:102:5 'getReport' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:106:5 'getAllReports' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:110:5 'getReportsByType' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:114:5 'getReportsByDateRange' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:119:5 'exportReportToPdf' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:123:5 'exportReportToExcel' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:127:5 'exportReportToCsv' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:132:5 'saveReportTemplate' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:136:5 'getReportTemplate' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:140:5 'getAllReportTemplates' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/ReportRepositoryImpl.kt:144:5 'deleteReportTemplate' in 'ReportRepository' is final and cannot be overridden
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/UserRepositoryImpl.kt:81:36 Unresolved reference: toDomainModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/impl/AuthRepositoryImpl.kt:22:47 Unresolved reference: fromUser
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/impl/AuthRepositoryImpl.kt:32:47 Unresolved reference: fromUser
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/impl/AuthRepositoryImpl.kt:43:16 Type mismatch: inferred type is Flow<com.example.sharen.domain.model.User?> but Flow<com.example.sharen.data.model.User?> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/impl/AuthRepositoryImpl.kt:43:47 Type mismatch: inferred type is com.example.sharen.domain.model.User? but com.example.sharen.data.model.User? was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/data/repository/impl/AuthRepositoryImpl.kt:49:63 Type mismatch: inferred type is com.example.sharen.data.model.User but com.example.sharen.domain.model.User was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/di/DatabaseModule.kt:40:53 Unresolved reference: SellerDao
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/di/DatabaseModule.kt:41:25 Unresolved reference: sellerDao
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/di/NetworkModule.kt:4:27 Unresolved reference: BuildConfig
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/di/NetworkModule.kt:17:31 Unresolved reference: storage
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/di/NetworkModule.kt:56:13 Not enough information to infer type variable Config
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/di/NetworkModule.kt:56:21 Unresolved reference: Storage
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:3:40 Unresolved reference: DashboardStats
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:24:33 Unresolved reference: DashboardStats
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:11 Type mismatch: inferred type is suspend (Array<List<Parcelable>>, Any?, Any?, Any?, Any?, Any?) -> Unit but suspend (Array<TypeVariable(T)>) -> TypeVariable(R) was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:13 Expected one parameter of type Array<List<Parcelable>>
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:24 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:51 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:68 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:49:85 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:52:56 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:56:59 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:60:53 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:63:56 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:63:67 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:66:57 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:67:39 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase.kt:74:13 Unresolved reference: DashboardStats
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:87:13 Cannot find a parameter with this name: subtotal
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:88:13 Cannot find a parameter with this name: discount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:89:13 Cannot find a parameter with this name: tax
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:92:13 Cannot find a parameter with this name: paymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:95:13 No value passed for parameter 'customerName'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:95:13 No value passed for parameter 'totalAmount'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase.kt:95:13 No value passed for parameter 'remainingAmount'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/payment/CreatePaymentUseCase.kt:48:22 Type mismatch: inferred type is Long but Double was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/payment/CreatePaymentUseCase.kt:51:13 Cannot find a parameter with this name: description
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/payment/CreatePaymentUseCase.kt:54:13 No value passed for parameter 'date'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:4:40 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:25:15 Unresolved reference: ProductType
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:81:13 Cannot find a parameter with this name: type
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:84:21 Type mismatch: inferred type is List<String> but List<ProductSize> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:85:22 Type mismatch: inferred type is List<String> but List<ProductColor> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:86:13 Cannot find a parameter with this name: materials
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:91:13 Cannot find a parameter with this name: imageUrls
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/domain/usecase/product/AddProductUseCase.kt:92:13 Cannot find a parameter with this name: tags
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:38:13 Unresolved reference: btnSendResetLink
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:46:13 Unresolved reference: ivBack
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:64:29 Unresolved reference: btnSendResetLink
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:64:46 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:67:29 Unresolved reference: btnSendResetLink
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:67:46 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:123:17 Unresolved reference: btnSendResetLink
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:123:34 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:128:17 Unresolved reference: btnSendResetLink
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/ForgotPasswordActivity.kt:128:34 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/auth/RegisterActivity.kt:49:13 Unresolved reference: ivBack
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/CustomerActivity.kt:154:35 Unresolved reference: CustomerDetailsActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/CustomerActivity.kt:160:35 Unresolved reference: CustomerFormActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/CustomerActivity.kt:165:35 Unresolved reference: CustomerFormActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:55:17 Unresolved reference: btnEdit
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:69:17 Unresolved reference: tvCustomerAddress
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:69:35 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:72:17 Unresolved reference: tvTotalPurchases
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:72:34 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:73:17 Unresolved reference: tvTotalDebt
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:73:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:74:17 Unresolved reference: tvCreditLimit
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:74:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:78:17 Unresolved reference: tvRemainingCredit
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:78:35 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:94:17 Unresolved reference: tvCreditStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:95:21 Unresolved reference: text
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:96:21 Unresolved reference: setTextColor
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:103:17 Unresolved reference: tvMemberSince
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:103:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:107:21 Unresolved reference: badgeDebtor
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:107:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:109:21 Unresolved reference: badgeDebtor
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:109:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:114:21 Unresolved reference: badgeVip
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:114:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:116:21 Unresolved reference: badgeVip
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/customer/adapter/CustomerAdapter.kt:116:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:9:43 Unresolved reference: invoice
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:10:43 Unresolved reference: product
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:11:43 Unresolved reference: payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:40:13 Unresolved reference: cardCustomers
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:44:13 Unresolved reference: cardProducts
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:48:13 Unresolved reference: cardInvoices
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:52:13 Unresolved reference: cardPayments
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:57:13 Unresolved reference: btnNewInvoice
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:61:13 Unresolved reference: btnNewCustomer
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:65:13 Unresolved reference: btnNewProduct
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:69:13 Unresolved reference: btnReports
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:77:17 Unresolved reference: tvWelcome
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:77:27 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:82:17 Unresolved reference: tvDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:82:24 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:106:13 Unresolved reference: tvCustomersCount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:106:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:107:13 Unresolved reference: tvCustomersLabel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:107:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:110:13 Unresolved reference: tvProductsCount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:110:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:111:13 Unresolved reference: tvProductsLabel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:111:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:114:13 Unresolved reference: tvInvoicesCount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:114:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:115:13 Unresolved reference: tvInvoicesLabel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:115:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:118:13 Unresolved reference: tvPaymentsCount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:118:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:119:13 Unresolved reference: tvPaymentsLabel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:119:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:122:13 Unresolved reference: tvTotalSales
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:122:26 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:123:13 Unresolved reference: tvTotalDebt
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:123:25 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:133:35 Unresolved reference: ProductActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:138:35 Unresolved reference: InvoiceActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:143:35 Unresolved reference: PaymentActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:148:35 Unresolved reference: InvoiceActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/ui/dashboard/DashboardActivity.kt:160:35 Unresolved reference: ProductActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/viewmodel/CustomerViewModel.kt:127:64 Unresolved reference: Date
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/presentation/viewmodel/CustomerViewModel.kt:168:33 Unresolved reference: Date
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:11:39 Unresolved reference: ActivityUserManagementBinding
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:17:35 Unresolved reference: ActivityUserManagementBinding
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:19:35 Unresolved reference: UserAdapter
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:23:19 Unresolved reference: ActivityUserManagementBinding
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:36:54 Unresolved reference: user_management
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:41:19 Unresolved reference: UserAdapter
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:42:29 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:43:32 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:44:31 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:45:29 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:48:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:49:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:52:47 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:86:34 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:90:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:102:39 Unresolved reference: User
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:104:22 Unresolved reference: UserDetailsDialog
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:114:42 Unresolved reference: delete_user
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:115:44 Unresolved reference: delete_user_confirmation
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:125:32 Unresolved reference: admin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:126:32 Unresolved reference: manager
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:127:32 Unresolved reference: seller
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:128:32 Unresolved reference: customer
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:132:42 Unresolved reference: select_role
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:147:22 Unresolved reference: UserFormDialog
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:147:45 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:147:51 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:147:58 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:147:65 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:148:34 Too many arguments for public final fun createUser(): Unit defined in com.example.sharen.ui.admin.UserManagementViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:148:40 Too many arguments for public final fun createUser(): Unit defined in com.example.sharen.ui.admin.UserManagementViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:148:47 Too many arguments for public final fun createUser(): Unit defined in com.example.sharen.ui.admin.UserManagementViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementActivity.kt:148:54 Too many arguments for public final fun createUser(): Unit defined in com.example.sharen.ui.admin.UserManagementViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementViewModel.kt:39:32 Type mismatch: inferred type is List<com.example.sharen.domain.model.User> but List<com.example.sharen.data.model.User> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementViewModel.kt:65:52 Operator '==' cannot be applied to 'UserRole' and 'String?'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementViewModel.kt:84:45 Unresolved reference: approveUser
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementViewModel.kt:122:45 Unresolved reference: changeUserRole
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/admin/UserManagementViewModel.kt:141:45 Unresolved reference: createUser
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/auth/AuthViewModel.kt:43:84 Too many arguments for public abstract suspend fun register(name: String, email: String, password: String, phone: String): Result<User> defined in com.example.sharen.data.repository.AuthRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/auth/AuthViewModel.kt:54:45 Unresolved reference: resetPassword
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerDetailsActivity.kt:65:64 Unresolved reference: invoiceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerDetailsViewModel.kt:9:43 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerDetailsViewModel.kt:18:37 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerDetailsViewModel.kt:43:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerDetailsViewModel.kt:47:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerDetailsViewModel.kt:67:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerFormViewModel.kt:8:43 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerFormViewModel.kt:17:37 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerFormViewModel.kt:45:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerFormViewModel.kt:49:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerFormViewModel.kt:97:31 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:8:43 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:21:37 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:55:18 Not enough information to infer type variable R
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:62:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:66:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:76:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/customer/CustomerListViewModel.kt:79:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardActivity.kt:118:53 Unresolved reference: invoiceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:8:38 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:75:17 Cannot find a parameter with this name: invoiceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:77:17 Cannot find a parameter with this name: customerName
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:79:17 Cannot find a parameter with this name: date
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:80:17 Cannot find a parameter with this name: status
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:80:17 No value passed for parameter 'type'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:80:26 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:84:17 Cannot find a parameter with this name: invoiceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:86:17 Cannot find a parameter with this name: customerName
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:88:17 Cannot find a parameter with this name: date
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:89:17 Cannot find a parameter with this name: status
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:89:17 No value passed for parameter 'type'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:89:26 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:93:17 Cannot find a parameter with this name: invoiceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:95:17 Cannot find a parameter with this name: customerName
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:97:17 Cannot find a parameter with this name: date
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:98:17 Cannot find a parameter with this name: status
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:98:17 No value passed for parameter 'type'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/DashboardViewModel.kt:98:26 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:12:38 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:53:60 Unresolved reference: invoiceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:54:55 Unresolved reference: customerName
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:55:79 Unresolved reference: date
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:61:31 Unresolved reference: status
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:62:17 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:63:83 Unresolved reference: transaction_status_paid
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:66:17 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:67:83 Unresolved reference: transaction_status_pending
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:71:17 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:72:83 Unresolved reference: transaction_status_partial
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:76:17 Unresolved reference: TransactionStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/dashboard/TransactionAdapter.kt:77:83 Unresolved reference: transaction_status_cancelled
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:84:94 Too many arguments for public open fun actionInstallmentDetailToInstallmentEdit(): InstallmentDetailFragmentDirections.ActionInstallmentDetailToInstallmentEdit defined in com.example.sharen.ui.installment.InstallmentDetailFragmentDirections
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:102:39 Unresolved reference: Installment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:112:57 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:113:60 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:114:58 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:115:60 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:121:32 Unresolved reference: delete_installment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:123:51 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:123:54 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentDetailFragment.kt:147:58 Unresolved reference: error_invalid_amount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:78:27 Unresolved reference: isLoading
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:78:47 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:97:45 Unresolved reference: Installment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:105:17 'when' expression must be exhaustive, add necessary 'else' branch
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:106:21 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:107:21 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:108:21 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:119:64 Unresolved reference: error_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:124:63 Unresolved reference: error_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:129:60 Unresolved reference: error_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:144:33 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:145:30 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:146:33 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:147:21 Unresolved reference: InstallmentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:152:17 Type mismatch: inferred type is Long but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:153:17 Too many arguments for public final fun updateInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:154:17 Too many arguments for public final fun updateInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:155:17 Too many arguments for public final fun updateInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:156:17 Too many arguments for public final fun updateInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:157:17 Too many arguments for public final fun updateInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:161:22 Unresolved reference: customerId
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:162:17 Too many arguments for public final fun createInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:163:17 Too many arguments for public final fun createInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:164:17 Too many arguments for public final fun createInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:165:17 Too many arguments for public final fun createInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentEditFragment.kt:166:17 Too many arguments for public final fun createInstallment(installment: Installment): Unit defined in com.example.sharen.ui.installment.InstallmentViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentListFragment.kt:72:29 Unresolved reference: swipeRefreshLayout
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentListFragment.kt:72:48 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentListFragment.kt:90:17 Unresolved reference: fabAdd
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentListFragment.kt:92:90 Too many arguments for public open fun actionInstallmentListToInstallmentEdit(): InstallmentListFragmentDirections.ActionInstallmentListToInstallmentEdit defined in com.example.sharen.ui.installment.InstallmentListFragmentDirections
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentListFragment.kt:96:17 Unresolved reference: swipeRefreshLayout
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:38:39 Type mismatch: inferred type is Flow<List<Installment>> but List<Installment> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:56:40 Type mismatch: inferred type is Flow<Installment?> but Installment was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:58:28 Type mismatch: inferred type is List<Any> but List<Installment> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:147:54 Type mismatch: inferred type is String but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:198:52 Type mismatch: inferred type is String but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:233:22 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public inline fun <T> Result<TypeVariable(T)>.onSuccess(action: (value: TypeVariable(T)) -> Unit): Result<TypeVariable(T)> defined in kotlin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:233:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:236:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:249:53 Type mismatch: inferred type is String but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:250:22 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public inline fun <T> Result<TypeVariable(T)>.onSuccess(action: (value: TypeVariable(T)) -> Unit): Result<TypeVariable(T)> defined in kotlin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:250:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:253:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:266:58 Type mismatch: inferred type is String but Long was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:292:21 Type mismatch: inferred type is Double but Int was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:293:19 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public inline fun <T> Result<TypeVariable(T)>.onSuccess(action: (value: TypeVariable(T)) -> Unit): Result<TypeVariable(T)> defined in kotlin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:293:31 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/InstallmentViewModel.kt:295:31 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/installment/adapter/InstallmentAdapter.kt:38:32 Unresolved reference: bindingAdapterPosition
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsActivity.kt:143:82 Unresolved reference: cash
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsActivity.kt:144:82 Unresolved reference: card
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsActivity.kt:145:89 Unresolved reference: installment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsActivity.kt:146:83 Unresolved reference: mixed
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsActivity.kt:183:39 Unresolved reference: PaymentActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsActivity.kt:184:26 Unresolved reference: PaymentActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:50:13 Type mismatch: inferred type is Result<Invoice?> but Flow<TypeVariable(T)> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:51:18 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Flow<TypeVariable(T)>.onStart(action: suspend FlowCollector<TypeVariable(T)>.() -> Unit): Flow<TypeVariable(T)> defined in kotlinx.coroutines.flow
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:52:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:56:28 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:70:31 Unresolved reference: getInvoiceItems
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:71:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:72:28 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:81:31 Unresolved reference: getPaymentsByInvoiceId
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:82:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:83:28 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:93:31 Unresolved reference: updateInvoiceStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceDetailsViewModel.kt:95:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceItemAdapter.kt:51:25 Unresolved reference: tvTax
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceItemAdapter.kt:51:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceItemAdapter.kt:53:25 Unresolved reference: tvTax
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceItemAdapter.kt:53:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceListActivity.kt:58:28 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public inline fun TextView.doAfterTextChanged(crossinline action: (text: Editable?) -> Unit): TextWatcher defined in androidx.core.widget
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/InvoiceListViewModel.kt:59:35 Type mismatch: inferred type is List<com.example.sharen.domain.model.Invoice> but List<com.example.sharen.data.model.Invoice> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:40:21 Unresolved reference: tvPaymentDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:40:35 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:40:71 Unresolved reference: paymentDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:43:21 Unresolved reference: tvAmount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:43:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:46:21 Unresolved reference: tvPaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:46:37 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:49:21 Unresolved reference: tvReferenceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:49:39 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:52:21 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:52:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:53:21 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:62:20 'when' expression must be exhaustive, add necessary 'INSTALLMENT' branch or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:63:109 Unresolved reference: cash
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:64:116 Unresolved reference: card
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:67:61 Unresolved reference: OTHER
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:72:20 'when' expression must be exhaustive, add necessary 'CONFIRMED', 'REJECTED' branches or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:76:31 Unresolved reference: REFUNDED
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:81:20 'when' expression must be exhaustive, add necessary 'CONFIRMED', 'REJECTED' branches or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/PaymentAdapter.kt:85:31 Unresolved reference: REFUNDED
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:16:39 Unresolved reference: CustomerSelectionActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:17:38 Unresolved reference: ProductSelectionActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:146:35 Unresolved reference: CustomerSelectionActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:151:35 Unresolved reference: ProductSelectionActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:157:58 Unresolved reference: dialog_edit_quantity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:158:118 Unresolved reference: etQuantity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:167:14 Overload resolution ambiguity: 
public open fun setView(p0: View!): AlertDialog.Builder! defined in androidx.appcompat.app.AlertDialog.Builder
public open fun setView(p0: Int): AlertDialog.Builder! defined in androidx.appcompat.app.AlertDialog.Builder
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:168:49 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceActivity.kt:168:52 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:10:39 Unresolved reference: ItemSalesInvoiceProductBinding
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:20:23 Unresolved reference: ItemSalesInvoiceProductBinding
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:33:30 Unresolved reference: ItemSalesInvoiceProductBinding
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:38:35 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:39:35 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:42:32 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:43:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:44:34 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:48:36 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceItemAdapter.kt:51:36 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:77:13 Type mismatch: inferred type is Result<Invoice?> but Flow<TypeVariable(T)> was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:78:18 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public fun <T> Flow<TypeVariable(T)>.onStart(action: suspend FlowCollector<TypeVariable(T)>.() -> Unit): Flow<TypeVariable(T)> defined in kotlinx.coroutines.flow
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:79:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:83:28 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:249:49 Type mismatch: inferred type is com.example.sharen.data.model.Invoice but com.example.sharen.domain.model.Invoice was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:262:35 Unresolved reference: createInvoice
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/invoice/SalesInvoiceViewModel.kt:268:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:46:21 Unresolved reference: tvMessage
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:46:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:49:21 Unresolved reference: tvDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:49:28 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:64:55 Unresolved reference: ic_notification_system
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:65:56 Unresolved reference: ic_notification_invoice
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:66:56 Unresolved reference: ic_notification_payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:67:60 Unresolved reference: ic_notification_installment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:68:53 Unresolved reference: ic_notification_user
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:69:56 Unresolved reference: ic_notification_product
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/notification/NotificationAdapter.kt:70:36 Unresolved reference: ic_notification_system
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/AddPaymentFragment.kt:54:23 Unresolved reference: PaymentMethodAdapter
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/AddPaymentFragment.kt:55:9 Val cannot be reassigned
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/AddPaymentFragment.kt:61:55 Unresolved reference: selectedItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/AddPaymentViewModel.kt:8:43 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/AddPaymentViewModel.kt:20:36 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:46:21 Unresolved reference: tvInstallmentNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:46:41 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:47:26 Unresolved reference: installment_number
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:51:21 Unresolved reference: tvAmount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:51:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:54:21 Unresolved reference: tvDueDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:54:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:58:21 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:58:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:59:21 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:64:21 Unresolved reference: btnPay
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:64:28 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:65:21 Unresolved reference: btnPay
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:70:21 Unresolved reference: btnRemind
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:70:31 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:72:21 Unresolved reference: btnRemind
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentAdapter.kt:78:61 Unresolved reference: pending_payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentFormDialog.kt:77:38 Unresolved reference: amount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentFormDialog.kt:86:14 Unresolved reference: setNegativeButtonString
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentFormDialog.kt:90:68 Unresolved reference: colorPrimary
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/InstallmentFormDialog.kt:118:57 Unresolved reference: invalid_amount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:52:59 Unresolved reference: getPaymentMethodText
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:66:51 Unresolved reference: number
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:81:57 Unresolved reference: payment_success
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:96:57 Unresolved reference: amount_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:103:53 Unresolved reference: payment_method_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:110:53 Unresolved reference: amount_invalid
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:117:53 Unresolved reference: amount_exceeds_remaining
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:124:53 Unresolved reference: payment_date_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:129:41 Unresolved reference: etTransactionNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:131:53 Unresolved reference: transaction_number_required
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:136:23 Unresolved reference: Payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:144:33 No value passed for parameter 'method'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:144:33 No value passed for parameter 'referenceNumber'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentActivity.kt:144:33 No value passed for parameter 'notes'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAdapter.kt:39:32 Unresolved reference: bindingAdapterPosition
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAdapter.kt:83:20 'when' expression must be exhaustive, add necessary 'CREDIT_CARD', 'BANK_TRANSFER' branches or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAdapter.kt:85:31 Unresolved reference: CARD
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAdapter.kt:86:31 Unresolved reference: TRANSFER
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAdapter.kt:89:31 Unresolved reference: OTHER
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:50:23 Unresolved reference: getPayment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:64:9 Val cannot be reassigned
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:99:33 Unresolved reference: collect
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:99:43 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:101:53 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:126:86 Unresolved reference: paymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:140:38 Unresolved reference: selectedItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:147:38 Unresolved reference: selectedItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:165:73 Unresolved reference: selectedItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:166:13 Cannot find a parameter with this name: paymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:166:80 Unresolved reference: selectedItem
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentAddEditFragment.kt:172:13 No value passed for parameter 'method'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:46:19 Unresolved reference: getPayment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:70:33 Unresolved reference: collect
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:70:43 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:72:53 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:81:17 Unresolved reference: buttonEdit
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:83:49 Unresolved reference: actionPaymentDetailToPaymentEdit
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:87:17 Unresolved reference: buttonDelete
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:91:17 Unresolved reference: buttonConfirm
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:95:17 Unresolved reference: buttonReject
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:100:35 Unresolved reference: Payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:104:13 Unresolved reference: textViewStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:104:28 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:105:13 Unresolved reference: textViewMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:105:28 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:110:13 Unresolved reference: buttonConfirm
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:110:27 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:110:57 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:111:13 Unresolved reference: buttonReject
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:111:26 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:111:56 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:112:13 Unresolved reference: buttonEdit
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:112:24 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:112:54 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:113:13 Unresolved reference: buttonDelete
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:113:26 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:113:56 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:119:32 Unresolved reference: delete_payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:120:34 Unresolved reference: delete_payment_confirmation
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:121:51 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:121:54 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:131:32 Unresolved reference: reject_payment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:132:34 Unresolved reference: reject_payment_confirmation
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:133:51 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailFragment.kt:133:54 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:27:28 Unresolved reference: PaymentDetailsViewModel
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:27:55 Not enough information to infer type variable VM
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:27:55 Property delegate must have a 'getValue(PaymentDetailsFragment, KProperty<*>)' method. None of the following functions is suitable: 
public inline operator fun <T> Lazy<???>.getValue(thisRef: Any?, property: KProperty<*>): ??? defined in kotlin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:28:23 Unresolved reference: PaymentDetailsFragmentArgs
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:28:53 Not enough information to infer type variable Args
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:28:53 Property delegate must have a 'getValue(PaymentDetailsFragment, KProperty<*>)' method. None of the following functions is suitable: 
public inline operator fun <T> Lazy<???>.getValue(thisRef: Any?, property: KProperty<*>): ??? defined in kotlin
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:54:45 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:77:71 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:82:67 Unresolved reference: it
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:95:46 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:96:16 'when' expression must be exhaustive, add necessary 'else' branch
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:97:13 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:98:13 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:99:13 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:100:13 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:101:13 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:102:13 Unresolved reference: PaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:106:46 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:107:16 'when' expression must be exhaustive, add necessary 'else' branch
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:108:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:109:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:110:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:111:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:112:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:116:47 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:117:16 'when' expression must be exhaustive, add necessary 'else' branch
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:118:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:119:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:120:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:121:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentDetailsFragment.kt:122:13 Unresolved reference: PaymentStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:42:19 Cannot access 'loadPayments': it is private in 'PaymentListViewModel'
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:66:26 Unresolved reference: doAfterTextChanged
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:66:47 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:67:23 Unresolved reference: searchPayments
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:77:28 Unresolved reference: observe
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:77:44 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:82:19 Unresolved reference: isLoading
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:82:45 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:86:25 Unresolved reference: observe
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:86:41 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:87:19 Overload resolution ambiguity: 
public open fun makeText(p0: Context!, p1: CharSequence!, p2: Int): Toast! defined in android.widget.Toast
public open fun makeText(p0: Context!, p1: Int, p2: Int): Toast! defined in android.widget.Toast
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListActivity.kt:92:35 Unresolved reference: PaymentDetailActivity
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:43:21 Unresolved reference: tvPaymentDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:43:35 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:43:71 Unresolved reference: paymentDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:46:21 Unresolved reference: tvAmount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:46:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:49:21 Unresolved reference: tvPaymentMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:49:37 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:52:21 Unresolved reference: tvReferenceNumber
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:52:39 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:55:21 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:55:30 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:56:21 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:65:20 'when' expression must be exhaustive, add necessary 'INSTALLMENT' branch or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:66:109 Unresolved reference: cash
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:67:116 Unresolved reference: card
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:70:61 Unresolved reference: OTHER
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:75:20 'when' expression must be exhaustive, add necessary 'CONFIRMED', 'REJECTED' branches or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:79:31 Unresolved reference: REFUNDED
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:84:20 'when' expression must be exhaustive, add necessary 'CONFIRMED', 'REJECTED' branches or 'else' branch instead
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListAdapter.kt:88:31 Unresolved reference: REFUNDED
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListFragment.kt:30:52 Unresolved reference: actionPaymentListToPaymentDetails
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListFragment.kt:31:29 Overload resolution ambiguity: 
public open fun navigate(deepLink: Uri): Unit defined in androidx.navigation.NavController
public open fun navigate(request: NavDeepLinkRequest): Unit defined in androidx.navigation.NavController
public open fun navigate(directions: NavDirections): Unit defined in androidx.navigation.NavController
public open fun navigate(resId: Int): Unit defined in androidx.navigation.NavController
public final fun navigate(route: String, navOptions: NavOptions? = ..., navigatorExtras: Navigator.Extras? = ...): Unit defined in androidx.navigation.NavController
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListFragment.kt:66:56 Unresolved reference: actionPaymentListToAddPayment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListFragment.kt:67:33 Overload resolution ambiguity: 
public open fun navigate(deepLink: Uri): Unit defined in androidx.navigation.NavController
public open fun navigate(request: NavDeepLinkRequest): Unit defined in androidx.navigation.NavController
public open fun navigate(directions: NavDirections): Unit defined in androidx.navigation.NavController
public open fun navigate(resId: Int): Unit defined in androidx.navigation.NavController
public final fun navigate(route: String, navOptions: NavOptions? = ..., navigatorExtras: Navigator.Extras? = ...): Unit defined in androidx.navigation.NavController
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListViewModel.kt:6:43 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListViewModel.kt:17:36 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListViewModel.kt:43:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentListViewModel.kt:47:28 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:12:43 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:22:36 Unresolved reference: PaymentRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:58:61 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch: 
public suspend fun Flow<*>.collect(): Unit defined in kotlinx.coroutines.flow
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:58:71 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:102:48 Unresolved reference: addPayment
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:123:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:126:32 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:143:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:160:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:177:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:194:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:211:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:225:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:228:32 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:242:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:245:32 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:259:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:262:32 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:276:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/PaymentViewModel.kt:279:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:47:17 Unresolved reference: tvAmount
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:47:26 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:48:17 Unresolved reference: tvDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:48:24 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:49:17 Unresolved reference: tvStatus
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:49:26 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:50:17 Unresolved reference: tvMethod
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:50:26 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:51:17 Unresolved reference: tvReference
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/payment/adapter/PaymentAdapter.kt:51:29 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:8:43 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:17:36 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:38:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:42:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:55:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:60:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductDetailsViewModel.kt:77:30 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductFormViewModel.kt:8:43 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductFormViewModel.kt:17:36 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductFormViewModel.kt:51:26 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductFormViewModel.kt:55:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductFormViewModel.kt:128:31 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:8:43 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:17:36 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:63:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:67:42 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:75:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:79:42 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:87:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:91:42 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:104:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/product/ProductListViewModel.kt:114:34 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/DisplaySettingsActivity.kt:28:45 Unresolved reference: R
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/NotificationSettingsActivity.kt:28:45 Unresolved reference: R
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/PasswordChangeActivity.kt:47:57 Unresolved reference: password_changed_successfully
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/PasswordChangeViewModel.kt:31:21 Type mismatch: inferred type is Result<Unit> but Boolean was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:65:17 Unresolved reference: btnEditName
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:69:17 Unresolved reference: btnEditPhone
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:77:17 Unresolved reference: btnNotificationSettings
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:81:17 Unresolved reference: btnDisplaySettings
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:94:25 Unresolved reference: tvName
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:94:32 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:95:25 Unresolved reference: tvEmail
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:95:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:96:25 Unresolved reference: tvPhone
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:96:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:97:25 Unresolved reference: tvRole
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:97:32 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:98:25 Unresolved reference: tvJoinDate
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:98:36 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:104:49 Unresolved reference: placeholder_profile
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:105:43 Unresolved reference: placeholder_profile
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:117:21 Unresolved reference: progressBar
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileActivity.kt:117:33 Variable expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:35:45 Unresolved reference: getCurrentUserId
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:37:56 Unresolved reference: collect
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:37:66 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:61:56 Type mismatch: inferred type is UserEntity but User was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:84:56 Type mismatch: inferred type is UserEntity but User was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:102:45 Unresolved reference: uploadProfileImage
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/ProfileViewModel.kt:111:51 Type mismatch: inferred type is UserEntity but User was expected
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/profile/SecurityActivity.kt:28:45 Unresolved reference: R
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportActivity.kt:21:12 Unresolved reference: alirezaafkar
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportActivity.kt:22:12 Unresolved reference: alirezaafkar
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportActivity.kt:36:45 Unresolved reference: DateSetListener
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportActivity.kt:113:62 Unresolved reference: loadThisMonthData
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportActivity.kt:125:9 Unresolved reference: DatePicker
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportActivity.kt:281:5 'onDateSet' overrides nothing
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportViewModel.kt:8:43 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportViewModel.kt:9:43 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportViewModel.kt:24:36 Unresolved reference: ProductRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportViewModel.kt:25:37 Unresolved reference: CustomerRepository
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/report/ReportViewModel.kt:638:34 Unresolved reference: exportReport
e: file:///C:/New%20folder/sharen/app/src/main/java/com/example/sharen/ui/settings/SettingsActivity.kt:50:40 Unresolved reference: SecurityActivity

> Task :app:compileDebugKotlin FAILED

[Incubating] Problems report is available at: file:///C:/New%20folder/sharen/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

* Exception is:
org.gradle.api.tasks.TaskExecutionException: Execution failed for task ':app:compileDebugKotlin'.
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.lambda$executeIfValid$1(ExecuteActionsTaskExecuter.java:130)
	at org.gradle.internal.Try$Failure.ifSuccessfulOrElse(Try.java:293)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:128)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)
	at org.gradle.api.internal.tasks.execution.ProblemsTaskPathTrackingTaskExecuter.execute(ProblemsTaskPathTrackingTaskExecuter.java:40)
	at org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46)
	at org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.java:51)
	at org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57)
	at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
	at org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
	at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:331)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:318)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultTaskExecutionGraph.java:314)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:314)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:303)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
	at org.gradle.execution.plan.DefaultPlanExecutor.process(DefaultPlanExecutor.java:111)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph.executeWithServices(DefaultTaskExecutionGraph.java:138)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph.execute(DefaultTaskExecutionGraph.java:123)
	at org.gradle.execution.SelectedTaskExecutionAction.execute(SelectedTaskExecutionAction.java:35)
	at org.gradle.execution.DryRunBuildExecutionAction.execute(DryRunBuildExecutionAction.java:51)
	at org.gradle.execution.BuildOperationFiringBuildWorkerExecutor$ExecuteTasks.call(BuildOperationFiringBuildWorkerExecutor.java:54)
	at org.gradle.execution.BuildOperationFiringBuildWorkerExecutor$ExecuteTasks.call(BuildOperationFiringBuildWorkerExecutor.java:43)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.execution.BuildOperationFiringBuildWorkerExecutor.execute(BuildOperationFiringBuildWorkerExecutor.java:40)
	at org.gradle.internal.build.DefaultBuildLifecycleController.lambda$executeTasks$10(DefaultBuildLifecycleController.java:313)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
	at org.gradle.internal.model.StateTransitionController.lambda$tryTransition$8(StateTransitionController.java:177)
	at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:46)
	at org.gradle.internal.model.StateTransitionController.tryTransition(StateTransitionController.java:177)
	at org.gradle.internal.build.DefaultBuildLifecycleController.executeTasks(DefaultBuildLifecycleController.java:304)
	at org.gradle.internal.build.DefaultBuildWorkGraphController$DefaultBuildWorkGraph.runWork(DefaultBuildWorkGraphController.java:220)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:263)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:127)
	at org.gradle.composite.internal.DefaultBuildController.doRun(DefaultBuildController.java:181)
	at org.gradle.composite.internal.DefaultBuildController.access$000(DefaultBuildController.java:50)
	at org.gradle.composite.internal.DefaultBuildController$BuildOpRunnable.lambda$run$0(DefaultBuildController.java:198)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.composite.internal.DefaultBuildController$BuildOpRunnable.run(DefaultBuildController.java:198)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
Caused by: org.gradle.workers.internal.DefaultWorkerExecutor$WorkExecutionException: A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
	at org.gradle.workers.internal.DefaultWorkerExecutor$WorkItemExecution.waitForCompletion(DefaultWorkerExecutor.java:287)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.lambda$waitForItemsAndGatherFailures$2(DefaultAsyncWorkTracker.java:130)
	at org.gradle.internal.Factories$1.create(Factories.java:31)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLocks(DefaultWorkerLeaseService.java:335)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLocks(DefaultWorkerLeaseService.java:318)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLock(DefaultWorkerLeaseService.java:323)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForItemsAndGatherFailures(DefaultAsyncWorkTracker.java:126)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForItemsAndGatherFailures(DefaultAsyncWorkTracker.java:92)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForAll(DefaultAsyncWorkTracker.java:78)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForCompletion(DefaultAsyncWorkTracker.java:66)
	at org.gradle.api.internal.tasks.execution.TaskExecution$3.run(TaskExecution.java:252)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeAction(TaskExecution.java:229)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeActions(TaskExecution.java:212)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeWithPreviousOutputFiles(TaskExecution.java:195)
	at org.gradle.api.internal.tasks.execution.TaskExecution.execute(TaskExecution.java:162)
	at org.gradle.internal.execution.steps.ExecuteStep.executeInternal(ExecuteStep.java:105)
	at org.gradle.internal.execution.steps.ExecuteStep.access$000(ExecuteStep.java:44)
	at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:59)
	at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:56)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:56)
	at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:44)
	at org.gradle.internal.execution.steps.CancelExecutionStep.execute(CancelExecutionStep.java:42)
	at org.gradle.internal.execution.steps.TimeoutStep.executeWithoutTimeout(TimeoutStep.java:75)
	at org.gradle.internal.execution.steps.TimeoutStep.execute(TimeoutStep.java:55)
	at org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:50)
	at org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:28)
	at org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:67)
	at org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:37)
	at org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:61)
	at org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:26)
	at org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:69)
	at org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:46)
	at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:40)
	at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:29)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeWithoutCache(BuildCacheStep.java:189)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeAndStoreInCache(BuildCacheStep.java:145)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$executeWithCache$4(BuildCacheStep.java:101)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$executeWithCache$5(BuildCacheStep.java:101)
	at org.gradle.internal.Try$Success.map(Try.java:175)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeWithCache(BuildCacheStep.java:85)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$execute$0(BuildCacheStep.java:74)
	at org.gradle.internal.Either$Left.fold(Either.java:115)
	at org.gradle.internal.execution.caching.CachingState.fold(CachingState.java:62)
	at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:73)
	at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:48)
	at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:46)
	at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:35)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.executeBecause(SkipUpToDateStep.java:75)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.lambda$execute$2(SkipUpToDateStep.java:53)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:53)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:35)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedStep.java:37)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedStep.java:27)
	at org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingStateStep.java:49)
	at org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingStateStep.java:27)
	at org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:71)
	at org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:39)
	at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:65)
	at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:36)
	at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:107)
	at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:56)
	at org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecutionStep.java:64)
	at org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecutionStep.java:43)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.executeWithNonEmptySources(AbstractSkipEmptyWorkStep.java:125)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:61)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:36)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsStartedStep.execute(MarkSnapshottingInputsStartedStep.java:38)
	at org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:36)
	at org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:23)
	at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:75)
	at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:41)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.lambda$execute$0(AssignMutableWorkspaceStep.java:35)
	at org.gradle.api.internal.tasks.execution.TaskExecution$4.withWorkspace(TaskExecution.java:289)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:31)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:22)
	at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:40)
	at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:23)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$2(ExecuteWorkBuildOperationFiringStep.java:67)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.java:67)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.java:39)
	at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:46)
	at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:34)
	at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:48)
	at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:35)
	at org.gradle.internal.execution.impl.DefaultExecutionEngine$1.execute(DefaultExecutionEngine.java:61)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:127)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)
	at org.gradle.api.internal.tasks.execution.ProblemsTaskPathTrackingTaskExecuter.execute(ProblemsTaskPathTrackingTaskExecuter.java:40)
	at org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46)
	at org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.java:51)
	at org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57)
	at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
	at org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
	at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:331)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:318)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultTaskExecutionGraph.java:314)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:314)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:303)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
	at org.gradle.execution.plan.DefaultPlanExecutor.process(DefaultPlanExecutor.java:111)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph.executeWithServices(DefaultTaskExecutionGraph.java:138)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph.execute(DefaultTaskExecutionGraph.java:123)
	at org.gradle.execution.SelectedTaskExecutionAction.execute(SelectedTaskExecutionAction.java:35)
	at org.gradle.execution.DryRunBuildExecutionAction.execute(DryRunBuildExecutionAction.java:51)
	at org.gradle.execution.BuildOperationFiringBuildWorkerExecutor$ExecuteTasks.call(BuildOperationFiringBuildWorkerExecutor.java:54)
	at org.gradle.execution.BuildOperationFiringBuildWorkerExecutor$ExecuteTasks.call(BuildOperationFiringBuildWorkerExecutor.java:43)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.execution.BuildOperationFiringBuildWorkerExecutor.execute(BuildOperationFiringBuildWorkerExecutor.java:40)
	at org.gradle.internal.build.DefaultBuildLifecycleController.lambda$executeTasks$10(DefaultBuildLifecycleController.java:313)
	at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
	at org.gradle.internal.model.StateTransitionController.lambda$tryTransition$8(StateTransitionController.java:177)
	at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:46)
	at org.gradle.internal.model.StateTransitionController.tryTransition(StateTransitionController.java:177)
	at org.gradle.internal.build.DefaultBuildLifecycleController.executeTasks(DefaultBuildLifecycleController.java:304)
	at org.gradle.internal.build.DefaultBuildWorkGraphController$DefaultBuildWorkGraph.runWork(DefaultBuildWorkGraphController.java:220)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:263)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:127)
	at org.gradle.composite.internal.DefaultBuildController.doRun(DefaultBuildController.java:181)
	at org.gradle.composite.internal.DefaultBuildController.access$000(DefaultBuildController.java:50)
	at org.gradle.composite.internal.DefaultBuildController$BuildOpRunnable.lambda$run$0(DefaultBuildController.java:198)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.composite.internal.DefaultBuildController$BuildOpRunnable.run(DefaultBuildController.java:198)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
Caused by: org.jetbrains.kotlin.gradle.tasks.CompilationErrorException: Compilation error. See log for more details
	at org.jetbrains.kotlin.gradle.tasks.TasksUtilsKt.throwExceptionIfCompilationFailed(tasksUtils.kt:22)
	at org.jetbrains.kotlin.compilerRunner.GradleKotlinCompilerWork.run(GradleKotlinCompilerWork.kt:144)
	at org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction.execute(GradleCompilerRunnerWithWorkers.kt:76)
	at org.gradle.workers.internal.DefaultWorkerServer.execute(DefaultWorkerServer.java:63)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)
	at org.gradle.internal.classloader.ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)
	at org.gradle.workers.internal.AbstractWorker$1.call(AbstractWorker.java:44)
	at org.gradle.workers.internal.AbstractWorker$1.call(AbstractWorker.java:41)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.workers.internal.AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)
	at org.gradle.workers.internal.DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:194)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:127)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:169)
	at org.gradle.internal.Factories$1.create(Factories.java:31)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:263)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:127)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:132)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:164)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:133)
	... 2 more


Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.12/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD FAILED in 23m 4s
32 actionable tasks: 18 executed, 14 from cache
