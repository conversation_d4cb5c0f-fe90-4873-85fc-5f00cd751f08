package com.example.sharen.ui.dashboard;

import androidx.annotation.NonNull;
import androidx.navigation.ActionOnlyNavDirections;
import androidx.navigation.NavDirections;
import com.example.sharen.R;

public class DashboardFragmentDirections {
  private DashboardFragmentDirections() {
  }

  @NonNull
  public static NavDirections actionDashboardToCustomers() {
    return new ActionOnlyNavDirections(R.id.action_dashboard_to_customers);
  }

  @NonNull
  public static NavDirections actionDashboardToProducts() {
    return new ActionOnlyNavDirections(R.id.action_dashboard_to_products);
  }

  @NonNull
  public static NavDirections actionDashboardToInvoices() {
    return new ActionOnlyNavDirections(R.id.action_dashboard_to_invoices);
  }
}
