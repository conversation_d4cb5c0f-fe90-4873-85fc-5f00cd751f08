// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySalesInvoiceBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button btnAddItem;

  @NonNull
  public final Button btnRecalculate;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final Button btnSelectCustomer;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final TextInputEditText etDiscount;

  @NonNull
  public final TextInputEditText etTax;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvInvoiceItems;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvFinalAmount;

  @NonNull
  public final TextView tvInvoiceNumber;

  @NonNull
  public final TextView tvSubtotal;

  private ActivitySalesInvoiceBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button btnAddItem, @NonNull Button btnRecalculate, @NonNull Button btnSave,
      @NonNull Button btnSelectCustomer, @NonNull TextView emptyView,
      @NonNull TextInputEditText etDiscount, @NonNull TextInputEditText etTax,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView rvInvoiceItems,
      @NonNull Toolbar toolbar, @NonNull TextView tvCustomerName, @NonNull TextView tvFinalAmount,
      @NonNull TextView tvInvoiceNumber, @NonNull TextView tvSubtotal) {
    this.rootView = rootView;
    this.btnAddItem = btnAddItem;
    this.btnRecalculate = btnRecalculate;
    this.btnSave = btnSave;
    this.btnSelectCustomer = btnSelectCustomer;
    this.emptyView = emptyView;
    this.etDiscount = etDiscount;
    this.etTax = etTax;
    this.progressBar = progressBar;
    this.rvInvoiceItems = rvInvoiceItems;
    this.toolbar = toolbar;
    this.tvCustomerName = tvCustomerName;
    this.tvFinalAmount = tvFinalAmount;
    this.tvInvoiceNumber = tvInvoiceNumber;
    this.tvSubtotal = tvSubtotal;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySalesInvoiceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySalesInvoiceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_sales_invoice, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySalesInvoiceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAddItem;
      Button btnAddItem = ViewBindings.findChildViewById(rootView, id);
      if (btnAddItem == null) {
        break missingId;
      }

      id = R.id.btnRecalculate;
      Button btnRecalculate = ViewBindings.findChildViewById(rootView, id);
      if (btnRecalculate == null) {
        break missingId;
      }

      id = R.id.btnSave;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btnSelectCustomer;
      Button btnSelectCustomer = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectCustomer == null) {
        break missingId;
      }

      id = R.id.emptyView;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.etDiscount;
      TextInputEditText etDiscount = ViewBindings.findChildViewById(rootView, id);
      if (etDiscount == null) {
        break missingId;
      }

      id = R.id.etTax;
      TextInputEditText etTax = ViewBindings.findChildViewById(rootView, id);
      if (etTax == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rvInvoiceItems;
      RecyclerView rvInvoiceItems = ViewBindings.findChildViewById(rootView, id);
      if (rvInvoiceItems == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCustomerName;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tvFinalAmount;
      TextView tvFinalAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvFinalAmount == null) {
        break missingId;
      }

      id = R.id.tvInvoiceNumber;
      TextView tvInvoiceNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceNumber == null) {
        break missingId;
      }

      id = R.id.tvSubtotal;
      TextView tvSubtotal = ViewBindings.findChildViewById(rootView, id);
      if (tvSubtotal == null) {
        break missingId;
      }

      return new ActivitySalesInvoiceBinding((CoordinatorLayout) rootView, btnAddItem,
          btnRecalculate, btnSave, btnSelectCustomer, emptyView, etDiscount, etTax, progressBar,
          rvInvoiceItems, toolbar, tvCustomerName, tvFinalAmount, tvInvoiceNumber, tvSubtotal);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
