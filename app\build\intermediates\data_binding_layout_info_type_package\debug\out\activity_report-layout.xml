<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_report" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_report.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_report_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="168" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="74"/></Target><Target id="@+id/tabLayout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="17" startOffset="8" endLine="44" endOffset="52"/></Target><Target id="@+id/tvDateRange" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="65" endOffset="52"/></Target><Target id="@+id/chipToday" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="77" startOffset="20" endLine="81" endOffset="54"/></Target><Target id="@+id/chipThisWeek" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="83" startOffset="20" endLine="87" endOffset="58"/></Target><Target id="@+id/chipThisMonth" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="89" startOffset="20" endLine="93" endOffset="59"/></Target><Target id="@+id/chipThisYear" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="95" startOffset="20" endLine="99" endOffset="58"/></Target><Target id="@+id/btnSelectDate" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="105" startOffset="12" endLine="110" endOffset="59"/></Target><Target id="@+id/lineChart" view="com.github.mikephil.charting.charts.LineChart"><Expressions/><location startLine="117" startOffset="16" endLine="121" endOffset="47"/></Target><Target id="@+id/pieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="123" startOffset="16" endLine="127" endOffset="47"/></Target><Target id="@+id/barChart" view="com.github.mikephil.charting.charts.BarChart"><Expressions/><location startLine="129" startOffset="16" endLine="133" endOffset="47"/></Target><Target id="@+id/radarChart" view="com.github.mikephil.charting.charts.RadarChart"><Expressions/><location startLine="135" startOffset="16" endLine="139" endOffset="47"/></Target><Target id="@+id/tableLayout" view="TableLayout"><Expressions/><location startLine="143" startOffset="12" endLine="148" endOffset="44"/></Target><Target id="@+id/btnExport" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="150" startOffset="12" endLine="155" endOffset="54"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="161" startOffset="4" endLine="166" endOffset="49"/></Target></Targets></Layout>