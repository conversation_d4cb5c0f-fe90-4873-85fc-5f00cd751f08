package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.data.model.Customer
import java.util.Date
import java.util.UUID

@Entity(
    tableName = "customers",
    foreignKeys = [
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index("userId")]
)
data class CustomerEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val name: String,
    val phone: String,
    val address: String? = null,
    val notes: String? = null,
    val debtAmount: Long = 0,
    val totalPurchases: Long = 0,
    val lastPurchaseDate: Long? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    fun toCustomer(): Customer = Customer(
        id = id,
        userId = userId,
        name = name,
        phone = phone,
        address = address,
        notes = notes,
        debtAmount = debtAmount,
        totalPurchases = totalPurchases,
        lastPurchaseDate = lastPurchaseDate?.let { Date(it) },
        createdAt = Date(createdAt),
        updatedAt = Date(updatedAt)
    )

    companion object {
        fun fromCustomer(customer: Customer): CustomerEntity = CustomerEntity(
            id = customer.id,
            userId = customer.userId,
            name = customer.name,
            phone = customer.phone,
            address = customer.address,
            notes = customer.notes,
            debtAmount = customer.debtAmount,
            totalPurchases = customer.totalPurchases,
            lastPurchaseDate = customer.lastPurchaseDate?.time,
            createdAt = customer.createdAt.time,
            updatedAt = customer.updatedAt.time
        )
    }
}