<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_notification" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_notification_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="36" endOffset="35"/></Target><Target id="@+id/ivIcon" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="58"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="34" endOffset="47"/></Target></Targets></Layout>