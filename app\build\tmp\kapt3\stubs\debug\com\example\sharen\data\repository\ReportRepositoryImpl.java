package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\b\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ*\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\r\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000e\u0010\u000bJ*\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0010\u0010\u000bJ*\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u000bJ*\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u000bJ\"\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0017\u0010\u0018J2\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\"\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00160\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b \u0010\u0018J\"\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00160\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\"\u0010\u0018J*\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010$\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b%\u0010&J*\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010$\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b(\u0010&J\"\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00160\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b*\u0010\u0018J\"\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00160\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b,\u0010\u0018J2\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b1\u00102J2\u00103\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u00102J2\u00105\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b6\u0010\u001eJ2\u00107\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b8\u0010\u001eJ2\u00109\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b:\u0010\u001eJ2\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b<\u0010\u001eJ*\u0010=\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010.\u001a\u00020/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b>\u0010?J*\u0010@\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010.\u001a\u00020/H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bA\u0010?J\u0014\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160D0CH\u0016J\u0014\u0010E\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160D0CH\u0016J*\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\b\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bG\u0010\u000bJ!\u0010H\u001a\u00020I2\u0006\u0010\u001a\u001a\u00020J2\u0006\u0010\u001c\u001a\u00020JH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010KJ*\u0010L\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010\r\u001a\u00020\tH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bM\u0010\u000bJ$\u0010N\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160D0C2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001bH\u0016J\u001c\u0010O\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160D0C2\u0006\u0010P\u001a\u00020QH\u0016J*\u0010R\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010S\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bT\u0010UJ*\u0010V\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010W\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bX\u0010UJ*\u0010Y\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u0010S\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bZ\u0010UR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006["}, d2 = {"Lcom/example/sharen/data/repository/ReportRepositoryImpl;", "Lcom/example/sharen/data/repository/ReportRepository;", "remoteDataSource", "Lcom/example/sharen/data/remote/ReportRemoteDataSource;", "(Lcom/example/sharen/data/remote/ReportRemoteDataSource;)V", "deleteReport", "Lkotlin/Result;", "", "reportId", "", "deleteReport-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteReportTemplate", "templateId", "deleteReportTemplate-gIAlu-s", "exportReportToCsv", "exportReportToCsv-gIAlu-s", "exportReportToExcel", "exportReportToExcel-gIAlu-s", "exportReportToPdf", "exportReportToPdf-gIAlu-s", "generateCurrentInventoryReport", "Lcom/example/sharen/data/model/Report;", "generateCurrentInventoryReport-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateCustomerActivityReport", "startDate", "Ljava/util/Date;", "endDate", "generateCustomerActivityReport-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateCustomerDebtReport", "generateCustomerDebtReport-IoAF18A", "generateCustomerLoyaltyReport", "generateCustomerLoyaltyReport-IoAF18A", "generateDailyFinancialReport", "date", "generateDailyFinancialReport-gIAlu-s", "(Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateDailySalesReport", "generateDailySalesReport-gIAlu-s", "generateDebtReport", "generateDebtReport-IoAF18A", "generateLowStockReport", "generateLowStockReport-IoAF18A", "generateMonthlyFinancialReport", "year", "", "month", "generateMonthlyFinancialReport-0E7RQCE", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateMonthlySalesReport", "generateMonthlySalesReport-0E7RQCE", "generateProfitLossReport", "generateProfitLossReport-0E7RQCE", "generateSalesByCategoryReport", "generateSalesByCategoryReport-0E7RQCE", "generateSalesByCustomerReport", "generateSalesByCustomerReport-0E7RQCE", "generateStockMovementReport", "generateStockMovementReport-0E7RQCE", "generateYearlyFinancialReport", "generateYearlyFinancialReport-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateYearlySalesReport", "generateYearlySalesReport-gIAlu-s", "getAllReportTemplates", "Lkotlinx/coroutines/flow/Flow;", "", "getAllReports", "getReport", "getReport-gIAlu-s", "getReportData", "Lcom/example/sharen/data/model/ReportData;", "", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReportTemplate", "getReportTemplate-gIAlu-s", "getReportsByDateRange", "getReportsByType", "type", "Lcom/example/sharen/data/model/ReportType;", "saveReport", "report", "saveReport-gIAlu-s", "(Lcom/example/sharen/data/model/Report;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveReportTemplate", "template", "saveReportTemplate-gIAlu-s", "updateReport", "updateReport-gIAlu-s", "app_debug"})
public final class ReportRepositoryImpl extends com.example.sharen.data.repository.ReportRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.ReportRemoteDataSource remoteDataSource = null;
    
    @javax.inject.Inject
    public ReportRepositoryImpl(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.ReportRemoteDataSource remoteDataSource) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getReportData(long startDate, long endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.ReportData> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getAllReports() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getReportsByType(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.ReportType type) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getReportsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getAllReportTemplates() {
        return null;
    }
}