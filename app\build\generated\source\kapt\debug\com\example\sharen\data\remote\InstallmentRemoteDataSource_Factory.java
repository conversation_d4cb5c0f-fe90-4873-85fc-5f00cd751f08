package com.example.sharen.data.remote;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InstallmentRemoteDataSource_Factory implements Factory<InstallmentRemoteDataSource> {
  private final Provider<SupabaseApiService> apiServiceProvider;

  public InstallmentRemoteDataSource_Factory(Provider<SupabaseApiService> apiServiceProvider) {
    this.apiServiceProvider = apiServiceProvider;
  }

  @Override
  public InstallmentRemoteDataSource get() {
    return newInstance(apiServiceProvider.get());
  }

  public static InstallmentRemoteDataSource_Factory create(
      Provider<SupabaseApiService> apiServiceProvider) {
    return new InstallmentRemoteDataSource_Factory(apiServiceProvider);
  }

  public static InstallmentRemoteDataSource newInstance(SupabaseApiService apiService) {
    return new InstallmentRemoteDataSource(apiService);
  }
}
