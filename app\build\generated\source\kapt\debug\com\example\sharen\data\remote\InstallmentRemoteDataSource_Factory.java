package com.example.sharen.data.remote;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InstallmentRemoteDataSource_Factory implements Factory<InstallmentRemoteDataSource> {
  @Override
  public InstallmentRemoteDataSource get() {
    return newInstance();
  }

  public static InstallmentRemoteDataSource_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static InstallmentRemoteDataSource newInstance() {
    return new InstallmentRemoteDataSource();
  }

  private static final class InstanceHolder {
    private static final InstallmentRemoteDataSource_Factory INSTANCE = new InstallmentRemoteDataSource_Factory();
  }
}
