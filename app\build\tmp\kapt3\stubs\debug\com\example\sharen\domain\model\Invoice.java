package com.example.sharen.domain.model;

/**
 * Domain Model برای فاکتور
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u001a\n\u0002\u0010\b\n\u0002\b,\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u00e5\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u0012\u0006\u0010\f\u001a\u00020\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\r\u0012\b\b\u0002\u0010\u000f\u001a\u00020\r\u0012\u0006\u0010\u0010\u001a\u00020\r\u0012\b\b\u0002\u0010\u0011\u001a\u00020\r\u0012\u0006\u0010\u0012\u001a\u00020\r\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0016\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0018\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u001b\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0018\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0018\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010 J\t\u0010I\u001a\u00020\u0003H\u00c6\u0003J\t\u0010J\u001a\u00020\rH\u00c6\u0003J\t\u0010K\u001a\u00020\rH\u00c6\u0003J\t\u0010L\u001a\u00020\rH\u00c6\u0003J\t\u0010M\u001a\u00020\rH\u00c6\u0003J\t\u0010N\u001a\u00020\u0014H\u00c6\u0003J\t\u0010O\u001a\u00020\u0016H\u00c6\u0003J\u000b\u0010P\u001a\u0004\u0018\u00010\u0018H\u00c6\u0003J\u000b\u0010Q\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010R\u001a\u00020\u001bH\u00c6\u0003J\t\u0010S\u001a\u00020\u0018H\u00c6\u0003J\t\u0010T\u001a\u00020\u0003H\u00c6\u0003J\t\u0010U\u001a\u00020\u0018H\u00c6\u0003J\u000b\u0010V\u001a\u0004\u0018\u00010\u0018H\u00c6\u0003J\u000b\u0010W\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010X\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000f\u0010\\\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00c6\u0003J\t\u0010]\u001a\u00020\rH\u00c6\u0003J\t\u0010^\u001a\u00020\rH\u00c6\u0003J\u00f7\u0001\u0010_\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\r2\b\b\u0002\u0010\u000f\u001a\u00020\r2\b\b\u0002\u0010\u0010\u001a\u00020\r2\b\b\u0002\u0010\u0011\u001a\u00020\r2\b\b\u0002\u0010\u0012\u001a\u00020\r2\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u00162\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00182\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u001a\u001a\u00020\u001b2\b\b\u0002\u0010\u001c\u001a\u00020\u00182\b\b\u0002\u0010\u001d\u001a\u00020\u00182\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00182\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\t\u0010`\u001a\u000206H\u00d6\u0001J\u0013\u0010a\u001a\u00020\u001b2\b\u0010b\u001a\u0004\u0018\u00010cH\u00d6\u0003J\t\u0010d\u001a\u000206H\u00d6\u0001J\t\u0010e\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010f\u001a\u00020g2\u0006\u0010h\u001a\u00020i2\u0006\u0010j\u001a\u000206H\u00d6\u0001R\u0013\u0010\u001e\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0013\u0010\u001f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0011\u0010%\u001a\u00020\r8F\u00a2\u0006\u0006\u001a\u0004\b&\u0010\'R\u0011\u0010\u001c\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\"R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010$R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010$R\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\'R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\"R\u0011\u0010\u0010\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\'R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010$R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010$R\u0011\u0010\u001a\u001a\u00020\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u00100R\u0011\u00101\u001a\u00020\u001b8F\u00a2\u0006\u0006\u001a\u0004\b1\u00100R\u0011\u00102\u001a\u00020\u001b8F\u00a2\u0006\u0006\u001a\u0004\b2\u00100R\u0011\u00103\u001a\u00020\u001b8F\u00a2\u0006\u0006\u001a\u0004\b3\u00100R\u0011\u00104\u001a\u00020\u001b8F\u00a2\u0006\u0006\u001a\u0004\b4\u00100R\u0011\u00105\u001a\u0002068F\u00a2\u0006\u0006\u001a\u0004\b7\u00108R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010$R\u0011\u0010\u0011\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010\'R\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010>R\u0011\u0010\u0012\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010\'R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010$R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010$R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010CR\u0011\u0010\u000f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010\'R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010\'R\u0011\u0010F\u001a\u0002068F\u00a2\u0006\u0006\u001a\u0004\bG\u00108R\u0011\u0010\u001d\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010\"\u00a8\u0006k"}, d2 = {"Lcom/example/sharen/domain/model/Invoice;", "Landroid/os/Parcelable;", "id", "", "invoiceNumber", "customerId", "customerName", "sellerId", "sellerName", "items", "", "Lcom/example/sharen/domain/model/InvoiceItem;", "totalAmount", "", "discountAmount", "taxAmount", "finalAmount", "paidAmount", "remainingAmount", "status", "Lcom/example/sharen/domain/model/InvoiceStatus;", "paymentType", "Lcom/example/sharen/domain/model/PaymentType;", "dueDate", "Ljava/util/Date;", "notes", "isDeleted", "", "createdAt", "updatedAt", "approvedAt", "approvedBy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;JJJJJJLcom/example/sharen/domain/model/InvoiceStatus;Lcom/example/sharen/domain/model/PaymentType;Ljava/util/Date;Ljava/lang/String;ZLjava/util/Date;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;)V", "getApprovedAt", "()Ljava/util/Date;", "getApprovedBy", "()Ljava/lang/String;", "calculatedRemainingAmount", "getCalculatedRemainingAmount", "()J", "getCreatedAt", "getCustomerId", "getCustomerName", "getDiscountAmount", "getDueDate", "getFinalAmount", "getId", "getInvoiceNumber", "()Z", "isFullyPaid", "isInstallment", "isOverdue", "isPartiallyPaid", "itemCount", "", "getItemCount", "()I", "getItems", "()Ljava/util/List;", "getNotes", "getPaidAmount", "getPaymentType", "()Lcom/example/sharen/domain/model/PaymentType;", "getRemainingAmount", "getSellerId", "getSellerName", "getStatus", "()Lcom/example/sharen/domain/model/InvoiceStatus;", "getTaxAmount", "getTotalAmount", "totalQuantity", "getTotalQuantity", "getUpdatedAt", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize
public final class Invoice implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String invoiceNumber = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String customerId = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String customerName = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sellerId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sellerName = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.example.sharen.domain.model.InvoiceItem> items = null;
    private final long totalAmount = 0L;
    private final long discountAmount = 0L;
    private final long taxAmount = 0L;
    private final long finalAmount = 0L;
    private final long paidAmount = 0L;
    private final long remainingAmount = 0L;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.model.InvoiceStatus status = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.model.PaymentType paymentType = null;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date dueDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    private final boolean isDeleted = false;
    @org.jetbrains.annotations.NotNull
    private final java.util.Date createdAt = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Date updatedAt = null;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date approvedAt = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String approvedBy = null;
    
    public Invoice(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName, @org.jetbrains.annotations.Nullable
    java.lang.String sellerId, @org.jetbrains.annotations.Nullable
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.domain.model.InvoiceItem> items, long totalAmount, long discountAmount, long taxAmount, long finalAmount, long paidAmount, long remainingAmount, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.InvoiceStatus status, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.PaymentType paymentType, @org.jetbrains.annotations.Nullable
    java.util.Date dueDate, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean isDeleted, @org.jetbrains.annotations.NotNull
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull
    java.util.Date updatedAt, @org.jetbrains.annotations.Nullable
    java.util.Date approvedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getInvoiceNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCustomerId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCustomerName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSellerId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSellerName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.domain.model.InvoiceItem> getItems() {
        return null;
    }
    
    public final long getTotalAmount() {
        return 0L;
    }
    
    public final long getDiscountAmount() {
        return 0L;
    }
    
    public final long getTaxAmount() {
        return 0L;
    }
    
    public final long getFinalAmount() {
        return 0L;
    }
    
    public final long getPaidAmount() {
        return 0L;
    }
    
    public final long getRemainingAmount() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.InvoiceStatus getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.PaymentType getPaymentType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getDueDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date getCreatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date getUpdatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getApprovedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getApprovedBy() {
        return null;
    }
    
    public final boolean isFullyPaid() {
        return false;
    }
    
    public final boolean isPartiallyPaid() {
        return false;
    }
    
    public final boolean isOverdue() {
        return false;
    }
    
    public final long getCalculatedRemainingAmount() {
        return 0L;
    }
    
    public final boolean isInstallment() {
        return false;
    }
    
    public final int getItemCount() {
        return 0;
    }
    
    public final int getTotalQuantity() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component10() {
        return 0L;
    }
    
    public final long component11() {
        return 0L;
    }
    
    public final long component12() {
        return 0L;
    }
    
    public final long component13() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.InvoiceStatus component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.PaymentType component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component17() {
        return null;
    }
    
    public final boolean component18() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.domain.model.InvoiceItem> component7() {
        return null;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.Invoice copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName, @org.jetbrains.annotations.Nullable
    java.lang.String sellerId, @org.jetbrains.annotations.Nullable
    java.lang.String sellerName, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.domain.model.InvoiceItem> items, long totalAmount, long discountAmount, long taxAmount, long finalAmount, long paidAmount, long remainingAmount, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.InvoiceStatus status, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.PaymentType paymentType, @org.jetbrains.annotations.Nullable
    java.util.Date dueDate, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean isDeleted, @org.jetbrains.annotations.NotNull
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull
    java.util.Date updatedAt, @org.jetbrains.annotations.Nullable
    java.util.Date approvedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy) {
        return null;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
}