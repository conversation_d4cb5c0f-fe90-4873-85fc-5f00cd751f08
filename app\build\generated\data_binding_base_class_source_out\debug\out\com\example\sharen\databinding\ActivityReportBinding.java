// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TableLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.charts.RadarChart;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityReportBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final BarChart barChart;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final MaterialButton btnExport;

  @NonNull
  public final MaterialButton btnSelectDate;

  @NonNull
  public final Chip chipThisMonth;

  @NonNull
  public final Chip chipThisWeek;

  @NonNull
  public final Chip chipThisYear;

  @NonNull
  public final Chip chipToday;

  @NonNull
  public final LineChart lineChart;

  @NonNull
  public final PieChart pieChart;

  @NonNull
  public final RadarChart radarChart;

  @NonNull
  public final TabLayout tabLayout;

  @NonNull
  public final TableLayout tableLayout;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvDateRange;

  private ActivityReportBinding(@NonNull CoordinatorLayout rootView, @NonNull BarChart barChart,
      @NonNull BottomNavigationView bottomNavigation, @NonNull MaterialButton btnExport,
      @NonNull MaterialButton btnSelectDate, @NonNull Chip chipThisMonth,
      @NonNull Chip chipThisWeek, @NonNull Chip chipThisYear, @NonNull Chip chipToday,
      @NonNull LineChart lineChart, @NonNull PieChart pieChart, @NonNull RadarChart radarChart,
      @NonNull TabLayout tabLayout, @NonNull TableLayout tableLayout, @NonNull Toolbar toolbar,
      @NonNull TextView tvDateRange) {
    this.rootView = rootView;
    this.barChart = barChart;
    this.bottomNavigation = bottomNavigation;
    this.btnExport = btnExport;
    this.btnSelectDate = btnSelectDate;
    this.chipThisMonth = chipThisMonth;
    this.chipThisWeek = chipThisWeek;
    this.chipThisYear = chipThisYear;
    this.chipToday = chipToday;
    this.lineChart = lineChart;
    this.pieChart = pieChart;
    this.radarChart = radarChart;
    this.tabLayout = tabLayout;
    this.tableLayout = tableLayout;
    this.toolbar = toolbar;
    this.tvDateRange = tvDateRange;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityReportBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityReportBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_report, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityReportBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.barChart;
      BarChart barChart = ViewBindings.findChildViewById(rootView, id);
      if (barChart == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btnExport;
      MaterialButton btnExport = ViewBindings.findChildViewById(rootView, id);
      if (btnExport == null) {
        break missingId;
      }

      id = R.id.btnSelectDate;
      MaterialButton btnSelectDate = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectDate == null) {
        break missingId;
      }

      id = R.id.chipThisMonth;
      Chip chipThisMonth = ViewBindings.findChildViewById(rootView, id);
      if (chipThisMonth == null) {
        break missingId;
      }

      id = R.id.chipThisWeek;
      Chip chipThisWeek = ViewBindings.findChildViewById(rootView, id);
      if (chipThisWeek == null) {
        break missingId;
      }

      id = R.id.chipThisYear;
      Chip chipThisYear = ViewBindings.findChildViewById(rootView, id);
      if (chipThisYear == null) {
        break missingId;
      }

      id = R.id.chipToday;
      Chip chipToday = ViewBindings.findChildViewById(rootView, id);
      if (chipToday == null) {
        break missingId;
      }

      id = R.id.lineChart;
      LineChart lineChart = ViewBindings.findChildViewById(rootView, id);
      if (lineChart == null) {
        break missingId;
      }

      id = R.id.pieChart;
      PieChart pieChart = ViewBindings.findChildViewById(rootView, id);
      if (pieChart == null) {
        break missingId;
      }

      id = R.id.radarChart;
      RadarChart radarChart = ViewBindings.findChildViewById(rootView, id);
      if (radarChart == null) {
        break missingId;
      }

      id = R.id.tabLayout;
      TabLayout tabLayout = ViewBindings.findChildViewById(rootView, id);
      if (tabLayout == null) {
        break missingId;
      }

      id = R.id.tableLayout;
      TableLayout tableLayout = ViewBindings.findChildViewById(rootView, id);
      if (tableLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvDateRange;
      TextView tvDateRange = ViewBindings.findChildViewById(rootView, id);
      if (tvDateRange == null) {
        break missingId;
      }

      return new ActivityReportBinding((CoordinatorLayout) rootView, barChart, bottomNavigation,
          btnExport, btnSelectDate, chipThisMonth, chipThisWeek, chipThisYear, chipToday, lineChart,
          pieChart, radarChart, tabLayout, tableLayout, toolbar, tvDateRange);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
