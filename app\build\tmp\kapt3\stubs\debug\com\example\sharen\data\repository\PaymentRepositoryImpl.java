package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J*\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000e\u0010\u000fJ*\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0011\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u0013J*\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\n2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0016\u0010\u000fJ\u0014\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00190\u0018H\u0016J*\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\n2\u0006\u0010\u001c\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\u000fJ*\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001f\u0010\u000fJ>\u0010 \u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\"0!0\n2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020$H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b&\u0010\'J\u001c\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00190\u00182\u0006\u0010\u001c\u001a\u00020\rH\u0016J$\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00190\u00182\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020$H\u0016J\u001c\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00190\u00182\u0006\u0010+\u001a\u00020,H\u0016J2\u0010-\u001a\b\u0012\u0004\u0012\u00020\u001b0\n2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020$H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b.\u0010\'J2\u0010/\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\r2\u0006\u00100\u001a\u00020\rH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b1\u00102J*\u00103\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0011\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u0010\u0013R\u0014\u0010\u0002\u001a\u00020\u0003X\u0096\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00065"}, d2 = {"Lcom/example/sharen/data/repository/PaymentRepositoryImpl;", "Lcom/example/sharen/data/repository/PaymentRepository;", "paymentDao", "Lcom/example/sharen/data/local/dao/PaymentDao;", "remoteDataSource", "Lcom/example/sharen/data/remote/PaymentRemoteDataSource;", "(Lcom/example/sharen/data/local/dao/PaymentDao;Lcom/example/sharen/data/remote/PaymentRemoteDataSource;)V", "getPaymentDao", "()Lcom/example/sharen/data/local/dao/PaymentDao;", "confirmPayment", "Lkotlin/Result;", "Lcom/example/sharen/data/model/Payment;", "paymentId", "", "confirmPayment-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPayment", "payment", "createPayment-gIAlu-s", "(Lcom/example/sharen/data/model/Payment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePayment", "", "deletePayment-gIAlu-s", "getAllPayments", "Lkotlinx/coroutines/flow/Flow;", "", "getCustomerTotalPayments", "", "customerId", "getCustomerTotalPayments-gIAlu-s", "getPayment", "getPayment-gIAlu-s", "getPaymentStatistics", "", "", "startDate", "Ljava/util/Date;", "endDate", "getPaymentStatistics-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPaymentsByCustomer", "getPaymentsByDateRange", "getPaymentsByStatus", "status", "Lcom/example/sharen/data/model/PaymentStatus;", "getTotalPaymentsByDateRange", "getTotalPaymentsByDateRange-0E7RQCE", "rejectPayment", "reason", "rejectPayment-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePayment", "updatePayment-gIAlu-s", "app_debug"})
public final class PaymentRepositoryImpl extends com.example.sharen.data.repository.PaymentRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.PaymentDao paymentDao = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.PaymentRemoteDataSource remoteDataSource = null;
    
    @javax.inject.Inject
    public PaymentRepositoryImpl(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.PaymentDao paymentDao, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.PaymentRemoteDataSource remoteDataSource) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.data.local.dao.PaymentDao getPaymentDao() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getAllPayments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentStatus status) {
        return null;
    }
}