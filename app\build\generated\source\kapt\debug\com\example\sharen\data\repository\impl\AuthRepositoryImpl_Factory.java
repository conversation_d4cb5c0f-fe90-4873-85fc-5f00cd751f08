package com.example.sharen.data.repository.impl;

import com.example.sharen.data.local.dao.UserDao;
import com.example.sharen.data.remote.AuthRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthRepositoryImpl_Factory implements Factory<AuthRepositoryImpl> {
  private final Provider<UserDao> userDaoProvider;

  private final Provider<AuthRemoteDataSource> authRemoteDataSourceProvider;

  public AuthRepositoryImpl_Factory(Provider<UserDao> userDaoProvider,
      Provider<AuthRemoteDataSource> authRemoteDataSourceProvider) {
    this.userDaoProvider = userDaoProvider;
    this.authRemoteDataSourceProvider = authRemoteDataSourceProvider;
  }

  @Override
  public AuthRepositoryImpl get() {
    return newInstance(userDaoProvider.get(), authRemoteDataSourceProvider.get());
  }

  public static AuthRepositoryImpl_Factory create(Provider<UserDao> userDaoProvider,
      Provider<AuthRemoteDataSource> authRemoteDataSourceProvider) {
    return new AuthRepositoryImpl_Factory(userDaoProvider, authRemoteDataSourceProvider);
  }

  public static AuthRepositoryImpl newInstance(UserDao userDao,
      AuthRemoteDataSource authRemoteDataSource) {
    return new AuthRepositoryImpl(userDao, authRemoteDataSource);
  }
}
