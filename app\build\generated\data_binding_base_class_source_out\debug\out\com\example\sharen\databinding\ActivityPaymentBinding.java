// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPaymentBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final Button btnSubmitPayment;

  @NonNull
  public final TextInputEditText etAmount;

  @NonNull
  public final TextInputEditText etNotes;

  @NonNull
  public final TextInputEditText etReferenceNumber;

  @NonNull
  public final Spinner spinnerPaymentMethod;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvInvoiceDate;

  @NonNull
  public final TextView tvInvoiceNumber;

  @NonNull
  public final TextView tvPaidAmount;

  @NonNull
  public final TextView tvRemainingAmount;

  @NonNull
  public final TextView tvTotalAmount;

  private ActivityPaymentBinding(@NonNull CoordinatorLayout rootView,
      @NonNull Button btnSubmitPayment, @NonNull TextInputEditText etAmount,
      @NonNull TextInputEditText etNotes, @NonNull TextInputEditText etReferenceNumber,
      @NonNull Spinner spinnerPaymentMethod, @NonNull Toolbar toolbar,
      @NonNull TextView tvCustomerName, @NonNull TextView tvInvoiceDate,
      @NonNull TextView tvInvoiceNumber, @NonNull TextView tvPaidAmount,
      @NonNull TextView tvRemainingAmount, @NonNull TextView tvTotalAmount) {
    this.rootView = rootView;
    this.btnSubmitPayment = btnSubmitPayment;
    this.etAmount = etAmount;
    this.etNotes = etNotes;
    this.etReferenceNumber = etReferenceNumber;
    this.spinnerPaymentMethod = spinnerPaymentMethod;
    this.toolbar = toolbar;
    this.tvCustomerName = tvCustomerName;
    this.tvInvoiceDate = tvInvoiceDate;
    this.tvInvoiceNumber = tvInvoiceNumber;
    this.tvPaidAmount = tvPaidAmount;
    this.tvRemainingAmount = tvRemainingAmount;
    this.tvTotalAmount = tvTotalAmount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPaymentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPaymentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_payment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPaymentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnSubmitPayment;
      Button btnSubmitPayment = ViewBindings.findChildViewById(rootView, id);
      if (btnSubmitPayment == null) {
        break missingId;
      }

      id = R.id.etAmount;
      TextInputEditText etAmount = ViewBindings.findChildViewById(rootView, id);
      if (etAmount == null) {
        break missingId;
      }

      id = R.id.etNotes;
      TextInputEditText etNotes = ViewBindings.findChildViewById(rootView, id);
      if (etNotes == null) {
        break missingId;
      }

      id = R.id.etReferenceNumber;
      TextInputEditText etReferenceNumber = ViewBindings.findChildViewById(rootView, id);
      if (etReferenceNumber == null) {
        break missingId;
      }

      id = R.id.spinnerPaymentMethod;
      Spinner spinnerPaymentMethod = ViewBindings.findChildViewById(rootView, id);
      if (spinnerPaymentMethod == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCustomerName;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tvInvoiceDate;
      TextView tvInvoiceDate = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceDate == null) {
        break missingId;
      }

      id = R.id.tvInvoiceNumber;
      TextView tvInvoiceNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceNumber == null) {
        break missingId;
      }

      id = R.id.tvPaidAmount;
      TextView tvPaidAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvPaidAmount == null) {
        break missingId;
      }

      id = R.id.tvRemainingAmount;
      TextView tvRemainingAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvRemainingAmount == null) {
        break missingId;
      }

      id = R.id.tvTotalAmount;
      TextView tvTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalAmount == null) {
        break missingId;
      }

      return new ActivityPaymentBinding((CoordinatorLayout) rootView, btnSubmitPayment, etAmount,
          etNotes, etReferenceNumber, spinnerPaymentMethod, toolbar, tvCustomerName, tvInvoiceDate,
          tvInvoiceNumber, tvPaidAmount, tvRemainingAmount, tvTotalAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
