// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityForgotPasswordBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnResetPassword;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilEmail;

  @NonNull
  public final TextView tvBackToLogin;

  @NonNull
  public final TextView tvForgotPasswordTitle;

  @NonNull
  public final TextView tvInstruction;

  private ActivityForgotPasswordBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnResetPassword, @NonNull TextInputEditText etEmail,
      @NonNull ImageView ivLogo, @NonNull ProgressBar progressBar,
      @NonNull TextInputLayout tilEmail, @NonNull TextView tvBackToLogin,
      @NonNull TextView tvForgotPasswordTitle, @NonNull TextView tvInstruction) {
    this.rootView = rootView;
    this.btnResetPassword = btnResetPassword;
    this.etEmail = etEmail;
    this.ivLogo = ivLogo;
    this.progressBar = progressBar;
    this.tilEmail = tilEmail;
    this.tvBackToLogin = tvBackToLogin;
    this.tvForgotPasswordTitle = tvForgotPasswordTitle;
    this.tvInstruction = tvInstruction;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityForgotPasswordBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityForgotPasswordBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_forgot_password, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityForgotPasswordBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_reset_password;
      MaterialButton btnResetPassword = ViewBindings.findChildViewById(rootView, id);
      if (btnResetPassword == null) {
        break missingId;
      }

      id = R.id.et_email;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.iv_logo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.til_email;
      TextInputLayout tilEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilEmail == null) {
        break missingId;
      }

      id = R.id.tv_back_to_login;
      TextView tvBackToLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvBackToLogin == null) {
        break missingId;
      }

      id = R.id.tv_forgot_password_title;
      TextView tvForgotPasswordTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPasswordTitle == null) {
        break missingId;
      }

      id = R.id.tv_instruction;
      TextView tvInstruction = ViewBindings.findChildViewById(rootView, id);
      if (tvInstruction == null) {
        break missingId;
      }

      return new ActivityForgotPasswordBinding((ConstraintLayout) rootView, btnResetPassword,
          etEmail, ivLogo, progressBar, tilEmail, tvBackToLogin, tvForgotPasswordTitle,
          tvInstruction);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
