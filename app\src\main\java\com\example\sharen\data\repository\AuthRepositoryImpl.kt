package com.example.sharen.data.repository

import android.content.SharedPreferences
import com.example.sharen.data.local.dao.UserDao
import com.example.sharen.data.local.entity.UserEntity
import com.example.sharen.data.model.UserRole
import com.example.sharen.data.model.User
import com.example.sharen.data.remote.AuthRemoteDataSource
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.gotrue.auth
import io.github.jan.supabase.gotrue.providers.Email
import io.github.jan.supabase.postgrest.postgrest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepositoryImpl @Inject constructor(
    private val supabaseClient: SupabaseClient,
    private val userDao: UserDao,
    private val sharedPreferences: SharedPreferences,
    private val remoteDataSource: AuthRemoteDataSource
) : AuthRepository {

    companion object {
        private const val KEY_USER_ID = "user_id"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
    }

    override suspend fun login(username: String, password: String): Result<User> {
        return remoteDataSource.login(username, password)
    }

    override suspend fun register(user: User): Result<User> {
        return remoteDataSource.register(user)
    }

    override suspend fun logout() {
        remoteDataSource.logout()
    }

    override fun getCurrentUser(): Flow<User?> {
        return userDao.getCurrentUser()
    }

    override suspend fun updateUser(user: User): Result<User> {
        return remoteDataSource.updateUser(user)
    }

    override suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit> {
        return remoteDataSource.changePassword(oldPassword, newPassword)
    }

    override suspend fun resetPassword(email: String): Boolean = withContext(Dispatchers.IO) {
        try {
            supabaseClient.auth.resetPasswordForEmail(email)
            return@withContext true
        } catch (e: Exception) {
            throw e
        }
    }

    override suspend fun isUserLoggedIn(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    override suspend fun getCurrentUserId(): String? {
        return sharedPreferences.getString(KEY_USER_ID, null)
    }

    private suspend fun fetchUserProfile(userId: String): UserEntity? = withContext(Dispatchers.IO) {
        try {
            val response = supabaseClient.postgrest["users"]
                .select {
                    filter {
                        eq("id", userId)
                    }
                }
                .decodeSingle<Map<String, Any>>()
            
            return@withContext UserEntity(
                id = userId,
                email = response["email"] as String,
                name = response["name"] as String,
                phone = response["phone"] as String,
                role = (response["role"] as String?) ?: UserRole.CUSTOMER.name,
                isApproved = (response["is_approved"] as Boolean?) ?: false,
                imageUrl = response["image_url"] as String?,
                referrerId = response["referrer_id"] as String?,
                referrerCode = response["referrer_code"] as String?,
                createdAt = Date().time,
                updatedAt = Date().time
            )
        } catch (e: Exception) {
            return@withContext null
        }
    }

    private suspend fun createUserProfile(
        userId: String,
        name: String,
        email: String,
        phone: String,
        referrerCode: String?
    ): UserEntity = withContext(Dispatchers.IO) {
        // Insert user profile into Supabase
        val referrerId = if (referrerCode != null) {
            findReferrerId(referrerCode)
        } else null
        
        val newReferrerCode = generateReferrerCode()
        
        val userData = mapOf(
            "id" to userId,
            "name" to name,
            "email" to email,
            "phone" to phone,
            "role" to UserRole.CUSTOMER.name,
            "is_approved" to false,
            "referrer_id" to referrerId,
            "referrer_code" to newReferrerCode,
            "created_at" to Date().time,
            "updated_at" to Date().time
        )
        
        supabaseClient.postgrest["users"].insert(userData)
        
        return@withContext UserEntity(
            id = userId,
            email = email,
            name = name,
            phone = phone,
            role = UserRole.CUSTOMER.name,
            isApproved = false,
            imageUrl = null,
            referrerId = referrerId,
            referrerCode = newReferrerCode,
            createdAt = Date().time,
            updatedAt = Date().time
        )
    }

    private suspend fun findReferrerId(referrerCode: String): String? = withContext(Dispatchers.IO) {
        try {
            val response = supabaseClient.postgrest["users"]
                .select {
                    filter {
                        eq("referrer_code", referrerCode)
                    }
                }
                .decodeSingle<Map<String, Any>>()
            
            return@withContext response["id"] as String?
        } catch (e: Exception) {
            return@withContext null
        }
    }

    private fun generateReferrerCode(): String {
        // Generate a random 6-character alphanumeric code
        return UUID.randomUUID().toString().take(6).uppercase()
    }

    private fun saveLoginState(userId: String) {
        sharedPreferences.edit()
            .putBoolean(KEY_IS_LOGGED_IN, true)
            .putString(KEY_USER_ID, userId)
            .apply()
    }

    private fun clearLoginState() {
        sharedPreferences.edit()
            .remove(KEY_IS_LOGGED_IN)
            .remove(KEY_USER_ID)
            .apply()
    }
} 