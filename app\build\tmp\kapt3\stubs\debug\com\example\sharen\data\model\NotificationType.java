package com.example.sharen.data.model;

/**
 * انواع اعلان
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/example/sharen/data/model/NotificationType;", "", "(Ljava/lang/String;I)V", "ALL", "SYSTEM", "INVOICE", "PAYMENT", "INSTALLMENT", "USER", "PRODUCT", "app_debug"})
public enum NotificationType {
    /*public static final*/ ALL /* = new ALL() */,
    /*public static final*/ SYSTEM /* = new SYSTEM() */,
    /*public static final*/ INVOICE /* = new INVOICE() */,
    /*public static final*/ PAYMENT /* = new PAYMENT() */,
    /*public static final*/ INSTALLMENT /* = new INSTALLMENT() */,
    /*public static final*/ USER /* = new USER() */,
    /*public static final*/ PRODUCT /* = new PRODUCT() */;
    
    NotificationType() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.data.model.NotificationType> getEntries() {
        return null;
    }
}