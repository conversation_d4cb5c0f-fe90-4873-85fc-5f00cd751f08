package com.example.sharen.di;

import android.content.Context;
import com.example.sharen.data.local.SharenDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideSharenDatabaseFactory implements Factory<SharenDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideSharenDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SharenDatabase get() {
    return provideSharenDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideSharenDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideSharenDatabaseFactory(contextProvider);
  }

  public static SharenDatabase provideSharenDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSharenDatabase(context));
  }
}
