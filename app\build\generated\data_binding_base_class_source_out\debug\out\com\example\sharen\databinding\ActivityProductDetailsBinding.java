// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProductDetailsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final CollapsingToolbarLayout collapsingToolbar;

  @NonNull
  public final FloatingActionButton fabEdit;

  @NonNull
  public final ImageView ivProductImage;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvBarcode;

  @NonNull
  public final TextView tvCategory;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvMinimumStock;

  @NonNull
  public final TextView tvProductCode;

  @NonNull
  public final TextView tvProductName;

  @NonNull
  public final TextView tvPurchasePrice;

  @NonNull
  public final TextView tvSellingPrice;

  @NonNull
  public final TextView tvStock;

  private ActivityProductDetailsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull CollapsingToolbarLayout collapsingToolbar,
      @NonNull FloatingActionButton fabEdit, @NonNull ImageView ivProductImage,
      @NonNull ProgressBar progressBar, @NonNull Toolbar toolbar, @NonNull TextView tvBarcode,
      @NonNull TextView tvCategory, @NonNull TextView tvDescription,
      @NonNull TextView tvMinimumStock, @NonNull TextView tvProductCode,
      @NonNull TextView tvProductName, @NonNull TextView tvPurchasePrice,
      @NonNull TextView tvSellingPrice, @NonNull TextView tvStock) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.collapsingToolbar = collapsingToolbar;
    this.fabEdit = fabEdit;
    this.ivProductImage = ivProductImage;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
    this.tvBarcode = tvBarcode;
    this.tvCategory = tvCategory;
    this.tvDescription = tvDescription;
    this.tvMinimumStock = tvMinimumStock;
    this.tvProductCode = tvProductCode;
    this.tvProductName = tvProductName;
    this.tvPurchasePrice = tvPurchasePrice;
    this.tvSellingPrice = tvSellingPrice;
    this.tvStock = tvStock;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProductDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProductDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_product_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProductDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.collapsingToolbar;
      CollapsingToolbarLayout collapsingToolbar = ViewBindings.findChildViewById(rootView, id);
      if (collapsingToolbar == null) {
        break missingId;
      }

      id = R.id.fabEdit;
      FloatingActionButton fabEdit = ViewBindings.findChildViewById(rootView, id);
      if (fabEdit == null) {
        break missingId;
      }

      id = R.id.ivProductImage;
      ImageView ivProductImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProductImage == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvBarcode;
      TextView tvBarcode = ViewBindings.findChildViewById(rootView, id);
      if (tvBarcode == null) {
        break missingId;
      }

      id = R.id.tvCategory;
      TextView tvCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvCategory == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tvMinimumStock;
      TextView tvMinimumStock = ViewBindings.findChildViewById(rootView, id);
      if (tvMinimumStock == null) {
        break missingId;
      }

      id = R.id.tvProductCode;
      TextView tvProductCode = ViewBindings.findChildViewById(rootView, id);
      if (tvProductCode == null) {
        break missingId;
      }

      id = R.id.tvProductName;
      TextView tvProductName = ViewBindings.findChildViewById(rootView, id);
      if (tvProductName == null) {
        break missingId;
      }

      id = R.id.tvPurchasePrice;
      TextView tvPurchasePrice = ViewBindings.findChildViewById(rootView, id);
      if (tvPurchasePrice == null) {
        break missingId;
      }

      id = R.id.tvSellingPrice;
      TextView tvSellingPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvSellingPrice == null) {
        break missingId;
      }

      id = R.id.tvStock;
      TextView tvStock = ViewBindings.findChildViewById(rootView, id);
      if (tvStock == null) {
        break missingId;
      }

      return new ActivityProductDetailsBinding((CoordinatorLayout) rootView, appBarLayout,
          collapsingToolbar, fabEdit, ivProductImage, progressBar, toolbar, tvBarcode, tvCategory,
          tvDescription, tvMinimumStock, tvProductCode, tvProductName, tvPurchasePrice,
          tvSellingPrice, tvStock);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
