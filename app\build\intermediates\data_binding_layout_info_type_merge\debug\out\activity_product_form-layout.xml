<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_form" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_product_form.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_form_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="348" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="22" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="49"/></Target><Target id="@+id/ivProductImage" view="ImageView"><Expressions/><location startLine="62" startOffset="24" endLine="69" endOffset="78"/></Target><Target id="@+id/fabAddImage" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="71" startOffset="24" endLine="79" endOffset="78"/></Target><Target id="@+id/imageLoadingProgressBar" view="ProgressBar"><Expressions/><location startLine="81" startOffset="24" endLine="86" endOffset="55"/></Target><Target id="@+id/etProductName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="134" startOffset="24" endLine="139" endOffset="50"/></Target><Target id="@+id/etCategory" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="150" startOffset="24" endLine="155" endOffset="50"/></Target><Target id="@+id/etDescription" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="166" startOffset="24" endLine="172" endOffset="50"/></Target><Target id="@+id/etProductCode" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="209" startOffset="24" endLine="214" endOffset="50"/></Target><Target id="@+id/etBarcode" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="225" startOffset="24" endLine="230" endOffset="50"/></Target><Target id="@+id/etPurchasePrice" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="267" startOffset="24" endLine="272" endOffset="50"/></Target><Target id="@+id/etSellingPrice" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="283" startOffset="24" endLine="288" endOffset="50"/></Target><Target id="@+id/etStock" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="299" startOffset="24" endLine="304" endOffset="50"/></Target><Target id="@+id/etMinimumStock" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="315" startOffset="24" endLine="320" endOffset="50"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="328" startOffset="12" endLine="335" endOffset="41"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="341" startOffset="4" endLine="346" endOffset="35"/></Target></Targets></Layout>