package com.example.sharen.di;

import com.example.sharen.data.api.InstallmentApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideInstallmentApiFactory implements Factory<InstallmentApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideInstallmentApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public InstallmentApi get() {
    return provideInstallmentApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideInstallmentApiFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideInstallmentApiFactory(retrofitProvider);
  }

  public static InstallmentApi provideInstallmentApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideInstallmentApi(retrofit));
  }
}
