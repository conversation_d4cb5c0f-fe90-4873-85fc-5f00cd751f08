package com.example.sharen.data.remote;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PaymentRemoteDataSource_Factory implements Factory<PaymentRemoteDataSource> {
  private final Provider<SupabaseApiService> apiServiceProvider;

  public PaymentRemoteDataSource_Factory(Provider<SupabaseApiService> apiServiceProvider) {
    this.apiServiceProvider = apiServiceProvider;
  }

  @Override
  public PaymentRemoteDataSource get() {
    return newInstance(apiServiceProvider.get());
  }

  public static PaymentRemoteDataSource_Factory create(
      Provider<SupabaseApiService> apiServiceProvider) {
    return new PaymentRemoteDataSource_Factory(apiServiceProvider);
  }

  public static PaymentRemoteDataSource newInstance(SupabaseApiService apiService) {
    return new PaymentRemoteDataSource(apiService);
  }
}
