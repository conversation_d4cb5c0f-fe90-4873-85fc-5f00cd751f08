package com.example.sharen.ui.invoice

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.sharen.R
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.data.model.Payment
import com.example.sharen.databinding.ActivityInvoiceDetailsBinding
import dagger.hilt.android.AndroidEntryPoint
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.Locale

@AndroidEntryPoint
class InvoiceDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityInvoiceDetailsBinding
    private val viewModel: InvoiceDetailsViewModel by viewModels()
    private lateinit var itemsAdapter: InvoiceItemAdapter
    private lateinit var paymentsAdapter: PaymentAdapter
    private val numberFormatter = NumberFormat.getInstance(Locale("fa"))
    private val dateFormatter = SimpleDateFormat("yyyy/MM/dd", Locale("fa"))
    
    private var currentInvoice: Invoice? = null
    
    companion object {
        const val EXTRA_INVOICE_ID = "invoice_id"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInvoiceDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val invoiceId = intent.getStringExtra(EXTRA_INVOICE_ID)
            ?: throw IllegalArgumentException("شناسه فاکتور الزامی است")

        setupUI()
        setupAdapters()
        setupObservers()
        
        viewModel.loadInvoice(invoiceId)
    }

    private fun setupUI() {
        // تنظیم نوار ابزار
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.invoice_details)
        
        // تنظیم دکمه‌ها
        binding.btnAddPayment.setOnClickListener {
            navigateToAddPayment()
        }
        
        binding.btnApproveInvoice.setOnClickListener {
            approveInvoice()
        }
        
        binding.btnEditInvoice.setOnClickListener {
            editInvoice()
        }
        
        binding.btnPrintInvoice.setOnClickListener {
            printInvoice()
        }
    }
    
    private fun setupAdapters() {
        // آداپتر آیتم‌های فاکتور
        itemsAdapter = InvoiceItemAdapter(numberFormatter)
        binding.rvInvoiceItems.apply {
            layoutManager = LinearLayoutManager(this@InvoiceDetailsActivity)
            adapter = itemsAdapter
        }
        
        // آداپتر پرداخت‌ها
        paymentsAdapter = PaymentAdapter(numberFormatter, dateFormatter)
        binding.rvPayments.apply {
            layoutManager = LinearLayoutManager(this@InvoiceDetailsActivity)
            adapter = paymentsAdapter
        }
    }
    
    private fun setupObservers() {
        // مشاهده‌گر فاکتور
        viewModel.invoice.observe(this) { invoice ->
            currentInvoice = invoice
            updateUI(invoice)
        }
        
        // مشاهده‌گر آیتم‌های فاکتور
        viewModel.invoiceItems.observe(this) { items ->
            itemsAdapter.submitList(items)
            binding.tvNoItems.visibility = if (items.isEmpty()) View.VISIBLE else View.GONE
        }
        
        // مشاهده‌گر پرداخت‌ها
        viewModel.payments.observe(this) { payments ->
            paymentsAdapter.submitList(payments)
            binding.tvNoPayments.visibility = if (payments.isEmpty()) View.VISIBLE else View.GONE
        }
        
        // مشاهده‌گر وضعیت بارگذاری
        viewModel.isLoading.observe(this) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }
        
        // مشاهده‌گر خطا
        viewModel.errorMessage.observe(this) { message ->
            if (!message.isNullOrEmpty()) {
                Toast.makeText(this, message, Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun updateUI(invoice: Invoice) {
        // اطلاعات اصلی فاکتور
        binding.tvInvoiceNumber.text = invoice.invoiceNumber
        binding.tvCustomerName.text = invoice.customerName
        binding.tvInvoiceDate.text = dateFormatter.format(invoice.createdAt)
        
        // مبالغ فاکتور
        binding.tvTotalAmount.text = "${numberFormatter.format(invoice.totalAmount)} تومان"
        binding.tvDiscount.text = "${numberFormatter.format(invoice.discount)} تومان"
        binding.tvTax.text = "${numberFormatter.format(invoice.tax)} تومان"
        binding.tvFinalAmount.text = "${numberFormatter.format(invoice.finalAmount)} تومان"
        binding.tvPaidAmount.text = "${numberFormatter.format(invoice.paidAmount)} تومان"
        binding.tvRemainingAmount.text = "${numberFormatter.format(invoice.remainingAmount)} تومان"
        
        // نوع پرداخت
        binding.tvPaymentType.text = when (invoice.paymentType) {
            com.example.sharen.data.model.PaymentType.CASH -> getString(R.string.cash)
            com.example.sharen.data.model.PaymentType.CARD -> getString(R.string.card)
            com.example.sharen.data.model.PaymentType.INSTALLMENT -> getString(R.string.installment)
            com.example.sharen.data.model.PaymentType.MIXED -> getString(R.string.mixed)
        }
        
        // وضعیت فاکتور
        binding.tvStatus.text = when (invoice.status) {
            InvoiceStatus.DRAFT -> getString(R.string.draft)
            InvoiceStatus.PENDING -> getString(R.string.pending)
            InvoiceStatus.APPROVED -> getString(R.string.approved)
            InvoiceStatus.PAID -> getString(R.string.paid)
            InvoiceStatus.PARTIALLY_PAID -> getString(R.string.partially_paid)
            InvoiceStatus.CANCELLED -> getString(R.string.cancelled)
        }
        
        // تنظیم رنگ وضعیت
        val statusColor = when (invoice.status) {
            InvoiceStatus.DRAFT -> R.color.gray
            InvoiceStatus.PENDING -> R.color.orange
            InvoiceStatus.APPROVED -> R.color.blue
            InvoiceStatus.PAID -> R.color.green
            InvoiceStatus.PARTIALLY_PAID -> R.color.yellow
            InvoiceStatus.CANCELLED -> R.color.red
        }
        binding.tvStatus.setTextColor(getColor(statusColor))
        
        // نمایش یا عدم نمایش دکمه‌ها بر اساس وضعیت فاکتور
        binding.btnApproveInvoice.visibility = 
            if (invoice.status == InvoiceStatus.DRAFT || invoice.status == InvoiceStatus.PENDING) View.VISIBLE else View.GONE
        
        binding.btnEditInvoice.visibility = 
            if (invoice.status == InvoiceStatus.DRAFT || invoice.status == InvoiceStatus.PENDING) View.VISIBLE else View.GONE
        
        binding.btnAddPayment.visibility =
            if (invoice.status == InvoiceStatus.APPROVED || invoice.status == InvoiceStatus.PARTIALLY_PAID) View.VISIBLE else View.GONE
    }
    
    private fun navigateToAddPayment() {
        currentInvoice?.let { invoice ->
            val intent = Intent(this, PaymentActivity::class.java).apply {
                putExtra(PaymentActivity.EXTRA_INVOICE_ID, invoice.id)
            }
            startActivity(intent)
        }
    }
    
    private fun approveInvoice() {
        currentInvoice?.let { invoice ->
            AlertDialog.Builder(this)
                .setTitle(R.string.approve_invoice)
                .setMessage("آیا از تأیید فاکتور ${invoice.invoiceNumber} اطمینان دارید؟")
                .setPositiveButton(R.string.confirm) { _, _ ->
                    viewModel.updateInvoiceStatus(invoice.id, InvoiceStatus.APPROVED)
                }
                .setNegativeButton(R.string.cancel, null)
                .show()
        }
    }
    
    private fun editInvoice() {
        currentInvoice?.let { invoice ->
            val intent = Intent(this, SalesInvoiceActivity::class.java).apply {
                putExtra(SalesInvoiceActivity.EXTRA_INVOICE_ID, invoice.id)
            }
            startActivity(intent)
        }
    }
    
    private fun printInvoice() {
        Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_invoice_details, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_share -> {
                shareInvoice()
                true
            }
            R.id.action_delete -> {
                promptDeleteInvoice()
                true
            }
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun shareInvoice() {
        Toast.makeText(this, getString(R.string.feature_coming_soon), Toast.LENGTH_SHORT).show()
    }
    
    private fun promptDeleteInvoice() {
        currentInvoice?.let { invoice ->
            AlertDialog.Builder(this)
                .setTitle(R.string.delete)
                .setMessage("آیا از حذف فاکتور ${invoice.invoiceNumber} اطمینان دارید؟")
                .setPositiveButton(R.string.confirm) { _, _ ->
                    viewModel.deleteInvoice(invoice.id)
                    finish()
                }
                .setNegativeButton(R.string.cancel, null)
                .show()
        }
    }
} 