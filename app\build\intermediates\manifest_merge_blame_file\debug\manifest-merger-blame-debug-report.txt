1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.sharen"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.CAMERA" />
13-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:7:5-65
13-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:7:22-62
14    <uses-permission
14-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:8:5-107
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:8:22-77
16        android:maxSdkVersion="32" />
16-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:8:78-104
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:9:5-76
17-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:9:22-73
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:25:5-68
18-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:25:22-65
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:27:5-81
19-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:27:22-78
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
20-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:28:5-77
20-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:28:22-74
21
22    <permission
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.sharen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.sharen.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:11:5-84:19
29        android:name="com.example.sharen.SharenApplication"
29-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:12:9-42
30        android:allowBackup="true"
30-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:13:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\38f86bbedb8ee84081f79de8a05a707d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:14:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:15:9-54
36        android:icon="@mipmap/ic_launcher"
36-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:16:9-43
37        android:label="@string/app_name"
37-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:17:9-41
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:18:9-54
39        android:supportsRtl="true"
39-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:19:9-35
40        android:testOnly="true"
41        android:theme="@style/Theme.Sharen" >
41-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:20:9-44
42        <activity
42-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:22:9-30:20
43            android:name="com.example.sharen.MainActivity"
43-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:23:13-41
44            android:exported="true"
44-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:24:13-36
45            android:theme="@style/Theme.Sharen.Splash" >
45-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:25:13-55
46            <intent-filter>
46-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:26:13-29:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:27:17-69
47-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:27:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:28:17-77
49-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:28:27-74
50            </intent-filter>
51        </activity>
52        <activity
52-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:32:9-35:63
53            android:name="com.example.sharen.presentation.ui.auth.LoginActivity"
53-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:33:13-63
54            android:exported="false"
54-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:34:13-37
55            android:theme="@style/Theme.Sharen.NoActionBar" />
55-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:35:13-60
56        <activity
56-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:37:9-40:63
57            android:name="com.example.sharen.presentation.ui.auth.RegisterActivity"
57-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:38:13-66
58            android:exported="false"
58-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:39:13-37
59            android:theme="@style/Theme.Sharen.NoActionBar" />
59-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:40:13-60
60        <activity
60-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:42:9-45:63
61            android:name="com.example.sharen.presentation.ui.auth.ForgotPasswordActivity"
61-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:43:13-72
62            android:exported="false"
62-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:44:13-37
63            android:theme="@style/Theme.Sharen.NoActionBar" />
63-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:45:13-60
64        <activity
64-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:47:9-49:40
65            android:name="com.example.sharen.presentation.ui.dashboard.DashboardActivity"
65-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:48:13-72
66            android:exported="false" />
66-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:49:13-37
67        <activity
67-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:51:9-53:40
68            android:name="com.example.sharen.presentation.ui.customer.CustomerActivity"
68-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:52:13-70
69            android:exported="false" />
69-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:53:13-37
70        <activity
70-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:55:9-57:40
71            android:name="com.example.sharen.presentation.ui.product.ProductActivity"
71-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:56:13-68
72            android:exported="false" />
72-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:57:13-37
73        <activity
73-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:59:9-61:40
74            android:name="com.example.sharen.presentation.ui.invoice.InvoiceActivity"
74-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:60:13-68
75            android:exported="false" />
75-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:61:13-37
76        <activity
76-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:63:9-65:40
77            android:name="com.example.sharen.presentation.ui.payment.PaymentActivity"
77-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:64:13-68
78            android:exported="false" />
78-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:65:13-37
79        <activity
79-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:67:9-69:40
80            android:name="com.example.sharen.presentation.ui.report.ReportActivity"
80-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:68:13-66
81            android:exported="false" />
81-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:69:13-37
82        <activity
82-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:71:9-73:40
83            android:name="com.example.sharen.presentation.ui.settings.SettingsActivity"
83-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:72:13-70
84            android:exported="false" />
84-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:73:13-37
85
86        <provider
87            android:name="androidx.core.content.FileProvider"
87-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:76:13-62
88            android:authorities="com.example.sharen.fileprovider"
88-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:77:13-64
89            android:exported="false"
89-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:78:13-37
90            android:grantUriPermissions="true" >
90-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:79:13-47
91            <meta-data
91-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:80:13-82:54
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:81:17-67
93                android:resource="@xml/file_paths" />
93-->C:\New folder\sharen\app\src\main\AndroidManifest.xml:82:17-51
94        </provider>
95        <provider
95-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:31:9-37:35
96            android:name="androidx.work.impl.WorkManagerInitializer"
96-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:32:13-69
97            android:authorities="com.example.sharen.workmanager-init"
97-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:33:13-68
98            android:directBootAware="false"
98-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:34:13-44
99            android:exported="false"
99-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:35:13-37
100            android:multiprocess="true" />
100-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:36:13-40
101
102        <service
102-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:39:9-44:35
103            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
103-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:40:13-88
104            android:directBootAware="false"
104-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:41:13-44
105            android:enabled="@bool/enable_system_alarm_service_default"
105-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:42:13-72
106            android:exported="false" />
106-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:43:13-37
107        <service
107-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:45:9-51:35
108            android:name="androidx.work.impl.background.systemjob.SystemJobService"
108-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:46:13-84
109            android:directBootAware="false"
109-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:47:13-44
110            android:enabled="@bool/enable_system_job_service_default"
110-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:48:13-70
111            android:exported="true"
111-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:49:13-36
112            android:permission="android.permission.BIND_JOB_SERVICE" />
112-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:50:13-69
113        <service
113-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:52:9-57:35
114            android:name="androidx.work.impl.foreground.SystemForegroundService"
114-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:53:13-81
115            android:directBootAware="false"
115-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:54:13-44
116            android:enabled="@bool/enable_system_foreground_service_default"
116-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:55:13-77
117            android:exported="false" />
117-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:56:13-37
118
119        <receiver
119-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:59:9-64:35
120            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
120-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:60:13-88
121            android:directBootAware="false"
121-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:61:13-44
122            android:enabled="true"
122-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:62:13-35
123            android:exported="false" />
123-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:63:13-37
124        <receiver
124-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:65:9-75:20
125            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
125-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:66:13-106
126            android:directBootAware="false"
126-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:67:13-44
127            android:enabled="false"
127-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:68:13-36
128            android:exported="false" >
128-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:69:13-37
129            <intent-filter>
129-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:71:13-74:29
130                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
130-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:72:17-87
130-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:72:25-84
131                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
131-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:73:17-90
131-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:73:25-87
132            </intent-filter>
133        </receiver>
134        <receiver
134-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:76:9-86:20
135            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
135-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:77:13-104
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:78:13-44
137            android:enabled="false"
137-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:79:13-36
138            android:exported="false" >
138-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:80:13-37
139            <intent-filter>
139-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:82:13-85:29
140                <action android:name="android.intent.action.BATTERY_OKAY" />
140-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:83:17-77
140-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:83:25-74
141                <action android:name="android.intent.action.BATTERY_LOW" />
141-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:84:17-76
141-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:84:25-73
142            </intent-filter>
143        </receiver>
144        <receiver
144-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:87:9-97:20
145            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
145-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:88:13-104
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:89:13-44
147            android:enabled="false"
147-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:90:13-36
148            android:exported="false" >
148-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:91:13-37
149            <intent-filter>
149-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:93:13-96:29
150                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
150-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:94:17-83
150-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:94:25-80
151                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
151-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:95:17-82
151-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:95:25-79
152            </intent-filter>
153        </receiver>
154        <receiver
154-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:98:9-107:20
155            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
155-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:99:13-103
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:100:13-44
157            android:enabled="false"
157-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:101:13-36
158            android:exported="false" >
158-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:102:13-37
159            <intent-filter>
159-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:104:13-106:29
160                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
160-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:105:17-79
160-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:105:25-76
161            </intent-filter>
162        </receiver>
163        <receiver
163-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:108:9-119:20
164            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
164-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:109:13-88
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:110:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:111:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:112:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:114:13-118:29
169                <action android:name="android.intent.action.BOOT_COMPLETED" />
169-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:115:17-79
169-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:115:25-76
170                <action android:name="android.intent.action.TIME_SET" />
170-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:116:17-73
170-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:116:25-70
171                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
171-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:117:17-81
171-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:117:25-78
172            </intent-filter>
173        </receiver>
174        <receiver
174-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:120:9-129:20
175            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
175-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:121:13-99
176            android:directBootAware="false"
176-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:122:13-44
177            android:enabled="@bool/enable_system_alarm_service_default"
177-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:123:13-72
178            android:exported="false" >
178-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:124:13-37
179            <intent-filter>
179-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:126:13-128:29
180                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
180-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:127:17-98
180-->[androidx.work:work-runtime:2.3.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\3538ac697be21601595ef08aa4b1bb75\transformed\work-runtime-2.3.4\AndroidManifest.xml:127:25-95
181            </intent-filter>
182        </receiver>
183
184        <provider
184-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:9:9-17:20
185            android:name="androidx.startup.InitializationProvider"
185-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:10:13-67
186            android:authorities="com.example.sharen.androidx-startup"
186-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:11:13-68
187            android:exported="false" >
187-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:12:13-37
188            <meta-data
188-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:14:13-16:52
189                android:name="io.github.jan.supabase.gotrue.SupabaseInitializer"
189-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:15:17-81
190                android:value="androidx.startup" />
190-->[io.github.jan-tennert.supabase:gotrue-kt-android-debug:1.4.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\58a6044a93cab2165d6b854a98198488\transformed\gotrue-kt-debug\AndroidManifest.xml:16:17-49
191            <meta-data
191-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
192                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
192-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
193                android:value="androidx.startup" />
193-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e24d2977424e1b2bab98d3df9b808df3\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
194            <meta-data
194-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
195                android:name="androidx.emoji2.text.EmojiCompatInitializer"
195-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
196                android:value="androidx.startup" />
196-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7fbebb85020fe0d77b5505fcbd02294\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
197            <meta-data
197-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
198                android:name="com.russhwolf.settings.SettingsInitializer"
198-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
199                android:value="androidx.startup" />
199-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37da67dbcde55584b14621974a3f05f0\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
200            <meta-data
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
201                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
202                android:value="androidx.startup" />
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
203        </provider>
204
205        <uses-library
205-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
206            android:name="androidx.window.extensions"
206-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
207            android:required="false" />
207-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
208        <uses-library
208-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
209            android:name="androidx.window.sidecar"
209-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
210            android:required="false" />
210-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c44facd5da9f051e5aadaf82ac9a0238\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
211
212        <service
212-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
213            android:name="androidx.room.MultiInstanceInvalidationService"
213-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
214            android:directBootAware="true"
214-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
215            android:exported="false" />
215-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\124b49f2050a795a5088d483eb6c3eec\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
216
217        <receiver
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
218            android:name="androidx.profileinstaller.ProfileInstallReceiver"
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
219            android:directBootAware="false"
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
220            android:enabled="true"
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
221            android:exported="true"
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
222            android:permission="android.permission.DUMP" >
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
223            <intent-filter>
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
224                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
225            </intent-filter>
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
227                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
227-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
228            </intent-filter>
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
230                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
230-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
233                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
233-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e7ef3da0e533aa07c8824ea41bf5600\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
234            </intent-filter>
235        </receiver>
236    </application>
237
238</manifest>
