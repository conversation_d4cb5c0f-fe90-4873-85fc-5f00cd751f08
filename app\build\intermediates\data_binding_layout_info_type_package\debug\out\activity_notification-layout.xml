<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_notification" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_notification_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="138" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="27" endOffset="39"/></Target><Target id="@+id/btnClearAll" view="Button"><Expressions/><location startLine="19" startOffset="8" endLine="26" endOffset="46"/></Target><Target id="@+id/chipGroup" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="29" startOffset="4" endLine="69" endOffset="48"/></Target><Target id="@+id/chipAll" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="39" startOffset="8" endLine="44" endOffset="40"/></Target><Target id="@+id/chipSystem" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="46" startOffset="8" endLine="50" endOffset="43"/></Target><Target id="@+id/chipInvoice" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="52" startOffset="8" endLine="56" endOffset="45"/></Target><Target id="@+id/chipPayment" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="58" startOffset="8" endLine="62" endOffset="45"/></Target><Target id="@+id/chipInstallment" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="64" startOffset="8" endLine="68" endOffset="49"/></Target><Target id="@+id/statsLayout" view="LinearLayout"><Expressions/><location startLine="71" startOffset="4" endLine="101" endOffset="18"/></Target><Target id="@+id/tvUnreadCount" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="100" endOffset="28"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="103" startOffset="4" endLine="111" endOffset="52"/></Target><Target id="@+id/tvEmptyState" view="TextView"><Expressions/><location startLine="113" startOffset="4" endLine="125" endOffset="64"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="127" startOffset="4" endLine="136" endOffset="64"/></Target></Targets></Layout>