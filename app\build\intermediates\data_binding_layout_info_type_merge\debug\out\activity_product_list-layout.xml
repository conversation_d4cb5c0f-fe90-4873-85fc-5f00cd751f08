<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_list" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_product_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="144" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="93" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="19" endOffset="42"/></Target><Target id="@+id/tvProductCount" view="TextView"><Expressions/><location startLine="29" startOffset="12" endLine="35" endOffset="39"/></Target><Target id="@+id/searchView" view="androidx.appcompat.widget.SearchView"><Expressions/><location startLine="51" startOffset="20" endLine="57" endOffset="65"/></Target><Target id="@+id/btnFilter" view="ImageButton"><Expressions/><location startLine="61" startOffset="16" endLine="68" endOffset="74"/></Target><Target id="@+id/chipGroupFilters" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="72" startOffset="12" endLine="89" endOffset="56"/></Target><Target id="@+id/chipLowStock" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="80" startOffset="16" endLine="85" endOffset="50"/></Target><Target id="@+id/rvProducts" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="105" startOffset="12" endLine="112" endOffset="55"/></Target><Target id="@+id/tvNoProducts" view="TextView"><Expressions/><location startLine="114" startOffset="12" endLine="122" endOffset="43"/></Target><Target id="@+id/fabAddProduct" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="128" startOffset="4" endLine="135" endOffset="56"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="137" startOffset="4" endLine="142" endOffset="35"/></Target></Targets></Layout>