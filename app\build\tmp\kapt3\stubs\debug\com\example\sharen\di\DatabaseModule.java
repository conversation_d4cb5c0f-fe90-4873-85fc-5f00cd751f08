package com.example.sharen.di;

@dagger.Module
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\u0015\u001a\u00020\u00062\b\b\u0001\u0010\u0016\u001a\u00020\u0017H\u0007J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u00a8\u0006\u001a"}, d2 = {"Lcom/example/sharen/di/DatabaseModule;", "", "()V", "provideCategoryDao", "Lcom/example/sharen/data/local/dao/CategoryDao;", "database", "Lcom/example/sharen/data/local/SharenDatabase;", "provideCustomerDao", "Lcom/example/sharen/data/local/dao/CustomerDao;", "provideInstallmentDao", "Lcom/example/sharen/data/local/dao/InstallmentDao;", "provideInvoiceDao", "Lcom/example/sharen/data/local/dao/InvoiceDao;", "provideInvoiceItemDao", "Lcom/example/sharen/data/local/dao/InvoiceItemDao;", "providePaymentDao", "Lcom/example/sharen/data/local/dao/PaymentDao;", "provideProductDao", "Lcom/example/sharen/data/local/dao/ProductDao;", "provideSellerDao", "Lcom/example/sharen/data/local/dao/SellerDao;", "provideSharenDatabase", "context", "Landroid/content/Context;", "provideUserDao", "Lcom/example/sharen/data/local/dao/UserDao;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class DatabaseModule {
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.di.DatabaseModule INSTANCE = null;
    
    private DatabaseModule() {
        super();
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.SharenDatabase provideSharenDatabase(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.UserDao provideUserDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.CustomerDao provideCustomerDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.SellerDao provideSellerDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.ProductDao provideProductDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.CategoryDao provideCategoryDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.InvoiceDao provideInvoiceDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.InvoiceItemDao provideInvoiceItemDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.PaymentDao providePaymentDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
    
    @dagger.Provides
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.dao.InstallmentDao provideInstallmentDao(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.SharenDatabase database) {
        return null;
    }
}