package com.example.sharen.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/sharen/util/DateUtils;", "", "()V", "persianDateFormat", "Ljava/text/SimpleDateFormat;", "formatDate", "", "date", "Ljava/util/Date;", "formatDateTime", "app_debug"})
public final class DateUtils {
    @org.jetbrains.annotations.NotNull
    private static final java.text.SimpleDateFormat persianDateFormat = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.util.DateUtils INSTANCE = null;
    
    private DateUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatDate(@org.jetbrains.annotations.NotNull
    java.util.Date date) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatDateTime(@org.jetbrains.annotations.NotNull
    java.util.Date date) {
        return null;
    }
}