package com.example.sharen.data.model;

/**
 * وضعیت‌های مختلف فاکتور
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/example/sharen/data/model/InvoiceStatus;", "", "(Ljava/lang/String;I)V", "DRAFT", "PENDING", "APPROVED", "PAID", "PARTIALLY_PAID", "CANCELLED", "app_debug"})
public enum InvoiceStatus {
    /*public static final*/ DRAFT /* = new DRAFT() */,
    /*public static final*/ PENDING /* = new PENDING() */,
    /*public static final*/ APPROVED /* = new APPROVED() */,
    /*public static final*/ PAID /* = new PAID() */,
    /*public static final*/ PARTIALLY_PAID /* = new PARTIALLY_PAID() */,
    /*public static final*/ CANCELLED /* = new CANCELLED() */;
    
    InvoiceStatus() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.data.model.InvoiceStatus> getEntries() {
        return null;
    }
}