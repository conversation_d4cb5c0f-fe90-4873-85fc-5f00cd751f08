package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Transaction
import com.example.sharen.domain.model.TransactionType
import java.util.Date

@Entity(
    tableName = "transactions",
    foreignKeys = [
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = OrderEntity::class,
            parentColumns = ["id"],
            childColumns = ["orderId"],
            onDelete = ForeignKey.SET_NULL
        ),
        ForeignKey(
            entity = PaymentEntity::class,
            parentColumns = ["id"],
            childColumns = ["paymentId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index("customerId"),
        Index("orderId"),
        Index("paymentId")
    ]
)
data class TransactionEntity(
    @PrimaryKey
    val id: String,
    val customerId: String,
    val orderId: String?,
    val paymentId: String?,
    val amount: Long,
    val type: String,
    val description: String?,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toTransaction(): Transaction = Transaction(
        id = id,
        customerId = customerId,
        orderId = orderId,
        paymentId = paymentId,
        amount = amount,
        type = TransactionType.valueOf(type),
        description = description,
        createdAt = Date(createdAt),
        updatedAt = Date(updatedAt)
    )

    companion object {
        fun fromTransaction(transaction: Transaction): TransactionEntity = TransactionEntity(
            id = transaction.id,
            customerId = transaction.customerId,
            orderId = transaction.orderId,
            paymentId = transaction.paymentId,
            amount = transaction.amount,
            type = transaction.type.name,
            description = transaction.description,
            createdAt = transaction.createdAt.time,
            updatedAt = transaction.updatedAt.time
        )
    }
}