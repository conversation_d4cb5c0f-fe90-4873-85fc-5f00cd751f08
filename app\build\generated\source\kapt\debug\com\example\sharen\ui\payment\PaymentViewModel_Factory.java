package com.example.sharen.ui.payment;

import com.example.sharen.data.repository.InvoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PaymentViewModel_Factory implements Factory<PaymentViewModel> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  private final Provider<NonExistentClass> paymentRepositoryProvider;

  public PaymentViewModel_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<NonExistentClass> paymentRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public PaymentViewModel get() {
    return newInstance(invoiceRepositoryProvider.get(), paymentRepositoryProvider.get());
  }

  public static PaymentViewModel_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<NonExistentClass> paymentRepositoryProvider) {
    return new PaymentViewModel_Factory(invoiceRepositoryProvider, paymentRepositoryProvider);
  }

  public static PaymentViewModel newInstance(InvoiceRepository invoiceRepository,
      NonExistentClass paymentRepository) {
    return new PaymentViewModel(invoiceRepository, paymentRepository);
  }
}
