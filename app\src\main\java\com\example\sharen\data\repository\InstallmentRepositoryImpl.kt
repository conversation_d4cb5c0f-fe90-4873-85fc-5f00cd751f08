package com.example.sharen.data.repository

import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import com.example.sharen.data.remote.InstallmentRemoteDataSource
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InstallmentRepositoryImpl @Inject constructor(
    private val remoteDataSource: InstallmentRemoteDataSource
) : InstallmentRepository {
    
    override suspend fun createInstallment(installment: Installment): Result<Installment> {
        return remoteDataSource.createInstallment(installment)
    }
    
    override suspend fun updateInstallment(installment: Installment): Result<Installment> {
        return remoteDataSource.updateInstallment(installment)
    }
    
    override suspend fun deleteInstallment(installmentId: String): Result<Unit> {
        return remoteDataSource.deleteInstallment(installmentId)
    }
    
    override suspend fun getInstallment(installmentId: String): Result<Installment> {
        return remoteDataSource.getInstallment(installmentId)
    }
    
    override fun getAllInstallments(): Flow<List<Installment>> {
        return remoteDataSource.getAllInstallments()
    }
    
    override fun getInstallmentsByCustomer(customerId: String): Flow<List<Installment>> {
        return remoteDataSource.getInstallmentsByCustomer(customerId)
    }
    
    override fun getInstallmentsByDateRange(startDate: Date, endDate: Date): Flow<List<Installment>> {
        return remoteDataSource.getInstallmentsByDateRange(startDate, endDate)
    }
    
    override fun getInstallmentsByStatus(status: InstallmentStatus): Flow<List<Installment>> {
        return remoteDataSource.getInstallmentsByStatus(status)
    }
    
    override suspend fun payInstallment(installmentId: String, amount: Double): Result<Installment> {
        return remoteDataSource.payInstallment(installmentId, amount)
    }
    
    override suspend fun getUpcomingInstallments(customerId: String): Flow<List<Installment>> {
        return remoteDataSource.getUpcomingInstallments(customerId)
    }
    
    override suspend fun getOverdueInstallments(): Flow<List<Installment>> {
        return remoteDataSource.getOverdueInstallments()
    }
    
    override suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>> {
        return remoteDataSource.getInstallmentStatistics(startDate, endDate)
    }
    
    override suspend fun calculateRemainingAmount(installmentId: String): Result<Double> {
        return remoteDataSource.calculateRemainingAmount(installmentId)
    }
    
    override suspend fun getCustomerInstallmentHistory(customerId: String): Flow<List<Installment>> {
        return remoteDataSource.getCustomerInstallmentHistory(customerId)
    }
    
    override suspend fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        interestRate: Double
    ): Result<List<Installment>> {
        return remoteDataSource.generateInstallmentSchedule(
            totalAmount,
            numberOfInstallments,
            startDate,
            interestRate
        )
    }
} 