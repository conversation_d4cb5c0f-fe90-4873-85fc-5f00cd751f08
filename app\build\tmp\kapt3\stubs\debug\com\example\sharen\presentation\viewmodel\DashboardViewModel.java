package com.example.sharen.presentation.viewmodel;

/**
 * ViewModel برای داشبورد
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J\b\u0010\n\u001a\u00020\u000bH\u0002J\u0006\u0010\f\u001a\u00020\u000bR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006\r"}, d2 = {"Lcom/example/sharen/presentation/viewmodel/DashboardViewModel;", "Lcom/example/sharen/core/base/BaseViewModel;", "()V", "_dashboardStats", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/sharen/presentation/ui/dashboard/DashboardStats;", "dashboardStats", "Lkotlinx/coroutines/flow/StateFlow;", "getDashboardStats", "()Lkotlinx/coroutines/flow/StateFlow;", "loadDashboardData", "", "refreshDashboard", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class DashboardViewModel extends com.example.sharen.core.base.BaseViewModel {
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharen.presentation.ui.dashboard.DashboardStats> _dashboardStats = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.example.sharen.presentation.ui.dashboard.DashboardStats> dashboardStats = null;
    
    @javax.inject.Inject
    public DashboardViewModel() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.example.sharen.presentation.ui.dashboard.DashboardStats> getDashboardStats() {
        return null;
    }
    
    /**
     * بارگذاری داده‌های داشبورد
     */
    private final void loadDashboardData() {
    }
    
    /**
     * بروزرسانی داده‌های داشبورد
     */
    public final void refreshDashboard() {
    }
}