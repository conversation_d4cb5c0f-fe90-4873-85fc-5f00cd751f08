package com.example.sharen.di;

import com.example.sharen.data.local.SharenDatabase;
import com.example.sharen.data.local.dao.SellerDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideSellerDaoFactory implements Factory<SellerDao> {
  private final Provider<SharenDatabase> databaseProvider;

  public DatabaseModule_ProvideSellerDaoFactory(Provider<SharenDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public SellerDao get() {
    return provideSellerDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideSellerDaoFactory create(
      Provider<SharenDatabase> databaseProvider) {
    return new DatabaseModule_ProvideSellerDaoFactory(databaseProvider);
  }

  public static SellerDao provideSellerDao(SharenDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideSellerDao(database));
  }
}
