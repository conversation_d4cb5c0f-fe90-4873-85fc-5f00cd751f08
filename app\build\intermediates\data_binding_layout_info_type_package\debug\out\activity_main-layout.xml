<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="21" endOffset="59"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="36" endOffset="60"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="39" startOffset="4" endLine="47" endOffset="64"/></Target><Target id="@+id/tv_version" view="TextView"><Expressions/><location startLine="50" startOffset="4" endLine="61" endOffset="55"/></Target></Targets></Layout>