package com.example.sharen.domain.usecase.analytics;

/**
 * Use Case برای دریافت آمار داشبورد
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u000f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0086\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/example/sharen/domain/usecase/analytics/GetDashboardStatsUseCase;", "", "customerRepository", "Lcom/example/sharen/domain/repository/CustomerRepository;", "productRepository", "Lcom/example/sharen/domain/repository/ProductRepository;", "invoiceRepository", "Lcom/example/sharen/domain/repository/InvoiceRepository;", "paymentRepository", "Lcom/example/sharen/domain/repository/PaymentRepository;", "(Lcom/example/sharen/domain/repository/CustomerRepository;Lcom/example/sharen/domain/repository/ProductRepository;Lcom/example/sharen/domain/repository/InvoiceRepository;Lcom/example/sharen/domain/repository/PaymentRepository;)V", "invoke", "Lkotlinx/coroutines/flow/Flow;", "error/NonExistentClass", "app_debug"})
public final class GetDashboardStatsUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.CustomerRepository customerRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.ProductRepository productRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.InvoiceRepository invoiceRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.PaymentRepository paymentRepository = null;
    
    @javax.inject.Inject
    public GetDashboardStatsUseCase(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.CustomerRepository customerRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.ProductRepository productRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.InvoiceRepository invoiceRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.PaymentRepository paymentRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<error.NonExistentClass> invoke() {
        return null;
    }
}