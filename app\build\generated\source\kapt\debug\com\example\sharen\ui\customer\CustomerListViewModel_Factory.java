package com.example.sharen.ui.customer;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerListViewModel_Factory implements Factory<CustomerListViewModel> {
  private final Provider<NonExistentClass> customerRepositoryProvider;

  public CustomerListViewModel_Factory(Provider<NonExistentClass> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public CustomerListViewModel get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static CustomerListViewModel_Factory create(
      Provider<NonExistentClass> customerRepositoryProvider) {
    return new CustomerListViewModel_Factory(customerRepositoryProvider);
  }

  public static CustomerListViewModel newInstance(NonExistentClass customerRepository) {
    return new CustomerListViewModel(customerRepository);
  }
}
