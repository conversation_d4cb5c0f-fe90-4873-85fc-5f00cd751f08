package com.example.sharen.ui.profile;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0012\u001a\u00020\u0013H\u0002J\b\u0010\u0014\u001a\u00020\u0013H\u0002J\b\u0010\u0015\u001a\u00020\u0013H\u0002J\b\u0010\u0016\u001a\u00020\u0013H\u0002J\u0012\u0010\u0017\u001a\u00020\u00132\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u0014J\b\u0010\u001a\u001a\u00020\u001bH\u0016J\b\u0010\u001c\u001a\u00020\u0013H\u0002J\b\u0010\u001d\u001a\u00020\u0013H\u0002J\b\u0010\u001e\u001a\u00020\u0013H\u0002J\b\u0010\u001f\u001a\u00020\u0013H\u0002J\b\u0010 \u001a\u00020\u0013H\u0002J\b\u0010!\u001a\u00020\u0013H\u0002J\b\u0010\"\u001a\u00020\u0013H\u0002J\u0010\u0010#\u001a\u00020\u00132\u0006\u0010$\u001a\u00020%H\u0002R\u0010\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0004\n\u0002\u0010\u0005R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u001c\u0010\b\u001a\u0010\u0012\f\u0012\n \u000b*\u0004\u0018\u00010\n0\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\f\u001a\u00020\r8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0010\u0010\u0011\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006&"}, d2 = {"Lcom/example/sharen/ui/profile/ProfileActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "error/NonExistentClass", "Lerror/NonExistentClass;", "dateFormatter", "Ljava/text/SimpleDateFormat;", "pickImageLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "kotlin.jvm.PlatformType", "viewModel", "Lcom/example/sharen/ui/profile/ProfileViewModel;", "getViewModel", "()Lcom/example/sharen/ui/profile/ProfileViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "logout", "", "navigateToChangePassword", "navigateToDisplaySettings", "navigateToNotificationSettings", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onSupportNavigateUp", "", "pickProfileImage", "setupObservers", "setupToolbar", "setupUI", "showEditNameDialog", "showEditPhoneDialog", "showLogoutConfirmation", "updateProfilePicture", "uri", "Landroid/net/Uri;", "app_debug"})
public final class ProfileActivity extends androidx.appcompat.app.AppCompatActivity {
    private error.NonExistentClass binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private java.text.SimpleDateFormat dateFormatter;
    @org.jetbrains.annotations.NotNull
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> pickImageLauncher = null;
    
    public ProfileActivity() {
        super();
    }
    
    private final com.example.sharen.ui.profile.ProfileViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupUI() {
    }
    
    private final void setupObservers() {
    }
    
    private final void pickProfileImage() {
    }
    
    private final void updateProfilePicture(android.net.Uri uri) {
    }
    
    private final void showEditNameDialog() {
    }
    
    private final void showEditPhoneDialog() {
    }
    
    private final void navigateToChangePassword() {
    }
    
    private final void navigateToNotificationSettings() {
    }
    
    private final void navigateToDisplaySettings() {
    }
    
    private final void showLogoutConfirmation() {
    }
    
    private final void logout() {
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
}