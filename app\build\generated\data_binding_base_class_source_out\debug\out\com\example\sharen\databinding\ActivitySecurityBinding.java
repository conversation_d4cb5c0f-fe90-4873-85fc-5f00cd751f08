// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySecurityBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnChangePin;

  @NonNull
  public final SwitchMaterial switchBiometric;

  @NonNull
  public final SwitchMaterial switchPin;

  @NonNull
  public final Toolbar toolbar;

  private ActivitySecurityBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnChangePin, @NonNull SwitchMaterial switchBiometric,
      @NonNull SwitchMaterial switchPin, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnChangePin = btnChangePin;
    this.switchBiometric = switchBiometric;
    this.switchPin = switchPin;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySecurityBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySecurityBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_security, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySecurityBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnChangePin;
      MaterialButton btnChangePin = ViewBindings.findChildViewById(rootView, id);
      if (btnChangePin == null) {
        break missingId;
      }

      id = R.id.switchBiometric;
      SwitchMaterial switchBiometric = ViewBindings.findChildViewById(rootView, id);
      if (switchBiometric == null) {
        break missingId;
      }

      id = R.id.switchPin;
      SwitchMaterial switchPin = ViewBindings.findChildViewById(rootView, id);
      if (switchPin == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivitySecurityBinding((CoordinatorLayout) rootView, btnChangePin,
          switchBiometric, switchPin, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
