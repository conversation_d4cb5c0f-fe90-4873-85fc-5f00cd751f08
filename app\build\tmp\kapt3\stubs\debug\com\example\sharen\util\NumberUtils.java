package com.example.sharen.util;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tJ\u000e\u0010\n\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\tR\u0016\u0010\u0003\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/example/sharen/util/NumberUtils;", "", "()V", "numberFormat", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "formatCurrency", "", "amount", "", "formatNumber", "number", "app_debug"})
public final class NumberUtils {
    private static final java.text.NumberFormat numberFormat = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.util.NumberUtils INSTANCE = null;
    
    private NumberUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatCurrency(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatNumber(double number) {
        return null;
    }
}