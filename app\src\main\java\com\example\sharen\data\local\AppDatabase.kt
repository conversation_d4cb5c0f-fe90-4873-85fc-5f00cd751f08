package com.example.sharen.data.local

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.sharen.data.local.dao.*
import com.example.sharen.data.local.entity.*
import com.example.sharen.data.db.converter.DateConverter
import com.example.sharen.data.db.converter.InstallmentStatusConverter

@Database(
    entities = [
        UserEntity::class,
        CustomerEntity::class,
        ProductEntity::class,
        CategoryEntity::class,
        InvoiceEntity::class,
        InvoiceItemEntity::class,
        PaymentEntity::class,
        SellerEntity::class,
        InstallmentEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(DateConverter::class, InstallmentStatusConverter::class)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun customerDao(): CustomerDao
    abstract fun productDao(): ProductDao
    abstract fun categoryDao(): CategoryDao
    abstract fun invoiceDao(): InvoiceDao
    abstract fun invoiceItemDao(): InvoiceItemDao
    abstract fun paymentDao(): PaymentDao
    abstract fun sellerDao(): SellerDao
    abstract fun installmentDao(): InstallmentDao
} 