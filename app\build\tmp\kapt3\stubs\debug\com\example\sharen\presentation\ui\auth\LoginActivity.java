package com.example.sharen.presentation.ui.auth;

/**
 * صفحه ورود کاربر
 */
@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\r\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\n\u001a\u00020\u0002H\u0016J\b\u0010\u000b\u001a\u00020\fH\u0014J\b\u0010\r\u001a\u00020\fH\u0002J\b\u0010\u000e\u001a\u00020\fH\u0002J\b\u0010\u000f\u001a\u00020\fH\u0002J\b\u0010\u0010\u001a\u00020\fH\u0016J\b\u0010\u0011\u001a\u00020\fH\u0002J\b\u0010\u0012\u001a\u00020\fH\u0002J\b\u0010\u0013\u001a\u00020\fH\u0002J\b\u0010\u0014\u001a\u00020\fH\u0002J\b\u0010\u0015\u001a\u00020\fH\u0002J\b\u0010\u0016\u001a\u00020\fH\u0002J\b\u0010\u0017\u001a\u00020\fH\u0016J\b\u0010\u0018\u001a\u00020\fH\u0014R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0019"}, d2 = {"Lcom/example/sharen/presentation/ui/auth/LoginActivity;", "Lcom/example/sharen/core/base/BaseActivity;", "Lcom/example/sharen/databinding/ActivityLoginBinding;", "()V", "authViewModel", "Lcom/example/sharen/presentation/viewmodel/AuthViewModel;", "getAuthViewModel", "()Lcom/example/sharen/presentation/viewmodel/AuthViewModel;", "authViewModel$delegate", "Lkotlin/Lazy;", "getViewBinding", "hideLoading", "", "navigateToDashboard", "navigateToForgotPassword", "navigateToRegister", "observeData", "observeLoadingState", "observeLoginState", "observeMessages", "performLogin", "setupClickListeners", "setupUI", "setupViews", "showLoading", "app_debug"})
public final class LoginActivity extends com.example.sharen.core.base.BaseActivity<com.example.sharen.databinding.ActivityLoginBinding> {
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy authViewModel$delegate = null;
    
    public LoginActivity() {
        super();
    }
    
    private final com.example.sharen.presentation.viewmodel.AuthViewModel getAuthViewModel() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.databinding.ActivityLoginBinding getViewBinding() {
        return null;
    }
    
    @java.lang.Override
    public void setupViews() {
    }
    
    @java.lang.Override
    public void observeData() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void setupUI() {
    }
    
    private final void observeLoginState() {
    }
    
    private final void observeLoadingState() {
    }
    
    private final void observeMessages() {
    }
    
    private final void performLogin() {
    }
    
    private final void navigateToRegister() {
    }
    
    private final void navigateToForgotPassword() {
    }
    
    private final void navigateToDashboard() {
    }
    
    @java.lang.Override
    protected void showLoading() {
    }
    
    @java.lang.Override
    protected void hideLoading() {
    }
}