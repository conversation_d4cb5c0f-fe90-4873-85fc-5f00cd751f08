package com.example.sharen.ui.customers;

import androidx.annotation.NonNull;
import androidx.navigation.ActionOnlyNavDirections;
import androidx.navigation.NavDirections;
import com.example.sharen.R;

public class CustomersFragmentDirections {
  private CustomersFragmentDirections() {
  }

  @NonNull
  public static NavDirections actionCustomersToAddCustomer() {
    return new ActionOnlyNavDirections(R.id.action_customers_to_addCustomer);
  }

  @NonNull
  public static NavDirections actionCustomersToCustomerDetails() {
    return new ActionOnlyNavDirections(R.id.action_customers_to_customerDetails);
  }
}
