package com.example.sharen.domain.usecase.invoice

import com.example.sharen.domain.model.Invoice
import com.example.sharen.domain.model.InvoiceItem
import com.example.sharen.domain.model.InvoiceStatus
import com.example.sharen.domain.model.PaymentType
import com.example.sharen.domain.repository.InvoiceRepository
import com.example.sharen.domain.repository.ProductRepository
import com.example.sharen.domain.repository.CustomerRepository
import java.util.Date
import java.util.UUID
import javax.inject.Inject

/**
 * Use Case برای ایجاد فاکتور جدید
 */
class CreateInvoiceUseCase @Inject constructor(
    private val invoiceRepository: InvoiceRepository,
    private val productRepository: ProductRepository,
    private val customerRepository: CustomerRepository
) {
    suspend operator fun invoke(
        customerId: String,
        sellerId: String,
        items: List<InvoiceItem>,
        discount: Long = 0,
        tax: Long = 0,
        paymentType: PaymentType = PaymentType.CASH,
        notes: String? = null
    ): Result<Invoice> {

        // اعتبارسنجی ورودی‌ها
        if (customerId.isBlank()) {
            return Result.failure(IllegalArgumentException("شناسه مشتری نمی‌تواند خالی باشد"))
        }

        if (sellerId.isBlank()) {
            return Result.failure(IllegalArgumentException("شناسه فروشنده نمی‌تواند خالی باشد"))
        }

        if (items.isEmpty()) {
            return Result.failure(IllegalArgumentException("فاکتور باید حداقل یک آیتم داشته باشد"))
        }

        if (discount < 0) {
            return Result.failure(IllegalArgumentException("تخفیف نمی‌تواند منفی باشد"))
        }

        if (tax < 0) {
            return Result.failure(IllegalArgumentException("مالیات نمی‌تواند منفی باشد"))
        }

        // بررسی وجود مشتری
        val customerResult = customerRepository.getCustomerById(customerId)
        if (customerResult.isFailure || customerResult.getOrNull() == null) {
            return Result.failure(IllegalArgumentException("مشتری یافت نشد"))
        }

        // بررسی موجودی محصولات
        for (item in items) {
            val productResult = productRepository.getProductById(item.productId)
            if (productResult.isFailure || productResult.getOrNull() == null) {
                return Result.failure(IllegalArgumentException("محصول ${item.productId} یافت نشد"))
            }

            val product = productResult.getOrNull()!!
            if (product.stock < item.quantity) {
                return Result.failure(IllegalArgumentException("موجودی محصول ${product.name} کافی نیست"))
            }
        }

        // محاسبه مبالغ
        val subtotal = items.sumOf { it.totalPrice }
        val finalAmount = subtotal - discount + tax

        if (finalAmount < 0) {
            return Result.failure(IllegalArgumentException("مبلغ نهایی نمی‌تواند منفی باشد"))
        }

        // دریافت نام مشتری
        val customerResult = customerRepository.getCustomerById(customerId)
        val customerName = customerResult.getOrNull()?.name ?: "نامشخص"

        // ایجاد فاکتور
        val invoice = Invoice(
            id = UUID.randomUUID().toString(),
            invoiceNumber = generateInvoiceNumber(),
            customerId = customerId,
            customerName = customerName,
            sellerId = sellerId,
            items = items,
            totalAmount = subtotal,
            discountAmount = discount,
            taxAmount = tax,
            finalAmount = finalAmount,
            remainingAmount = finalAmount, // در ابتدا کل مبلغ باقیمانده است
            status = InvoiceStatus.PENDING,
            paymentType = paymentMethod,
            notes = notes?.trim(),
            createdAt = Date(),
            updatedAt = Date()
        )

        // ذخیره فاکتور
        val saveResult = invoiceRepository.addInvoice(invoice)
        if (saveResult.isFailure) {
            return saveResult
        }

        // کاهش موجودی محصولات
        for (item in items) {
            productRepository.decreaseStock(item.productId, item.quantity)
        }

        // بروزرسانی آمار مشتری
        customerRepository.recordPurchase(customerId, finalAmount, Date())

        return Result.success(invoice)
    }

    private fun generateInvoiceNumber(): String {
        val timestamp = System.currentTimeMillis()
        return "INV-${timestamp}"
    }
}
