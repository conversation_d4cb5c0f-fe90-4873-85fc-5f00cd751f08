package com.example.sharen.data.repository;

/**
 * رپوزیتوری مدیریت محصولات
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u000b\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J*\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000fH&J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000fH&J\u0016\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u000f2\u0006\u0010\n\u001a\u00020\u000bH&J\u000e\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u000fH&J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010\u0016\u001a\u00020\u000bH&J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010\u0018\u001a\u00020\u000bH&J*\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001a\u0010\u0007J2\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u0014H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\u001e\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u001f"}, d2 = {"Lcom/example/sharen/data/repository/ProductRepository;", "", "createProduct", "Lkotlin/Result;", "Lcom/example/sharen/data/model/Product;", "product", "createProduct-gIAlu-s", "(Lcom/example/sharen/data/model/Product;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProduct", "", "productId", "", "deleteProduct-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllProducts", "Lkotlinx/coroutines/flow/Flow;", "", "getLowStockProducts", "getProductById", "getProductCount", "", "getProductsByCategory", "category", "searchProducts", "query", "updateProduct", "updateProduct-gIAlu-s", "updateProductStock", "newStock", "updateProductStock-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface ProductRepository {
    
    /**
     * دریافت لیست همه محصولات
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> getAllProducts();
    
    /**
     * دریافت محصول با شناسه مشخص
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Product> getProductById(@org.jetbrains.annotations.NotNull
    java.lang.String productId);
    
    /**
     * جستجوی محصولات با کلمه کلیدی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> searchProducts(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * دریافت تعداد کل محصولات
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Integer> getProductCount();
    
    /**
     * دریافت لیست محصولات با موجودی کم
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> getLowStockProducts();
    
    /**
     * دریافت محصولات یک دسته‌بندی خاص
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> getProductsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category);
}