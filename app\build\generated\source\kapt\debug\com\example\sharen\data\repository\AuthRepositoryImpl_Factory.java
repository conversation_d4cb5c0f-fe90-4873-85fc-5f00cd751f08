package com.example.sharen.data.repository;

import com.example.sharen.data.local.dao.UserDao;
import com.example.sharen.data.remote.AuthRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthRepositoryImpl_Factory implements Factory<AuthRepositoryImpl> {
  private final Provider<UserDao> userDaoProvider;

  private final Provider<AuthRemoteDataSource> remoteDataSourceProvider;

  public AuthRepositoryImpl_Factory(Provider<UserDao> userDaoProvider,
      Provider<AuthRemoteDataSource> remoteDataSourceProvider) {
    this.userDaoProvider = userDaoProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public AuthRepositoryImpl get() {
    return newInstance(userDaoProvider.get(), remoteDataSourceProvider.get());
  }

  public static AuthRepositoryImpl_Factory create(Provider<UserDao> userDaoProvider,
      Provider<AuthRemoteDataSource> remoteDataSourceProvider) {
    return new AuthRepositoryImpl_Factory(userDaoProvider, remoteDataSourceProvider);
  }

  public static AuthRepositoryImpl newInstance(UserDao userDao,
      AuthRemoteDataSource remoteDataSource) {
    return new AuthRepositoryImpl(userDao, remoteDataSource);
  }
}
