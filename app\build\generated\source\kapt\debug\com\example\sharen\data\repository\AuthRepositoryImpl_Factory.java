package com.example.sharen.data.repository;

import android.content.SharedPreferences;
import com.example.sharen.data.local.dao.UserDao;
import com.example.sharen.data.remote.AuthRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import io.github.jan.supabase.SupabaseClient;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthRepositoryImpl_Factory implements Factory<AuthRepositoryImpl> {
  private final Provider<SupabaseClient> supabaseClientProvider;

  private final Provider<UserDao> userDaoProvider;

  private final Provider<SharedPreferences> sharedPreferencesProvider;

  private final Provider<AuthRemoteDataSource> remoteDataSourceProvider;

  public AuthRepositoryImpl_Factory(Provider<SupabaseClient> supabaseClientProvider,
      Provider<UserDao> userDaoProvider, Provider<SharedPreferences> sharedPreferencesProvider,
      Provider<AuthRemoteDataSource> remoteDataSourceProvider) {
    this.supabaseClientProvider = supabaseClientProvider;
    this.userDaoProvider = userDaoProvider;
    this.sharedPreferencesProvider = sharedPreferencesProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public AuthRepositoryImpl get() {
    return newInstance(supabaseClientProvider.get(), userDaoProvider.get(), sharedPreferencesProvider.get(), remoteDataSourceProvider.get());
  }

  public static AuthRepositoryImpl_Factory create(Provider<SupabaseClient> supabaseClientProvider,
      Provider<UserDao> userDaoProvider, Provider<SharedPreferences> sharedPreferencesProvider,
      Provider<AuthRemoteDataSource> remoteDataSourceProvider) {
    return new AuthRepositoryImpl_Factory(supabaseClientProvider, userDaoProvider, sharedPreferencesProvider, remoteDataSourceProvider);
  }

  public static AuthRepositoryImpl newInstance(SupabaseClient supabaseClient, UserDao userDao,
      SharedPreferences sharedPreferences, AuthRemoteDataSource remoteDataSource) {
    return new AuthRepositoryImpl(supabaseClient, userDao, sharedPreferences, remoteDataSource);
  }
}
