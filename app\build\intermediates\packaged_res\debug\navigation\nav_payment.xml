<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/nav_payment"
    app:startDestination="@id/paymentListFragment">

    <fragment
        android:id="@+id/paymentListFragment"
        android:name="com.example.sharen.ui.payment.PaymentListFragment"
        android:label="@string/payments">
        <action
            android:id="@+id/action_paymentListFragment_to_paymentAddEditFragment"
            app:destination="@id/paymentAddEditFragment" />
        <action
            android:id="@+id/action_paymentListFragment_to_paymentDetailFragment"
            app:destination="@id/paymentDetailFragment" />
    </fragment>

    <fragment
        android:id="@+id/paymentAddEditFragment"
        android:name="com.example.sharen.ui.payment.PaymentAddEditFragment"
        android:label="@string/add_edit_payment">
        <argument
            android:name="paymentId"
            android:defaultValue="@null"
            app:argType="string"
            app:nullable="true" />
        <argument
            android:name="customerId"
            app:argType="string" />
        <argument
            android:name="orderId"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/paymentDetailFragment"
        android:name="com.example.sharen.ui.payment.PaymentDetailFragment"
        android:label="@string/payment_details">
        <argument
            android:name="paymentId"
            app:argType="string" />
        <action
            android:id="@+id/action_paymentDetailFragment_to_paymentAddEditFragment"
            app:destination="@id/paymentAddEditFragment" />
    </fragment>

</navigation> 