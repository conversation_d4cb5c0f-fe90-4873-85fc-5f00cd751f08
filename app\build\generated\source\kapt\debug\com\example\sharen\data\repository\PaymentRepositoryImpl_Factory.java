package com.example.sharen.data.repository;

import com.example.sharen.data.local.dao.PaymentDao;
import com.example.sharen.data.remote.PaymentRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PaymentRepositoryImpl_Factory implements Factory<PaymentRepositoryImpl> {
  private final Provider<PaymentDao> paymentDaoProvider;

  private final Provider<PaymentRemoteDataSource> remoteDataSourceProvider;

  public PaymentRepositoryImpl_Factory(Provider<PaymentDao> paymentDaoProvider,
      Provider<PaymentRemoteDataSource> remoteDataSourceProvider) {
    this.paymentDaoProvider = paymentDaoProvider;
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public PaymentRepositoryImpl get() {
    return newInstance(paymentDaoProvider.get(), remoteDataSourceProvider.get());
  }

  public static PaymentRepositoryImpl_Factory create(Provider<PaymentDao> paymentDaoProvider,
      Provider<PaymentRemoteDataSource> remoteDataSourceProvider) {
    return new PaymentRepositoryImpl_Factory(paymentDaoProvider, remoteDataSourceProvider);
  }

  public static PaymentRepositoryImpl newInstance(PaymentDao paymentDao,
      PaymentRemoteDataSource remoteDataSource) {
    return new PaymentRepositoryImpl(paymentDao, remoteDataSource);
  }
}
