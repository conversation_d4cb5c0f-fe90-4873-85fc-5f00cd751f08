package com.example.sharen.data.model

import java.util.Date
import java.util.UUID

/**
 * مدل داده پرداخت
 */
data class Payment(
    val id: String = UUID.randomUUID().toString(),
    val customerId: String,
    val orderId: String? = null,
    val amount: Double,
    val date: Date,
    val status: PaymentStatus = PaymentStatus.PENDING,
    val method: PaymentMethod,
    val referenceNumber: String? = null,
    val notes: String? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    val createdBy: String? = null,
    val updatedBy: String? = null
)

/**
 * روش‌های پرداخت
 */
enum class PaymentMethod {
    CASH,             // نقدی
    CREDIT_CARD,      // کارت بانکی
    BANK_TRANSFER,    // انتقال بانکی
    CHECK,            // چک
    INSTALLMENT       // اقساط
}

/**
 * وضعیت‌های پرداخت
 */
enum class PaymentStatus {
    PENDING,          // در انتظار تأیید
    CONFIRMED,        // تأیید شده
    REJECTED,         // رد شده
    REFUNDED          // بازپرداخت شده
} 