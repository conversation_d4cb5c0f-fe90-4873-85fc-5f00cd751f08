package com.example.sharen.data.remote;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReportRemoteDataSource_Factory implements Factory<ReportRemoteDataSource> {
  private final Provider<SupabaseApiService> apiServiceProvider;

  public ReportRemoteDataSource_Factory(Provider<SupabaseApiService> apiServiceProvider) {
    this.apiServiceProvider = apiServiceProvider;
  }

  @Override
  public ReportRemoteDataSource get() {
    return newInstance(apiServiceProvider.get());
  }

  public static ReportRemoteDataSource_Factory create(
      Provider<SupabaseApiService> apiServiceProvider) {
    return new ReportRemoteDataSource_Factory(apiServiceProvider);
  }

  public static ReportRemoteDataSource newInstance(SupabaseApiService apiService) {
    return new ReportRemoteDataSource(apiService);
  }
}
