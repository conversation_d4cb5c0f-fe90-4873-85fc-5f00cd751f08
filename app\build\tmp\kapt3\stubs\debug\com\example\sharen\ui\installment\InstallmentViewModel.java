package com.example.sharen.ui.installment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007J\u0006\u0010\u0018\u001a\u00020\u0016J\u000e\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\nJ\u000e\u0010\u001b\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u001dJ&\u0010\u001e\u001a\u00020\u00162\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020 J\u000e\u0010&\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u0007J\u000e\u0010(\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u001dJ\u0016\u0010)\u001a\u00020\u00162\u0006\u0010#\u001a\u00020$2\u0006\u0010*\u001a\u00020$J\u000e\u0010+\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u0007J\u0016\u0010,\u001a\u00020\u00162\u0006\u0010#\u001a\u00020$2\u0006\u0010*\u001a\u00020$J\u000e\u0010-\u001a\u00020\u00162\u0006\u0010.\u001a\u00020/J\u0006\u00100\u001a\u00020\u0016J\u000e\u00101\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u0007J\u0006\u00102\u001a\u00020\u0016J\u0016\u00103\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u00104\u001a\u00020 J\u000e\u00105\u001a\u00020\u00162\u0006\u0010\u001c\u001a\u00020\u001dJ\u000e\u00106\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\nR\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001d\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00067"}, d2 = {"Lcom/example/sharen/ui/installment/InstallmentViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/example/sharen/data/repository/InstallmentRepository;", "(Lcom/example/sharen/data/repository/InstallmentRepository;)V", "_error", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_installments", "", "Lcom/example/sharen/data/model/Installment;", "_loading", "", "error", "Lkotlinx/coroutines/flow/StateFlow;", "getError", "()Lkotlinx/coroutines/flow/StateFlow;", "installments", "getInstallments", "loading", "getLoading", "calculateRemainingAmount", "", "installmentId", "clearError", "createInstallment", "installment", "deleteInstallment", "id", "", "generateInstallmentSchedule", "totalAmount", "", "numberOfInstallments", "", "startDate", "Ljava/util/Date;", "interestRate", "getCustomerInstallmentHistory", "customerId", "getInstallment", "getInstallmentStatistics", "endDate", "getInstallmentsByCustomer", "getInstallmentsByDateRange", "getInstallmentsByStatus", "status", "Lcom/example/sharen/data/model/InstallmentStatus;", "getOverdueInstallments", "getUpcomingInstallments", "loadInstallments", "payInstallment", "amount", "sendReminder", "updateInstallment", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class InstallmentViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.repository.InstallmentRepository repository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharen.data.model.Installment>> _installments = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.data.model.Installment>> installments = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _loading = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> loading = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> error = null;
    
    @javax.inject.Inject
    public InstallmentViewModel(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.repository.InstallmentRepository repository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.data.model.Installment>> getInstallments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getError() {
        return null;
    }
    
    public final void loadInstallments() {
    }
    
    public final void getInstallment(long id) {
    }
    
    public final void createInstallment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment) {
    }
    
    public final void updateInstallment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment) {
    }
    
    public final void deleteInstallment(long id) {
    }
    
    public final void payInstallment(long id, double amount) {
    }
    
    public final void sendReminder(long id) {
    }
    
    public final void getInstallmentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
    }
    
    public final void getInstallmentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void getInstallmentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.InstallmentStatus status) {
    }
    
    public final void getUpcomingInstallments(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
    }
    
    public final void getOverdueInstallments() {
    }
    
    public final void getInstallmentStatistics(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void calculateRemainingAmount(@org.jetbrains.annotations.NotNull
    java.lang.String installmentId) {
    }
    
    public final void getCustomerInstallmentHistory(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
    }
    
    public final void generateInstallmentSchedule(double totalAmount, int numberOfInstallments, @org.jetbrains.annotations.NotNull
    java.util.Date startDate, double interestRate) {
    }
    
    public final void clearError() {
    }
}