package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.data.model.Product
import java.util.Date

@Entity(
    tableName = "products",
    foreignKeys = [
        ForeignKey(
            entity = CategoryEntity::class,
            parentColumns = ["id"],
            childColumns = ["categoryId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [Index("categoryId")]
)
data class ProductEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val code: String?,
    val barcode: String?,
    val description: String?,
    val category: String?,
    val categoryId: String?,
    val purchasePrice: Long,
    val sellingPrice: Long,
    val stock: Int,
    val minimumStock: Int,
    val imageUrl: String?,
    val isActive: Boolean,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toProduct(): Product = Product(
        id = id,
        name = name,
        code = code,
        barcode = barcode,
        description = description,
        category = category,
        purchasePrice = purchasePrice,
        sellingPrice = sellingPrice,
        stock = stock,
        minimumStock = minimumStock,
        imageUrl = imageUrl,
        isActive = isActive,
        createdAt = Date(createdAt),
        lastUpdated = Date(updatedAt)
    )
    
    companion object {
        fun fromProduct(product: Product): ProductEntity = ProductEntity(
            id = product.id,
            name = product.name,
            code = product.code,
            barcode = product.barcode,
            description = product.description,
            category = product.category,
            categoryId = null, // TODO: Implement category ID mapping
            purchasePrice = product.purchasePrice,
            sellingPrice = product.sellingPrice,
            stock = product.stock,
            minimumStock = product.minimumStock,
            imageUrl = product.imageUrl,
            isActive = product.isActive,
            createdAt = product.createdAt.time,
            updatedAt = product.lastUpdated.time
        )
    }
} 