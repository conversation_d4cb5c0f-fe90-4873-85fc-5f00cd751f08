package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Product
import com.example.sharen.domain.model.ProductType
import com.example.sharen.domain.model.Season
import com.example.sharen.domain.model.Gender
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.Date

@Entity(
    tableName = "products",
    foreignKeys = [
        ForeignKey(
            entity = CategoryEntity::class,
            parentColumns = ["id"],
            childColumns = ["categoryId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [Index("categoryId")]
)
data class ProductEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val code: String,
    val barcode: String? = null,
    val description: String? = null,
    val categoryId: String,
    val brandId: String? = null,
    val type: String = ProductType.CLOTHING.name,
    val season: String = Season.ALL_SEASON.name,
    val gender: String = Gender.UNISEX.name,
    val sizes: String = "[]", // JSON array
    val colors: String = "[]", // JSON array
    val materials: String = "[]", // JSON array
    val purchasePrice: Long,
    val sellingPrice: Long,
    val stock: Int,
    val minimumStock: Int = 0,
    val imageUrls: String = "[]", // JSON array
    val tags: String = "[]", // JSON array
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    fun toDomainModel(): Product {
        val gson = Gson()
        val stringListType = object : TypeToken<List<String>>() {}.type

        return Product(
            id = id,
            name = name,
            code = code,
            barcode = barcode,
            description = description,
            categoryId = categoryId,
            brandId = brandId,
            type = ProductType.valueOf(type),
            season = Season.valueOf(season),
            gender = Gender.valueOf(gender),
            sizes = gson.fromJson(sizes, stringListType) ?: emptyList(),
            colors = gson.fromJson(colors, stringListType) ?: emptyList(),
            materials = gson.fromJson(materials, stringListType) ?: emptyList(),
            purchasePrice = purchasePrice,
            sellingPrice = sellingPrice,
            stock = stock,
            minimumStock = minimumStock,
            imageUrls = gson.fromJson(imageUrls, stringListType) ?: emptyList(),
            tags = gson.fromJson(tags, stringListType) ?: emptyList(),
            isActive = isActive,
            createdAt = Date(createdAt),
            updatedAt = Date(updatedAt)
        )
    }

    companion object {
        fun fromDomainModel(product: Product): ProductEntity {
            val gson = Gson()

            return ProductEntity(
                id = product.id,
                name = product.name,
                code = product.code,
                barcode = product.barcode,
                description = product.description,
                categoryId = product.categoryId,
                brandId = product.brandId,
                type = product.type.name,
                season = product.season.name,
                gender = product.gender.name,
                sizes = gson.toJson(product.sizes),
                colors = gson.toJson(product.colors),
                materials = gson.toJson(product.materials),
                purchasePrice = product.purchasePrice,
                sellingPrice = product.sellingPrice,
                stock = product.stock,
                minimumStock = product.minimumStock,
                imageUrls = gson.toJson(product.imageUrls),
                tags = gson.toJson(product.tags),
                isActive = product.isActive,
                createdAt = product.createdAt.time,
                updatedAt = product.updatedAt.time
            )
        }
    }
}