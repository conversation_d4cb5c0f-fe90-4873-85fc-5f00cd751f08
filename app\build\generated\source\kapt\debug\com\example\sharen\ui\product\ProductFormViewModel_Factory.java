package com.example.sharen.ui.product;

import com.example.sharen.data.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductFormViewModel_Factory implements Factory<ProductFormViewModel> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public ProductFormViewModel_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public ProductFormViewModel get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static ProductFormViewModel_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new ProductFormViewModel_Factory(productRepositoryProvider);
  }

  public static ProductFormViewModel newInstance(ProductRepository productRepository) {
    return new ProductFormViewModel(productRepository);
  }
}
