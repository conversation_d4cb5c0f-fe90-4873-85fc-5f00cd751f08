package com.example.sharen.ui.product;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductFormViewModel_Factory implements Factory<ProductFormViewModel> {
  private final Provider<NonExistentClass> productRepositoryProvider;

  public ProductFormViewModel_Factory(Provider<NonExistentClass> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public ProductFormViewModel get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static ProductFormViewModel_Factory create(
      Provider<NonExistentClass> productRepositoryProvider) {
    return new ProductFormViewModel_Factory(productRepositoryProvider);
  }

  public static ProductFormViewModel newInstance(NonExistentClass productRepository) {
    return new ProductFormViewModel(productRepository);
  }
}
