<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_payment_list" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_payment_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_payment_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/tilSearch" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="24" startOffset="8" endLine="42" endOffset="63"/></Target><Target id="@+id/etSearch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="37" startOffset="12" endLine="41" endOffset="42"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="44" startOffset="8" endLine="54" endOffset="51"/></Target><Target id="@+id/tvEmptyState" view="TextView"><Expressions/><location startLine="56" startOffset="8" endLine="68" endOffset="66"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="70" startOffset="8" endLine="79" endOffset="66"/></Target><Target id="@+id/bottomNavigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="83" startOffset="4" endLine="88" endOffset="49"/></Target><Target id="@+id/fabAddPayment" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="90" startOffset="4" endLine="98" endOffset="33"/></Target></Targets></Layout>