package com.example.sharen.ui.product;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010#\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\t\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0002J\u0010\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u0012\u0010\u001b\u001a\u00020\u00172\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0014J\b\u0010\u001e\u001a\u00020\u0017H\u0014J\b\u0010\u001f\u001a\u00020 H\u0016J\b\u0010!\u001a\u00020\u0017H\u0002J\b\u0010\"\u001a\u00020\u0017H\u0002J\b\u0010#\u001a\u00020\u0017H\u0002J\b\u0010$\u001a\u00020\u0017H\u0002J\b\u0010%\u001a\u00020\u0017H\u0002J\b\u0010&\u001a\u00020\u0017H\u0002J\b\u0010\'\u001a\u00020\u0017H\u0002J\u0016\u0010(\u001a\u00020\u00172\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u001a0*H\u0002J\u0010\u0010+\u001a\u00020\u00172\u0006\u0010,\u001a\u00020-H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n \r*\u0004\u0018\u00010\f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0010\u001a\u00020\u00118BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006."}, d2 = {"Lcom/example/sharen/ui/product/ProductListActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "Lcom/example/sharen/databinding/ActivityProductListBinding;", "categories", "", "", "categoryChips", "", "Lcom/google/android/material/chip/Chip;", "numberFormatter", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "productAdapter", "Lcom/example/sharen/ui/product/ProductAdapter;", "viewModel", "Lcom/example/sharen/ui/product/ProductListViewModel;", "getViewModel", "()Lcom/example/sharen/ui/product/ProductListViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "navigateToAddProduct", "", "navigateToProductDetails", "product", "Lcom/example/sharen/data/model/Product;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onResume", "onSupportNavigateUp", "", "setupFab", "setupFilterButton", "setupFilterChips", "setupObservers", "setupRecyclerView", "setupSearchView", "setupToolbar", "updateCategories", "products", "", "updateProductCount", "count", "", "app_debug"})
public final class ProductListActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.example.sharen.databinding.ActivityProductListBinding binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private com.example.sharen.ui.product.ProductAdapter productAdapter;
    private final java.text.NumberFormat numberFormatter = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Set<java.lang.String> categories = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Map<java.lang.String, com.google.android.material.chip.Chip> categoryChips = null;
    
    public ProductListActivity() {
        super();
    }
    
    private final com.example.sharen.ui.product.ProductListViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupSearchView() {
    }
    
    private final void setupFilterButton() {
    }
    
    private final void setupFilterChips() {
    }
    
    private final void setupFab() {
    }
    
    private final void setupObservers() {
    }
    
    private final void updateProductCount(int count) {
    }
    
    private final void updateCategories(java.util.List<com.example.sharen.data.model.Product> products) {
    }
    
    private final void navigateToProductDetails(com.example.sharen.data.model.Product product) {
    }
    
    private final void navigateToAddProduct() {
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
    
    @java.lang.Override
    protected void onResume() {
    }
}