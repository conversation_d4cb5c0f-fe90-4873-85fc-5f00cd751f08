package com.example.sharen.ui.payment

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentMethod
import com.example.sharen.data.repository.InvoiceRepository
import com.example.sharen.data.repository.PaymentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class PaymentViewModel @Inject constructor(
    private val invoiceRepository: InvoiceRepository,
    private val paymentRepository: PaymentRepository
) : ViewModel() {

    private var _invoiceId: String? = null

    private val _invoiceDetails = MutableLiveData<Invoice?>()
    val invoiceDetails: LiveData<Invoice?> = _invoiceDetails

    private val _paymentResult = MutableLiveData<Boolean>()
    val paymentResult: LiveData<Boolean> = _paymentResult

    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _payments = MutableStateFlow<List<Payment>>(emptyList())
    val payments: StateFlow<List<Payment>> = _payments.asStateFlow()

    private val _loading = MutableStateFlow(false)
    val loading: StateFlow<Boolean> = _loading.asStateFlow()

    init {
        loadPayments()
    }

    fun setInvoiceId(invoiceId: String) {
        _invoiceId = invoiceId
    }

    fun loadInvoiceDetails() {
        val invoiceId = _invoiceId ?: return
        viewModelScope.launch {
            try {
                _isLoading.value = true
                invoiceRepository.getInvoiceById(invoiceId).collect { invoice ->
                    _invoiceDetails.value = invoice
                    _isLoading.value = false
                }
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در بارگذاری اطلاعات فاکتور"
                _isLoading.value = false
            }
        }
    }

    fun submitPayment(
        amount: Long,
        method: PaymentMethod,
        referenceNumber: String?,
        notes: String?
    ) {
        val invoiceId = _invoiceId ?: return
        val invoice = _invoiceDetails.value ?: return

        if (amount <= 0) {
            _error.value = "لطفاً مبلغ معتبر وارد کنید"
            return
        }

        if (amount > invoice.remainingAmount) {
            _error.value = "مبلغ وارد شده بیشتر از مبلغ باقی‌مانده است"
            return
        }

        viewModelScope.launch {
            try {
                _isLoading.value = true

                val payment = Payment(
                    invoiceId = invoiceId,
                    amount = amount,
                    method = method,
                    referenceNumber = referenceNumber.takeIf { !it.isNullOrBlank() },
                    notes = notes.takeIf { !it.isNullOrBlank() },
                    paymentDate = Date()
                )

                val result = invoiceRepository.addPayment(payment)
                if (result.isSuccess) {
                    _paymentResult.value = true
                } else {
                    _error.value = "خطا در ثبت پرداخت"
                    _paymentResult.value = false
                }
                _isLoading.value = false
            } catch (e: Exception) {
                _error.value = e.message ?: "خطا در ثبت پرداخت"
                _paymentResult.value = false
                _isLoading.value = false
            }
        }
    }

    fun loadPayments() {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.getAllPayments()
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { payments ->
                        _payments.value = payments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun createPayment(payment: Payment) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.createPayment(payment)
                    .onSuccess {
                        loadPayments()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun updatePayment(payment: Payment) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.updatePayment(payment)
                    .onSuccess {
                        loadPayments()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun deletePayment(paymentId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.deletePayment(paymentId)
                    .onSuccess {
                        loadPayments()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun confirmPayment(paymentId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.confirmPayment(paymentId)
                    .onSuccess {
                        loadPayments()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun rejectPayment(paymentId: String, reason: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.rejectPayment(paymentId, reason)
                    .onSuccess {
                        loadPayments()
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getPaymentsByCustomer(customerId: String) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.getPaymentsByCustomer(customerId)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { payments ->
                        _payments.value = payments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getPaymentsByDateRange(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.getPaymentsByDateRange(startDate, endDate)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { payments ->
                        _payments.value = payments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getPaymentsByStatus(status: PaymentStatus) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.getPaymentsByStatus(status)
                    .catch { e ->
                        _error.value = e.message
                    }
                    .collect { payments ->
                        _payments.value = payments
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun getPaymentStatistics(startDate: Date, endDate: Date) {
        viewModelScope.launch {
            _loading.value = true
            try {
                paymentRepository.getPaymentStatistics(startDate, endDate)
                    .onSuccess { statistics ->
                        // Handle statistics
                    }
                    .onFailure { e ->
                        _error.value = e.message
                    }
            } finally {
                _loading.value = false
            }
        }
    }

    fun clearError() {
        _error.value = null
    }
} 