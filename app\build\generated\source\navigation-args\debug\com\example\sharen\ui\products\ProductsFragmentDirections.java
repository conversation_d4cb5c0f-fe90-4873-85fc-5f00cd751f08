package com.example.sharen.ui.products;

import androidx.annotation.NonNull;
import androidx.navigation.ActionOnlyNavDirections;
import androidx.navigation.NavDirections;
import com.example.sharen.R;

public class ProductsFragmentDirections {
  private ProductsFragmentDirections() {
  }

  @NonNull
  public static NavDirections actionProductsToAddProduct() {
    return new ActionOnlyNavDirections(R.id.action_products_to_addProduct);
  }

  @NonNull
  public static NavDirections actionProductsToProductDetails() {
    return new ActionOnlyNavDirections(R.id.action_products_to_productDetails);
  }
}
