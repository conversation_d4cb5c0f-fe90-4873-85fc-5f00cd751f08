package com.example.sharen.ui.payment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u0012\u0012\u0004\u0012\u00020\u0002\u0012\b\u0012\u00060\u0003R\u00020\u00000\u0001:\u0002\u0016\u0017BQ\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\n0\t\u0012\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\n0\t\u0012\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\u0002\u0010\rJ\u001c\u0010\u000e\u001a\u00020\n2\n\u0010\u000f\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u0010\u001a\u00020\u0011H\u0016J\u001c\u0010\u0012\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0011H\u0016R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/sharen/ui/payment/InstallmentAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/sharen/data/model/Installment;", "Lcom/example/sharen/ui/payment/InstallmentAdapter$InstallmentViewHolder;", "numberFormatter", "Ljava/text/NumberFormat;", "dateFormatter", "Ljava/text/SimpleDateFormat;", "onInstallmentClick", "Lkotlin/Function1;", "", "onPayInstallmentClick", "onRemindInstallmentClick", "(Ljava/text/NumberFormat;Ljava/text/SimpleDateFormat;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "onBindViewHolder", "holder", "position", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "InstallmentDiffCallback", "InstallmentViewHolder", "app_debug"})
public final class InstallmentAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.sharen.data.model.Installment, com.example.sharen.ui.payment.InstallmentAdapter.InstallmentViewHolder> {
    @org.jetbrains.annotations.NotNull
    private final java.text.NumberFormat numberFormatter = null;
    @org.jetbrains.annotations.NotNull
    private final java.text.SimpleDateFormat dateFormatter = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function1<com.example.sharen.data.model.Installment, kotlin.Unit> onInstallmentClick = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function1<com.example.sharen.data.model.Installment, kotlin.Unit> onPayInstallmentClick = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function1<com.example.sharen.data.model.Installment, kotlin.Unit> onRemindInstallmentClick = null;
    
    public InstallmentAdapter(@org.jetbrains.annotations.NotNull
    java.text.NumberFormat numberFormatter, @org.jetbrains.annotations.NotNull
    java.text.SimpleDateFormat dateFormatter, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.example.sharen.data.model.Installment, kotlin.Unit> onInstallmentClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.example.sharen.data.model.Installment, kotlin.Unit> onPayInstallmentClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.example.sharen.data.model.Installment, kotlin.Unit> onRemindInstallmentClick) {
        super(null);
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.ui.payment.InstallmentAdapter.InstallmentViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.example.sharen.ui.payment.InstallmentAdapter.InstallmentViewHolder holder, int position) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/example/sharen/ui/payment/InstallmentAdapter$InstallmentDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/sharen/data/model/Installment;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    public static final class InstallmentDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.sharen.data.model.Installment> {
        
        public InstallmentDiffCallback() {
            super();
        }
        
        @java.lang.Override
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Installment oldItem, @org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Installment newItem) {
            return false;
        }
        
        @java.lang.Override
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Installment oldItem, @org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Installment newItem) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u001c\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n0\f2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/example/sharen/ui/payment/InstallmentAdapter$InstallmentViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "Lcom/example/sharen/databinding/ItemInstallmentBinding;", "(Lcom/example/sharen/ui/payment/InstallmentAdapter;Lcom/example/sharen/databinding/ItemInstallmentBinding;)V", "bind", "", "installment", "Lcom/example/sharen/data/model/Installment;", "position", "", "getStatusInfo", "Lkotlin/Pair;", "", "status", "Lcom/example/sharen/data/model/InstallmentStatus;", "app_debug"})
    public final class InstallmentViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final com.example.sharen.databinding.ItemInstallmentBinding binding = null;
        
        public InstallmentViewHolder(@org.jetbrains.annotations.NotNull
        com.example.sharen.databinding.ItemInstallmentBinding binding) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.Installment installment, int position) {
        }
        
        private final kotlin.Pair<java.lang.String, java.lang.Integer> getStatusInfo(com.example.sharen.data.model.InstallmentStatus status) {
            return null;
        }
    }
}