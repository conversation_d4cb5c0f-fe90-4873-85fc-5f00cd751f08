<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_payment" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_payment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_payment_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="282" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="43"/></Target><Target id="@+id/tvInvoiceNumber" view="TextView"><Expressions/><location startLine="68" startOffset="24" endLine="73" endOffset="53"/></Target><Target id="@+id/tvCustomerName" view="TextView"><Expressions/><location startLine="89" startOffset="24" endLine="94" endOffset="52"/></Target><Target id="@+id/tvInvoiceDate" view="TextView"><Expressions/><location startLine="110" startOffset="24" endLine="115" endOffset="53"/></Target><Target id="@+id/tvTotalAmount" view="TextView"><Expressions/><location startLine="131" startOffset="24" endLine="136" endOffset="58"/></Target><Target id="@+id/tvPaidAmount" view="TextView"><Expressions/><location startLine="152" startOffset="24" endLine="157" endOffset="56"/></Target><Target id="@+id/tvRemainingAmount" view="TextView"><Expressions/><location startLine="172" startOffset="24" endLine="178" endOffset="56"/></Target><Target id="@+id/etAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="218" startOffset="24" endLine="222" endOffset="56"/></Target><Target id="@+id/spinnerPaymentMethod" view="Spinner"><Expressions/><location startLine="233" startOffset="20" endLine="239" endOffset="48"/></Target><Target id="@+id/etReferenceNumber" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="249" startOffset="24" endLine="253" endOffset="54"/></Target><Target id="@+id/etNotes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="264" startOffset="24" endLine="269" endOffset="50"/></Target><Target id="@+id/btnSubmitPayment" view="Button"><Expressions/><location startLine="273" startOffset="20" endLine="277" endOffset="63"/></Target></Targets></Layout>