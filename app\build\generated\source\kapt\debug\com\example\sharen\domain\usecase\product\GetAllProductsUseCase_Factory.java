package com.example.sharen.domain.usecase.product;

import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetAllProductsUseCase_Factory implements Factory<GetAllProductsUseCase> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public GetAllProductsUseCase_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public GetAllProductsUseCase get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static GetAllProductsUseCase_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new GetAllProductsUseCase_Factory(productRepositoryProvider);
  }

  public static GetAllProductsUseCase newInstance(ProductRepository productRepository) {
    return new GetAllProductsUseCase(productRepository);
  }
}
