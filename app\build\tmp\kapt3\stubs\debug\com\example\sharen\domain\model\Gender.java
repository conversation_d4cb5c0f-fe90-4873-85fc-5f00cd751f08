package com.example.sharen.domain.model;

/**
 * جنسیت
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/sharen/domain/model/Gender;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "MALE", "FEMALE", "UNISEX", "KIDS", "app_debug"})
public enum Gender {
    /*public static final*/ MALE /* = new MALE(null) */,
    /*public static final*/ FEMALE /* = new FEMALE(null) */,
    /*public static final*/ UNISEX /* = new UNISEX(null) */,
    /*public static final*/ KIDS /* = new KIDS(null) */;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String displayName = null;
    
    Gender(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.domain.model.Gender> getEntries() {
        return null;
    }
}