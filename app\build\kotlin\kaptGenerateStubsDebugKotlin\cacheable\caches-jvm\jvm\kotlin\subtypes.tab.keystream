androidx.room.RoomDatabase$com.example.sharen.data.model.Result1com.example.sharen.data.repository.AuthRepositoryandroid.os.Parcelablekotlin.Enum9com.example.sharen.domain.usecase.customer.CustomerFilter)com.example.sharen.core.base.BaseActivity4com.example.sharen.presentation.viewmodel.LoginState*com.example.sharen.core.base.BaseViewModel-com.example.sharen.ui.payment.AddPaymentStateOcom.google.android.material.navigation.NavigationBarView.OnItemSelectedListenerandroidx.fragment.app.Fragment(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback(androidx.appcompat.app.AppCompatActivityandroidx.lifecycle.ViewModel androidx.viewbinding.ViewBindingandroid.app.Application7com.example.sharen.domain.repository.CustomerRepository8com.example.sharen.data.repository.InstallmentRepository9com.example.sharen.data.repository.NotificationRepository6com.example.sharen.domain.repository.ProductRepository3com.example.sharen.data.repository.ReportRepository3com.example.sharen.domain.repository.UserRepositoryandroid.app.Dialog                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              