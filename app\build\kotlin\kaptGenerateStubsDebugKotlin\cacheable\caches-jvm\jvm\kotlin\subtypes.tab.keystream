androidx.room.RoomDatabase$com.example.sharen.data.model.Result1com.example.sharen.data.repository.AuthRepositorykotlin.Enum-com.example.sharen.ui.payment.AddPaymentStateOcom.google.android.material.navigation.NavigationBarView.OnItemSelectedListenerandroidx.fragment.app.Fragment(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback(androidx.appcompat.app.AppCompatActivityandroidx.lifecycle.ViewModel androidx.viewbinding.ViewBindingandroid.app.Application5com.example.sharen.data.repository.CustomerRepository8com.example.sharen.data.repository.InstallmentRepository4com.example.sharen.data.repository.InvoiceRepository9com.example.sharen.data.repository.NotificationRepository4com.example.sharen.data.repository.PaymentRepository4com.example.sharen.data.repository.ProductRepository3com.example.sharen.data.repository.ReportRepositoryandroid.app.Dialog                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      