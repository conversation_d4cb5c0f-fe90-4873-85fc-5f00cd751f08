package com.example.sharen.ui.profile;

import com.example.sharen.data.repository.AuthRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PasswordChangeViewModel_Factory implements Factory<PasswordChangeViewModel> {
  private final Provider<AuthRepository> authRepositoryProvider;

  public PasswordChangeViewModel_Factory(Provider<AuthRepository> authRepositoryProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public PasswordChangeViewModel get() {
    return newInstance(authRepositoryProvider.get());
  }

  public static PasswordChangeViewModel_Factory create(
      Provider<AuthRepository> authRepositoryProvider) {
    return new PasswordChangeViewModel_Factory(authRepositoryProvider);
  }

  public static PasswordChangeViewModel newInstance(AuthRepository authRepository) {
    return new PasswordChangeViewModel(authRepository);
  }
}
