package com.example.sharen.data.migration

import android.content.Context
import android.util.Log
import com.example.sharen.data.local.SharenDatabase
import com.example.sharen.data.local.entity.*
import com.example.sharen.domain.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * کلاس کمکی برای Migration داده‌ها از ساختار قدیم به جدید
 */
@Singleton
class DataMigrationHelper @Inject constructor(
    private val database: SharenDatabase
) {
    
    companion object {
        private const val TAG = "DataMigrationHelper"
        private const val MIGRATION_PREF_KEY = "data_migration_completed"
    }
    
    /**
     * اجرای Migration کامل داده‌ها
     */
    suspend fun performDataMigration(context: Context): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "شروع Migration داده‌ها...")
            
            // بررسی اینکه آیا Migration قبلاً انجام شده یا نه
            val sharedPrefs = context.getSharedPreferences("migration_prefs", Context.MODE_PRIVATE)
            if (sharedPrefs.getBoolean(MIGRATION_PREF_KEY, false)) {
                Log.d(TAG, "Migration قبلاً انجام شده است")
                return@withContext Result.success(Unit)
            }
            
            // Migration مرحله به مرحله
            migrateUsers()
            migrateCustomers()
            migrateProducts()
            migrateCategories()
            createSampleData()
            
            // علامت‌گذاری Migration به عنوان تکمیل شده
            sharedPrefs.edit().putBoolean(MIGRATION_PREF_KEY, true).apply()
            
            Log.d(TAG, "Migration داده‌ها با موفقیت تکمیل شد")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "خطا در Migration داده‌ها", e)
            Result.failure(e)
        }
    }
    
    /**
     * Migration کاربران
     */
    private suspend fun migrateUsers() {
        Log.d(TAG, "Migration کاربران...")
        
        // ایجاد کاربر پیش‌فرض (Admin)
        val adminUser = UserEntity(
            id = "admin_user_1",
            email = "<EMAIL>",
            passwordHash = "hashed_password", // در واقعیت باید hash شود
            name = "مدیر سیستم",
            phone = "09123456789",
            role = UserRole.ADMIN.name,
            isActive = true,
            lastLoginAt = null,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
        
        database.userDao().insertUser(adminUser)
        Log.d(TAG, "کاربر Admin ایجاد شد")
    }
    
    /**
     * Migration مشتریان
     */
    private suspend fun migrateCustomers() {
        Log.d(TAG, "Migration مشتریان...")
        
        // ایجاد چند مشتری نمونه
        val sampleCustomers = listOf(
            CustomerEntity(
                id = "customer_1",
                userId = "admin_user_1",
                name = "احمد محمدی",
                phone = "09123456789",
                address = "تهران، خیابان ولیعصر، پلاک 123",
                creditLimit = 5_000_000,
                totalPurchases = 2_500_000,
                totalPayments = 2_000_000,
                totalDebt = 500_000,
                lastPurchaseDate = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000), // 7 روز پیش
                lastPaymentDate = System.currentTimeMillis() - (3 * 24 * 60 * 60 * 1000), // 3 روز پیش
                notes = "مشتری ویژه",
                createdAt = System.currentTimeMillis() - (180 * 24 * 60 * 60 * 1000), // 6 ماه پیش
                updatedAt = System.currentTimeMillis()
            ),
            CustomerEntity(
                id = "customer_2",
                userId = "admin_user_1",
                name = "فاطمه احمدی",
                phone = "09987654321",
                address = "اصفهان، خیابان چهارباغ، پلاک 45",
                creditLimit = 3_000_000,
                totalPurchases = 1_800_000,
                totalPayments = 1_800_000,
                totalDebt = 0,
                lastPurchaseDate = System.currentTimeMillis() - (15 * 24 * 60 * 60 * 1000), // 15 روز پیش
                lastPaymentDate = System.currentTimeMillis() - (10 * 24 * 60 * 60 * 1000), // 10 روز پیش
                notes = null,
                createdAt = System.currentTimeMillis() - (120 * 24 * 60 * 60 * 1000), // 4 ماه پیش
                updatedAt = System.currentTimeMillis()
            ),
            CustomerEntity(
                id = "customer_3",
                userId = "admin_user_1",
                name = "علی رضایی",
                phone = "09111111111",
                address = "شیراز، خیابان زند، پلاک 78",
                creditLimit = 10_000_000,
                totalPurchases = 8_500_000,
                totalPayments = 7_000_000,
                totalDebt = 1_500_000,
                lastPurchaseDate = System.currentTimeMillis() - (2 * 24 * 60 * 60 * 1000), // 2 روز پیش
                lastPaymentDate = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000), // 30 روز پیش
                notes = "خرید عمده",
                createdAt = System.currentTimeMillis() - (90 * 24 * 60 * 60 * 1000), // 3 ماه پیش
                updatedAt = System.currentTimeMillis()
            )
        )
        
        database.customerDao().insertCustomers(sampleCustomers)
        Log.d(TAG, "${sampleCustomers.size} مشتری نمونه ایجاد شد")
    }
    
    /**
     * Migration محصولات
     */
    private suspend fun migrateProducts() {
        Log.d(TAG, "Migration محصولات...")
        
        // ایجاد چند محصول نمونه
        val sampleProducts = listOf(
            ProductEntity(
                id = "product_1",
                name = "پیراهن مردانه کلاسیک",
                code = "SH-001",
                barcode = "1234567890123",
                description = "پیراهن مردانه با کیفیت عالی و طراحی کلاسیک",
                categoryId = "category_1",
                brandId = null,
                type = ProductType.CLOTHING.name,
                season = Season.ALL_SEASON.name,
                gender = Gender.MALE.name,
                sizes = "[\"S\", \"M\", \"L\", \"XL\"]",
                colors = "[\"سفید\", \"آبی\", \"مشکی\"]",
                materials = "[\"پنبه\"]",
                purchasePrice = 150_000,
                sellingPrice = 250_000,
                stock = 25,
                minimumStock = 5,
                imageUrls = "[]",
                tags = "[\"پیراهن\", \"مردانه\", \"کلاسیک\"]",
                isActive = true,
                createdAt = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000),
                updatedAt = System.currentTimeMillis()
            ),
            ProductEntity(
                id = "product_2",
                name = "شلوار جین زنانه",
                code = "PA-001",
                barcode = "1234567890124",
                description = "شلوار جین زنانه با طراحی مدرن",
                categoryId = "category_2",
                brandId = null,
                type = ProductType.CLOTHING.name,
                season = Season.ALL_SEASON.name,
                gender = Gender.FEMALE.name,
                sizes = "[\"S\", \"M\", \"L\", \"XL\"]",
                colors = "[\"آبی\", \"مشکی\"]",
                materials = "[\"جین\"]",
                purchasePrice = 200_000,
                sellingPrice = 350_000,
                stock = 15,
                minimumStock = 3,
                imageUrls = "[]",
                tags = "[\"شلوار\", \"جین\", \"زنانه\"]",
                isActive = true,
                createdAt = System.currentTimeMillis() - (45 * 24 * 60 * 60 * 1000),
                updatedAt = System.currentTimeMillis()
            ),
            ProductEntity(
                id = "product_3",
                name = "کفش ورزشی",
                code = "SH-002",
                barcode = "1234567890125",
                description = "کفش ورزشی با کیفیت بالا",
                categoryId = "category_3",
                brandId = null,
                type = ProductType.SHOES.name,
                season = Season.ALL_SEASON.name,
                gender = Gender.UNISEX.name,
                sizes = "[\"38\", \"39\", \"40\", \"41\", \"42\", \"43\", \"44\"]",
                colors = "[\"سفید\", \"مشکی\", \"قرمز\"]",
                materials = "[\"چرم مصنوعی\", \"پارچه\"]",
                purchasePrice = 350_000,
                sellingPrice = 580_000,
                stock = 10,
                minimumStock = 2,
                imageUrls = "[]",
                tags = "[\"کفش\", \"ورزشی\", \"راحتی\"]",
                isActive = true,
                createdAt = System.currentTimeMillis() - (15 * 24 * 60 * 60 * 1000),
                updatedAt = System.currentTimeMillis()
            )
        )
        
        database.productDao().insertProducts(sampleProducts)
        Log.d(TAG, "${sampleProducts.size} محصول نمونه ایجاد شد")
    }
    
    /**
     * Migration دسته‌بندی‌ها
     */
    private suspend fun migrateCategories() {
        Log.d(TAG, "Migration دسته‌بندی‌ها...")
        
        val sampleCategories = listOf(
            CategoryEntity(
                id = "category_1",
                name = "پوشاک مردانه",
                description = "انواع پوشاک مردانه",
                parentId = null,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            ),
            CategoryEntity(
                id = "category_2",
                name = "پوشاک زنانه",
                description = "انواع پوشاک زنانه",
                parentId = null,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            ),
            CategoryEntity(
                id = "category_3",
                name = "کفش",
                description = "انواع کفش",
                parentId = null,
                isActive = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
        )
        
        database.categoryDao().insertCategories(sampleCategories)
        Log.d(TAG, "${sampleCategories.size} دسته‌بندی ایجاد شد")
    }
    
    /**
     * ایجاد داده‌های نمونه اضافی
     */
    private suspend fun createSampleData() {
        Log.d(TAG, "ایجاد داده‌های نمونه اضافی...")
        
        // ایجاد چند پرداخت نمونه
        val samplePayments = listOf(
            PaymentEntity(
                id = "payment_1",
                customerId = "customer_1",
                invoiceId = null,
                amount = 1_000_000,
                method = PaymentMethod.CASH.name,
                status = PaymentStatus.COMPLETED.name,
                description = "پرداخت نقدی",
                referenceNumber = null,
                createdAt = System.currentTimeMillis() - (3 * 24 * 60 * 60 * 1000),
                updatedAt = System.currentTimeMillis() - (3 * 24 * 60 * 60 * 1000)
            ),
            PaymentEntity(
                id = "payment_2",
                customerId = "customer_2",
                invoiceId = null,
                amount = 800_000,
                method = PaymentMethod.BANK_TRANSFER.name,
                status = PaymentStatus.COMPLETED.name,
                description = "انتقال بانکی",
                referenceNumber = "TXN123456",
                createdAt = System.currentTimeMillis() - (10 * 24 * 60 * 60 * 1000),
                updatedAt = System.currentTimeMillis() - (10 * 24 * 60 * 60 * 1000)
            )
        )
        
        database.paymentDao().insertPayments(samplePayments)
        Log.d(TAG, "${samplePayments.size} پرداخت نمونه ایجاد شد")
    }
    
    /**
     * پاک کردن تمام داده‌ها (برای تست)
     */
    suspend fun clearAllData(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            database.clearAllTables()
            Log.d(TAG, "تمام داده‌ها پاک شد")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "خطا در پاک کردن داده‌ها", e)
            Result.failure(e)
        }
    }
}
