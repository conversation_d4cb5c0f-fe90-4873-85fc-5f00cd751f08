package com.example.sharen.data.local.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u0010\bg\u0018\u00002\u00020\u0001J\u0011\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u001b\u0010\f\u001a\u0004\u0018\u00010\u00072\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u0011\u001a\u00020\u000eH\'J$\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\'J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u0017\u001a\u00020\u000eH\'J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u0019\u001a\u00020\u000eH\'J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u001b\u001a\u00020\u000eH\'J\u001b\u0010\u001c\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0011\u001a\u00020\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ#\u0010\u001d\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001eJ\u0019\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u001f\u0010 \u001a\u00020\u00032\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\"J\u0019\u0010#\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\b\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006$"}, d2 = {"Lcom/example/sharen/data/local/dao/PaymentDao;", "", "deleteAllPayments", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePayment", "payment", "Lcom/example/sharen/data/local/entity/PaymentEntity;", "(Lcom/example/sharen/data/local/entity/PaymentEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllPayments", "Lkotlinx/coroutines/flow/Flow;", "", "getPaymentById", "paymentId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPaymentsByCustomer", "customerId", "getPaymentsByDateRange", "startDate", "", "endDate", "getPaymentsByInvoice", "invoiceId", "getPaymentsByMethod", "method", "getPaymentsByStatus", "status", "getTotalPaymentsByCustomer", "getTotalPaymentsByDateRange", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertPayment", "insertPayments", "payments", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePayment", "app_debug"})
@androidx.room.Dao
public abstract interface PaymentDao {
    
    @androidx.room.Query(value = "SELECT * FROM payments ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.PaymentEntity>> getAllPayments();
    
    @androidx.room.Query(value = "SELECT * FROM payments WHERE id = :paymentId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPaymentById(@org.jetbrains.annotations.NotNull
    java.lang.String paymentId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.PaymentEntity> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM payments WHERE customerId = :customerId ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.PaymentEntity>> getPaymentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    @androidx.room.Query(value = "SELECT * FROM payments WHERE invoiceId = :invoiceId ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.PaymentEntity>> getPaymentsByInvoice(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId);
    
    @androidx.room.Query(value = "SELECT * FROM payments WHERE createdAt BETWEEN :startDate AND :endDate ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.PaymentEntity>> getPaymentsByDateRange(long startDate, long endDate);
    
    @androidx.room.Query(value = "SELECT * FROM payments WHERE method = :method ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.PaymentEntity>> getPaymentsByMethod(@org.jetbrains.annotations.NotNull
    java.lang.String method);
    
    @androidx.room.Query(value = "SELECT * FROM payments WHERE status = :status ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.local.entity.PaymentEntity>> getPaymentsByStatus(@org.jetbrains.annotations.NotNull
    java.lang.String status);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM payments WHERE customerId = :customerId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalPaymentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(amount) FROM payments WHERE createdAt BETWEEN :startDate AND :endDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalPaymentsByDateRange(long startDate, long endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertPayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.PaymentEntity payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertPayments(@org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.data.local.entity.PaymentEntity> payments, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updatePayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.PaymentEntity payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deletePayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.entity.PaymentEntity payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM payments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAllPayments(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}