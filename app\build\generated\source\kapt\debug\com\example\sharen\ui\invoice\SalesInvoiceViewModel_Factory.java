package com.example.sharen.ui.invoice;

import com.example.sharen.data.repository.InvoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SalesInvoiceViewModel_Factory implements Factory<SalesInvoiceViewModel> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  public SalesInvoiceViewModel_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
  }

  @Override
  public SalesInvoiceViewModel get() {
    return newInstance(invoiceRepositoryProvider.get());
  }

  public static SalesInvoiceViewModel_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider) {
    return new SalesInvoiceViewModel_Factory(invoiceRepositoryProvider);
  }

  public static SalesInvoiceViewModel newInstance(InvoiceRepository invoiceRepository) {
    return new SalesInvoiceViewModel(invoiceRepository);
  }
}
