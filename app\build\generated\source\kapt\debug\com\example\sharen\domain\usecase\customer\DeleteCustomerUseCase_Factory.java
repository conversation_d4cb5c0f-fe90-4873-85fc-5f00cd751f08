package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeleteCustomerUseCase_Factory implements Factory<DeleteCustomerUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public DeleteCustomerUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public DeleteCustomerUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static DeleteCustomerUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new DeleteCustomerUseCase_Factory(customerRepositoryProvider);
  }

  public static DeleteCustomerUseCase newInstance(CustomerRepository customerRepository) {
    return new DeleteCustomerUseCase(customerRepository);
  }
}
