// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDisplaySettingsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final SeekBar fontSizeSeekBar;

  @NonNull
  public final RadioButton radioDark;

  @NonNull
  public final RadioButton radioLight;

  @NonNull
  public final RadioButton radioSystem;

  @NonNull
  public final RadioGroup themeModeGroup;

  @NonNull
  public final Toolbar toolbar;

  private ActivityDisplaySettingsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull SeekBar fontSizeSeekBar, @NonNull RadioButton radioDark,
      @NonNull RadioButton radioLight, @NonNull RadioButton radioSystem,
      @NonNull RadioGroup themeModeGroup, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.fontSizeSeekBar = fontSizeSeekBar;
    this.radioDark = radioDark;
    this.radioLight = radioLight;
    this.radioSystem = radioSystem;
    this.themeModeGroup = themeModeGroup;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDisplaySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDisplaySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_display_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDisplaySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fontSizeSeekBar;
      SeekBar fontSizeSeekBar = ViewBindings.findChildViewById(rootView, id);
      if (fontSizeSeekBar == null) {
        break missingId;
      }

      id = R.id.radioDark;
      RadioButton radioDark = ViewBindings.findChildViewById(rootView, id);
      if (radioDark == null) {
        break missingId;
      }

      id = R.id.radioLight;
      RadioButton radioLight = ViewBindings.findChildViewById(rootView, id);
      if (radioLight == null) {
        break missingId;
      }

      id = R.id.radioSystem;
      RadioButton radioSystem = ViewBindings.findChildViewById(rootView, id);
      if (radioSystem == null) {
        break missingId;
      }

      id = R.id.themeModeGroup;
      RadioGroup themeModeGroup = ViewBindings.findChildViewById(rootView, id);
      if (themeModeGroup == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityDisplaySettingsBinding((CoordinatorLayout) rootView, fontSizeSeekBar,
          radioDark, radioLight, radioSystem, themeModeGroup, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
