package com.example.sharen.di;

import com.example.sharen.data.local.SharenDatabase;
import com.example.sharen.data.local.dao.InvoiceDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideInvoiceDaoFactory implements Factory<InvoiceDao> {
  private final Provider<SharenDatabase> databaseProvider;

  public DatabaseModule_ProvideInvoiceDaoFactory(Provider<SharenDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public InvoiceDao get() {
    return provideInvoiceDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideInvoiceDaoFactory create(
      Provider<SharenDatabase> databaseProvider) {
    return new DatabaseModule_ProvideInvoiceDaoFactory(databaseProvider);
  }

  public static InvoiceDao provideInvoiceDao(SharenDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideInvoiceDao(database));
  }
}
