com.example.sharen.MainActivity$com.example.sharen.SharenApplication)com.example.sharen.core.base.BaseActivity)com.example.sharen.core.base.BaseFragment*com.example.sharen.core.base.BaseViewModel.com.example.sharen.core.constants.AppConstants*com.example.sharen.data.api.InstallmentApi(com.example.sharen.data.db.DateConverter5com.example.sharen.data.db.InstallmentStatusConverter2com.example.sharen.data.db.converter.DateConverter?com.example.sharen.data.db.converter.InstallmentStatusConverter)com.example.sharen.data.local.AppDatabase3com.example.sharen.data.local.AppDatabase.Companion(com.example.sharen.data.local.Converters,com.example.sharen.data.local.SharenDatabase6com.example.sharen.data.local.SharenDatabase.Companion5com.example.sharen.data.local.converter.DateConverter-com.example.sharen.data.local.dao.CategoryDao-com.example.sharen.data.local.dao.CustomerDao8com.example.sharen.data.local.dao.CustomerPurchaseEntity0com.example.sharen.data.local.dao.InstallmentDao,com.example.sharen.data.local.dao.InvoiceDao0com.example.sharen.data.local.dao.InvoiceItemDao,com.example.sharen.data.local.dao.PaymentDao,com.example.sharen.data.local.dao.ProductDao)com.example.sharen.data.local.dao.UserDao3com.example.sharen.data.local.entity.CategoryEntity=com.example.sharen.data.local.entity.CategoryEntity.Companion3com.example.sharen.data.local.entity.CustomerEntity=<EMAIL><<EMAIL>:com.example.sharen.data.local.entity.OrderEntity.Companion4com.example.sharen.data.local.entity.OrderItemEntity>com.example.sharen.data.local.entity.OrderItemEntity.Companion2com.example.sharen.data.local.entity.PaymentEntity<com.example.sharen.data.local.entity.PaymentEntity.Companion2com.example.sharen.data.local.entity.ProductEntity<<EMAIL>/com.example.sharen.data.local.entity.UserEntity9com.example.sharen.data.local.entity.UserEntity.Companion7com.example.sharen.data.local.relation.CustomerWithUser5com.example.sharen.data.migration.DataMigrationHelper?com.example.sharen.data.migration.DataMigrationHelper.Companion&com.example.sharen.data.model.Category&com.example.sharen.data.model.Customer)com.example.sharen.data.model.Installment/com.example.sharen.data.model.InstallmentStatus%com.example.sharen.data.model.Invoice+com.example.sharen.data.model.InvoiceStatus)com.example.sharen.data.model.PaymentType)com.example.sharen.data.model.InvoiceItem*com.example.sharen.data.model.Notification.com.example.sharen.data.model.NotificationType#com.example.sharen.data.model.Order)com.example.sharen.data.model.OrderStatus'com.example.sharen.data.model.OrderItem%com.example.sharen.data.model.Payment+com.example.sharen.data.model.PaymentMethod+com.example.sharen.data.model.PaymentStatus%com.example.sharen.data.model.Product$com.example.sharen.data.model.Report*com.example.sharen.data.model.ReportFilter1com.example.sharen.data.model.ReportExportOptions*com.example.sharen.data.model.ExportFormat(com.example.sharen.data.model.ReportData(com.example.sharen.data.model.ReportType$com.example.sharen.data.model.Result,com.example.sharen.data.model.Result.Success*com.example.sharen.data.model.Result.Error,com.example.sharen.data.model.Result.Loading$com.example.sharen.data.model.Seller)com.example.sharen.data.model.Transaction-com.example.sharen.data.model.TransactionType"com.example.sharen.data.model.User&com.example.sharen.data.model.UserRole3com.example.sharen.data.remote.AuthRemoteDataSource:com.example.sharen.data.remote.InstallmentRemoteDataSource6com.example.sharen.data.remote.PaymentRemoteDataSource5com.example.sharen.data.remote.ReportRemoteDataSource1com.example.sharen.data.remote.SupabaseApiService1com.example.sharen.data.repository.AuthRepository5com.example.sharen.data.repository.AuthRepositoryImpl?com.example.sharen.data.repository.AuthRepositoryImpl.Companion9com.example.sharen.data.repository.CustomerRepositoryImpl8com.example.sharen.data.repository.InstallmentRepository<com.example.sharen.data.repository.InstallmentRepositoryImpl4com.example.sharen.data.repository.InvoiceRepository8com.example.sharen.data.repository.InvoiceRepositoryImpl9com.example.sharen.data.repository.NotificationRepository=com.example.sharen.data.repository.NotificationRepositoryImpl8com.example.sharen.data.repository.PaymentRepositoryImpl8com.example.sharen.data.repository.ProductRepositoryImpl3com.example.sharen.data.repository.ReportRepository7com.example.sharen.data.repository.ReportRepositoryImpl1com.example.sharen.data.repository.UserRepository5com.example.sharen.data.repository.UserRepositoryImpl:com.example.sharen.data.repository.impl.AuthRepositoryImpl:com.example.sharen.data.repository.impl.UserRepositoryImpl$com.example.sharen.di.DatabaseModule#com.example.sharen.di.NetworkModule"com.example.sharen.di.ReportModule&com.example.sharen.di.RepositoryModule%com.example.sharen.di.ViewModelModule(com.example.sharen.domain.model.Customer,com.example.sharen.domain.model.CreditStatus+com.example.sharen.domain.model.Installment1com.example.sharen.domain.model.InstallmentStatus'com.example.sharen.domain.model.Invoice+com.example.sharen.domain.model.InvoiceItem-com.example.sharen.domain.model.InvoiceStatus+com.example.sharen.domain.model.PaymentType'com.example.sharen.domain.model.Payment-com.example.sharen.domain.model.PaymentStatus-com.example.sharen.domain.model.PaymentMethod'com.example.sharen.domain.model.Product+com.example.sharen.domain.model.ProductSize,com.example.sharen.domain.model.ProductColor&com.example.sharen.domain.model.Season&com.example.sharen.domain.model.Gender+com.example.sharen.domain.model.StockStatus$com.example.sharen.domain.model.User(com.example.sharen.domain.model.UserRole2com.example.sharen.domain.model.UserRole.Companion*com.example.sharen.domain.model.UserStatus7com.example.sharen.domain.repository.CustomerRepository7com.example.sharen.domain.repository.CustomerStatistics5com.example.sharen.domain.repository.CustomerPurchase6com.example.sharen.domain.repository.InvoiceRepository6com.example.sharen.domain.repository.PaymentRepository6com.example.sharen.domain.repository.ProductRepository3com.example.sharen.domain.repository.UserRepositoryDcom.example.sharen.domain.usecase.analytics.GetDashboardStatsUseCase3com.example.sharen.domain.usecase.auth.LoginUseCase=<EMAIL>>com.example.sharen.domain.usecase.customer.GetCustomersUseCase9com.example.sharen.domain.usecase.customer.CustomerFilter=<EMAIL>@<EMAIL>>com.example.sharen.domain.usecase.invoice.CreateInvoiceUseCaseFcom.example.sharen.domain.usecase.invoice.GetInvoicesByCustomerUseCase>com.example.sharen.domain.usecase.payment.CreatePaymentUseCaseFcom.example.sharen.domain.usecase.payment.GetPaymentsByCustomerUseCase;com.example.sharen.domain.usecase.product.AddProductUseCase?com.example.sharen.domain.usecase.product.GetAllProductsUseCaseDcom.example.sharen.domain.usecase.product.GetLowStockProductsUseCase?com.example.sharen.domain.usecase.product.GetProductByIdUseCase?com.example.sharen.domain.usecase.product.SearchProductsUseCaseCcom.example.sharen.domain.usecase.product.UpdateProductStockUseCase>com.example.sharen.presentation.ui.auth.ForgotPasswordActivity5com.example.sharen.presentation.ui.auth.LoginActivity8com.example.sharen.presentation.ui.auth.RegisterActivity<com.example.sharen.presentation.ui.customer.CustomerActivityFcom.example.sharen.presentation.ui.customer.CustomerActivity.CompanionCcom.example.sharen.presentation.ui.customer.adapter.CustomerAdapterVcom.example.sharen.presentation.ui.customer.adapter.CustomerAdapter.CustomerViewHolderXcom.example.sharen.presentation.ui.customer.adapter.CustomerAdapter.CustomerDiffCallback>com.example.sharen.presentation.ui.dashboard.DashboardActivity;com.example.sharen.presentation.ui.dashboard.DashboardStats7com.example.sharen.presentation.viewmodel.AuthViewModel4com.example.sharen.presentation.viewmodel.LoginState9com.example.sharen.presentation.viewmodel.LoginState.Idle<com.example.sharen.presentation.viewmodel.LoginState.Loading<com.example.sharen.presentation.viewmodel.LoginState.Success:com.example.sharen.presentation.viewmodel.LoginState.Error;com.example.sharen.presentation.viewmodel.CustomerViewModel<com.example.sharen.presentation.viewmodel.DashboardViewModel2com.example.sharen.ui.admin.UserManagementActivity3com.example.sharen.ui.admin.UserManagementViewModel(com.example.sharen.ui.auth.AuthViewModel1com.example.sharen.ui.auth.ForgotPasswordActivity(com.example.sharen.ui.auth.LoginActivity+com.example.sharen.ui.auth.RegisterActivity)<EMAIL>=com.example.sharen.ui.customer.CustomerFormActivity.Companion4com.example.sharen.ui.customer.CustomerFormViewModel3com.example.sharen.ui.customer.CustomerListActivity=com.example.sharen.ui.customer.CustomerListActivity.Companion4com.example.sharen.ui.customer.CustomerListViewModel1com.example.sharen.ui.dashboard.DashboardActivity2com.example.sharen.ui.dashboard.DashboardViewModel2com.example.sharen.ui.dashboard.TransactionAdapterHcom.example.sharen.ui.dashboard.TransactionAdapter.TransactionViewHolderJcom.example.sharen.ui.dashboard.TransactionAdapter.TransactionDiffCallback;com.example.sharen.ui.installment.InstallmentDetailFragment9com.example.sharen.ui.installment.InstallmentEditFragment9com.example.sharen.ui.installment.InstallmentListFragment6com.example.sharen.ui.installment.InstallmentViewModel<com.example.sharen.ui.installment.adapter.InstallmentAdapterRcom.example.sharen.ui.installment.adapter.InstallmentAdapter.InstallmentViewHolderTcom.example.sharen.ui.installment.adapter.InstallmentAdapter.InstallmentDiffCallback,com.example.sharen.ui.invoice.InvoiceAdapter><EMAIL>>com.example.sharen.ui.invoice.InvoiceDetailsActivity.Companion5com.example.sharen.ui.invoice.InvoiceDetailsViewModel0com.example.sharen.ui.invoice.InvoiceItemAdapterFcom.example.sharen.ui.invoice.InvoiceItemAdapter.InvoiceItemViewHolderHcom.example.sharen.ui.invoice.InvoiceItemAdapter.InvoiceItemDiffCallback1com.example.sharen.ui.invoice.InvoiceListActivity2com.example.sharen.ui.invoice.InvoiceListViewModel,com.example.sharen.ui.invoice.PaymentAdapter><EMAIL><com.example.sharen.ui.invoice.SalesInvoiceActivity.Companion5com.example.sharen.ui.invoice.SalesInvoiceItemAdapterKcom.example.sharen.ui.invoice.SalesInvoiceItemAdapter.InvoiceItemViewHolderMcom.example.sharen.ui.invoice.SalesInvoiceItemAdapter.InvoiceItemDiffCallback3com.example.sharen.ui.invoice.SalesInvoiceViewModel6com.example.sharen.ui.notification.NotificationAdapterMcom.example.sharen.ui.notification.NotificationAdapter.NotificationViewHolderOcom.example.sharen.ui.notification.NotificationAdapter.NotificationDiffCallback0com.example.sharen.ui.payment.AddPaymentFragment1com.example.sharen.ui.payment.AddPaymentViewModel-com.example.sharen.ui.payment.AddPaymentState5com.example.sharen.ui.payment.AddPaymentState.Initial4com.example.sharen.ui.payment.AddPaymentState.Saving3com.example.sharen.ui.payment.AddPaymentState.Saved3com.example.sharen.ui.payment.AddPaymentState.Error0com.example.sharen.ui.payment.InstallmentAdapterFcom.example.sharen.ui.payment.InstallmentAdapter.InstallmentViewHolderHcom.example.sharen.ui.payment.InstallmentAdapter.InstallmentDiffCallback3com.example.sharen.ui.payment.InstallmentFormDialog-com.example.sharen.ui.payment.PaymentActivity,com.example.sharen.ui.payment.PaymentAdapter><EMAIL>,com.example.sharen.ui.product.ProductAdapter><EMAIL>>com.example.sharen.ui.product.ProductDetailsActivity.Companion5com.example.sharen.ui.product.ProductDetailsViewModel1com.example.sharen.ui.product.ProductFormActivity;com.example.sharen.ui.product.ProductFormActivity.Companion2com.example.sharen.ui.product.ProductFormViewModel1com.example.sharen.ui.product.ProductListActivity2com.example.sharen.ui.product.ProductListViewModel1com.example.sharen.ui.product.ProductTestActivity5com.example.sharen.ui.profile.DisplaySettingsActivity6com.example.sharen.ui.profile.DisplaySettingsViewModel:com.example.sharen.ui.profile.NotificationSettingsActivity;com.example.sharen.ui.profile.NotificationSettingsViewModel4com.example.sharen.ui.profile.PasswordChangeActivity5com.example.sharen.ui.profile.PasswordChangeViewModel-com.example.sharen.ui.profile.ProfileActivity.com.example.sharen.ui.profile.ProfileViewModel.com.example.sharen.ui.profile.SecurityActivity/com.example.sharen.ui.profile.SecurityViewModel+com.example.sharen.ui.report.ReportActivity,com.example.sharen.ui.report.ReportViewModel/com.example.sharen.ui.settings.SettingsActivity0com.example.sharen.ui.settings.SettingsViewModel!com.example.sharen.util.DateUtils"com.example.sharen.util.ImageUtils#com.example.sharen.util.NumberUtils1com.example.sharen.databinding.ItemInvoiceBinding1com.example.sharen.databinding.ItemPaymentBinding8com.example.sharen.databinding.ItemInvoiceProductBinding1com.example.sharen.databinding.ItemProductBinding2com.example.sharen.databinding.ItemCustomerBinding5com.example.sharen.databinding.ItemTransactionBinding6com.example.sharen.databinding.ItemNotificationBinding;com.example.sharen.ui.payment.PaymentListFragmentDirections5com.example.sharen.databinding.ItemInstallmentBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   