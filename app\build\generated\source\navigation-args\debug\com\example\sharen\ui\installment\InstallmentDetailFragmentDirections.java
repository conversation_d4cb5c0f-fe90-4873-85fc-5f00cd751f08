package com.example.sharen.ui.installment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.navigation.NavDirections;
import com.example.sharen.R;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class InstallmentDetailFragmentDirections {
  private InstallmentDetailFragmentDirections() {
  }

  @NonNull
  public static ActionInstallmentDetailToInstallmentEdit actionInstallmentDetailToInstallmentEdit(
      ) {
    return new ActionInstallmentDetailToInstallmentEdit();
  }

  public static class ActionInstallmentDetailToInstallmentEdit implements NavDirections {
    private final HashMap arguments = new HashMap();

    private ActionInstallmentDetailToInstallmentEdit() {
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionInstallmentDetailToInstallmentEdit setInstallmentId(long installmentId) {
      this.arguments.put("installmentId", installmentId);
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    @NonNull
    public Bundle getArguments() {
      Bundle __result = new Bundle();
      if (arguments.containsKey("installmentId")) {
        long installmentId = (long) arguments.get("installmentId");
        __result.putLong("installmentId", installmentId);
      } else {
        __result.putLong("installmentId", 0L);
      }
      return __result;
    }

    @Override
    public int getActionId() {
      return R.id.action_installmentDetail_to_installmentEdit;
    }

    @SuppressWarnings("unchecked")
    public long getInstallmentId() {
      return (long) arguments.get("installmentId");
    }

    @Override
    public boolean equals(Object object) {
      if (this == object) {
          return true;
      }
      if (object == null || getClass() != object.getClass()) {
          return false;
      }
      ActionInstallmentDetailToInstallmentEdit that = (ActionInstallmentDetailToInstallmentEdit) object;
      if (arguments.containsKey("installmentId") != that.arguments.containsKey("installmentId")) {
        return false;
      }
      if (getInstallmentId() != that.getInstallmentId()) {
        return false;
      }
      if (getActionId() != that.getActionId()) {
        return false;
      }
      return true;
    }

    @Override
    public int hashCode() {
      int result = 1;
      result = 31 * result + (int)(getInstallmentId() ^ (getInstallmentId() >>> 32));
      result = 31 * result + getActionId();
      return result;
    }

    @Override
    public String toString() {
      return "ActionInstallmentDetailToInstallmentEdit(actionId=" + getActionId() + "){"
          + "installmentId=" + getInstallmentId()
          + "}";
    }
  }
}
