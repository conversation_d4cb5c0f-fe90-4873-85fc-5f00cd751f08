package com.example.sharen.domain.model;

/**
 * نقش‌های کاربری
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u0000 \u000f2\b\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u000fB\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u0010"}, d2 = {"Lcom/example/sharen/domain/model/UserRole;", "", "displayName", "", "priority", "", "(Ljava/lang/String;ILjava/lang/String;I)V", "getDisplayName", "()Ljava/lang/String;", "getPriority", "()I", "ADMIN", "MANAGER", "SELLER", "CUSTOMER", "Companion", "app_debug"})
public enum UserRole {
    /*public static final*/ ADMIN /* = new ADMIN(null, 0) */,
    /*public static final*/ MANAGER /* = new MANAGER(null, 0) */,
    /*public static final*/ SELLER /* = new SELLER(null, 0) */,
    /*public static final*/ CUSTOMER /* = new CUSTOMER(null, 0) */;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String displayName = null;
    private final int priority = 0;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.domain.model.UserRole.Companion Companion = null;
    
    UserRole(java.lang.String displayName, int priority) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final int getPriority() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.example.sharen.domain.model.UserRole> getEntries() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/sharen/domain/model/UserRole$Companion;", "", "()V", "fromString", "Lcom/example/sharen/domain/model/UserRole;", "role", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.sharen.domain.model.UserRole fromString(@org.jetbrains.annotations.NotNull
        java.lang.String role) {
            return null;
        }
    }
}