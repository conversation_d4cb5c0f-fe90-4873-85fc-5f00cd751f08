package com.example.sharen.ui.payment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J.\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0014R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\bR\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\u0016"}, d2 = {"Lcom/example/sharen/ui/payment/AddPaymentViewModel;", "Landroidx/lifecycle/ViewModel;", "paymentRepository", "error/NonExistentClass", "(Lerror/NonExistentClass;)V", "_state", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/sharen/ui/payment/AddPaymentState;", "Lerror/NonExistentClass;", "state", "Lkotlinx/coroutines/flow/StateFlow;", "getState", "()Lkotlinx/coroutines/flow/StateFlow;", "savePayment", "", "amount", "", "method", "Lcom/example/sharen/data/model/PaymentMethod;", "referenceNumber", "", "notes", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class AddPaymentViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final error.NonExistentClass paymentRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharen.ui.payment.AddPaymentState> _state = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.example.sharen.ui.payment.AddPaymentState> state = null;
    
    @javax.inject.Inject
    public AddPaymentViewModel(@org.jetbrains.annotations.NotNull
    error.NonExistentClass paymentRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.example.sharen.ui.payment.AddPaymentState> getState() {
        return null;
    }
    
    public final void savePayment(double amount, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentMethod method, @org.jetbrains.annotations.Nullable
    java.lang.String referenceNumber, @org.jetbrains.annotations.Nullable
    java.lang.String notes) {
    }
}