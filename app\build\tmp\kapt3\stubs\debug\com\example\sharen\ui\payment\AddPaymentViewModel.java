package com.example.sharen.ui.payment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J.\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0013R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0015"}, d2 = {"Lcom/example/sharen/ui/payment/AddPaymentViewModel;", "Landroidx/lifecycle/ViewModel;", "paymentRepository", "Lcom/example/sharen/data/repository/PaymentRepository;", "(Lcom/example/sharen/data/repository/PaymentRepository;)V", "_state", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/sharen/ui/payment/AddPaymentState;", "state", "Lkotlinx/coroutines/flow/StateFlow;", "getState", "()Lkotlinx/coroutines/flow/StateFlow;", "savePayment", "", "amount", "", "method", "Lcom/example/sharen/data/model/PaymentMethod;", "referenceNumber", "", "notes", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class AddPaymentViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.repository.PaymentRepository paymentRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharen.ui.payment.AddPaymentState> _state = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.example.sharen.ui.payment.AddPaymentState> state = null;
    
    @javax.inject.Inject
    public AddPaymentViewModel(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.repository.PaymentRepository paymentRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.example.sharen.ui.payment.AddPaymentState> getState() {
        return null;
    }
    
    public final void savePayment(double amount, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentMethod method, @org.jetbrains.annotations.Nullable
    java.lang.String referenceNumber, @org.jetbrains.annotations.Nullable
    java.lang.String notes) {
    }
}