package com.example.sharen.data.repository;

/**
 * پیاده‌سازی Repository برای مدیریت محصولات
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\u0007H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\t\u0010\nJ2\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00062\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J*\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\f0\u00062\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u0015J\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u0017H\u0016J\u0014\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u0017H\u0016J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010\u001b\u001a\u00020\u0010H\u0016J\u0014\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u0017H\u0016J\u0014\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u0017H\u0016J\u001c\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010\u001f\u001a\u00020\u0010H\u0016J\u0014\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u0017H\u0016J,\u0010!\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u00062\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\"\u0010\u0015J\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010$\u001a\u00020\u000eH\u0016J\u001c\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010&\u001a\u00020\u000eH\u0016J\u001c\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010(\u001a\u00020)H\u0016J$\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020,H\u0016J\u001c\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u0010/\u001a\u000200H\u0016J\u001c\u00101\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u00102\u001a\u000203H\u0016J2\u00104\u001a\b\u0012\u0004\u0012\u00020\f0\u00062\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b5\u0010\u0012J\u001c\u00106\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00180\u00172\u0006\u00107\u001a\u00020\u000eH\u0016J*\u00108\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\u0007H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b9\u0010\nJ2\u0010:\u001a\b\u0012\u0004\u0012\u00020\f0\u00062\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010;\u001a\u00020\u0010H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b<\u0010\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006="}, d2 = {"Lcom/example/sharen/data/repository/ProductRepositoryImpl;", "Lcom/example/sharen/domain/repository/ProductRepository;", "productDao", "Lcom/example/sharen/data/local/dao/ProductDao;", "(Lcom/example/sharen/data/local/dao/ProductDao;)V", "addProduct", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Product;", "product", "addProduct-gIAlu-s", "(Lcom/example/sharen/domain/model/Product;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "decreaseStock", "", "productId", "", "quantity", "", "decreaseStock-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProduct", "deleteProduct-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveProducts", "Lkotlinx/coroutines/flow/Flow;", "", "getAllProducts", "getBestSellingProducts", "limit", "getInactiveProducts", "getLowStockProducts", "getNewProducts", "daysSinceAdded", "getOutOfStockProducts", "getProductById", "getProductById-gIAlu-s", "getProductsByBrand", "brandId", "getProductsByCategory", "categoryId", "getProductsByGender", "gender", "Lcom/example/sharen/domain/model/Gender;", "getProductsByPriceRange", "minPrice", "", "maxPrice", "getProductsBySeason", "season", "Lcom/example/sharen/domain/model/Season;", "getProductsByStockStatus", "inStock", "", "increaseStock", "increaseStock-0E7RQCE", "searchProducts", "query", "updateProduct", "updateProduct-gIAlu-s", "updateProductStock", "newStock", "updateProductStock-0E7RQCE", "app_debug"})
public final class ProductRepositoryImpl implements com.example.sharen.domain.repository.ProductRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.ProductDao productDao = null;
    
    @javax.inject.Inject
    public ProductRepositoryImpl(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.ProductDao productDao) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getAllProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByBrand(@org.jetbrains.annotations.NotNull
    java.lang.String brandId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> searchProducts(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByStockStatus(boolean inStock) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getLowStockProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getOutOfStockProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByPriceRange(long minPrice, long maxPrice) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getActiveProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getInactiveProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsBySeason(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.Season season) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByGender(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.Gender gender) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getBestSellingProducts(int limit) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getNewProducts(int daysSinceAdded) {
        return null;
    }
}