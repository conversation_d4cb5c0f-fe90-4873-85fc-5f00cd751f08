package com.example.sharen.data.repository;

/**
 * پیاده‌سازی رپوزیتوری محصولات با داده‌های آزمایشی
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u000e\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u00072\u0006\u0010\b\u001a\u00020\u0005H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\t\u0010\nJ*\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00072\u0006\u0010\r\u001a\u00020\u000eH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00130\u0012H\u0016J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J\u0014\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00130\u0012H\u0016J\u0016\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00050\u00122\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u000e\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00170\u0012H\u0016J\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00130\u00122\u0006\u0010\u001c\u001a\u00020\u000eH\u0016J\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00130\u00122\u0006\u0010\u001e\u001a\u00020\u000eH\u0016J*\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00050\u00072\u0006\u0010\b\u001a\u00020\u0005H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b \u0010\nJ2\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00050\u00072\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\"\u001a\u00020\u0017H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b#\u0010$R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006%"}, d2 = {"Lcom/example/sharen/data/repository/ProductRepositoryImpl;", "Lcom/example/sharen/data/repository/ProductRepository;", "()V", "mockProducts", "", "Lcom/example/sharen/data/model/Product;", "createProduct", "Lkotlin/Result;", "product", "createProduct-gIAlu-s", "(Lcom/example/sharen/data/model/Product;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProduct", "", "productId", "", "deleteProduct-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllProducts", "Lkotlinx/coroutines/flow/Flow;", "", "getDateBefore", "Ljava/util/Date;", "days", "", "getLowStockProducts", "getProductById", "getProductCount", "getProductsByCategory", "category", "searchProducts", "query", "updateProduct", "updateProduct-gIAlu-s", "updateProductStock", "newStock", "updateProductStock-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class ProductRepositoryImpl implements com.example.sharen.data.repository.ProductRepository {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.example.sharen.data.model.Product> mockProducts = null;
    
    @javax.inject.Inject
    public ProductRepositoryImpl() {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> getAllProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Product> getProductById(@org.jetbrains.annotations.NotNull
    java.lang.String productId) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> searchProducts(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.lang.Integer> getProductCount() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> getLowStockProducts() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Product>> getProductsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category) {
        return null;
    }
    
    private final java.util.Date getDateBefore(int days) {
        return null;
    }
}