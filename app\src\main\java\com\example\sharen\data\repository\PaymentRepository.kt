package com.example.sharen.data.repository

import com.example.sharen.data.local.dao.PaymentDao
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentStatus
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Singleton

@Singleton
abstract class PaymentRepository {
    abstract val paymentDao: PaymentDao

    fun getPayments(): Flow<List<Payment>> = paymentDao.getAllPayments()

    fun getPaymentById(id: String): Flow<Payment?> = paymentDao.getPaymentById(id)

    fun getPaymentsByCustomerId(customerId: String): Flow<List<Payment>> =
        paymentDao.getPaymentsByCustomerId(customerId)

    fun getPaymentsByOrderId(orderId: String): Flow<List<Payment>> =
        paymentDao.getPaymentsByOrderId(orderId)

    suspend fun insertPayment(payment: Payment) = paymentDao.insertPayment(payment)

    suspend fun updatePayment(payment: Payment) = paymentDao.updatePayment(payment)

    suspend fun deletePayment(payment: Payment) = paymentDao.deletePayment(payment)

    abstract suspend fun createPayment(payment: Payment): Result<Payment>

    abstract suspend fun deletePayment(paymentId: String): Result<Unit>

    abstract suspend fun getPayment(paymentId: String): Result<Payment>

    abstract fun getAllPayments(): Flow<List<Payment>>

    abstract fun getPaymentsByDateRange(startDate: Date, endDate: Date): Flow<List<Payment>>

    abstract fun getPaymentsByStatus(status: PaymentStatus): Flow<List<Payment>>

    abstract suspend fun confirmPayment(paymentId: String): Result<Payment>

    abstract suspend fun rejectPayment(paymentId: String, reason: String): Result<Payment>

    abstract suspend fun getTotalPaymentsByDateRange(startDate: Date, endDate: Date): Result<Double>

    abstract suspend fun getCustomerTotalPayments(customerId: String): Result<Double>

    abstract suspend fun getPaymentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>>
} 