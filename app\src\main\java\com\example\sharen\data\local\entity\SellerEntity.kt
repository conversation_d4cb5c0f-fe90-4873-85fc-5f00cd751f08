package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.sharen.data.model.Seller
import java.util.Date

@Entity(tableName = "sellers")
data class SellerEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val name: String,
    val phone: String,
    val address: String?,
    val email: String?,
    val totalSales: Long,
    val totalCommission: Long,
    val totalDebt: Long,
    val commissionRate: Long,
    val isActive: Boolean,
    val bankAccount: String?,
    val bankName: String?,
    val bankSheba: String?,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toSeller(): Seller = Seller(
        id = id,
        userId = userId,
        totalSales = totalSales,
        totalCommission = totalCommission,
        totalDebt = totalDebt,
        commissionRate = commissionRate,
        isActive = isActive,
        bankAccount = bankAccount,
        bankName = bankName,
        bankSheba = bankSheba
    )
    
    companion object {
        fun fromSeller(seller: Seller): SellerEntity = SellerEntity(
            id = seller.id,
            userId = seller.userId,
            name = "", // TODO: Get from User entity
            phone = "", // TODO: Get from User entity
            address = null, // TODO: Get from User entity
            email = null, // TODO: Get from User entity
            totalSales = seller.totalSales,
            totalCommission = seller.totalCommission,
            totalDebt = seller.totalDebt,
            commissionRate = seller.commissionRate,
            isActive = seller.isActive,
            bankAccount = seller.bankAccount,
            bankName = seller.bankName,
            bankSheba = seller.bankSheba,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
} 