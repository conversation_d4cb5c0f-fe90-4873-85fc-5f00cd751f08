<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_settings_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="360" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="17" endOffset="43"/></Target><Target id="@+id/layoutDisplaySettings" view="LinearLayout"><Expressions/><location startLine="61" startOffset="20" endLine="92" endOffset="34"/></Target><Target id="@+id/layoutNotificationSettings" view="LinearLayout"><Expressions/><location startLine="124" startOffset="20" endLine="155" endOffset="34"/></Target><Target id="@+id/layoutSecuritySettings" view="LinearLayout"><Expressions/><location startLine="187" startOffset="20" endLine="218" endOffset="34"/></Target><Target id="@+id/layoutBackup" view="LinearLayout"><Expressions/><location startLine="250" startOffset="20" endLine="281" endOffset="34"/></Target><Target id="@+id/layoutRestore" view="LinearLayout"><Expressions/><location startLine="313" startOffset="20" endLine="344" endOffset="34"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="350" startOffset="4" endLine="358" endOffset="51"/></Target></Targets></Layout>