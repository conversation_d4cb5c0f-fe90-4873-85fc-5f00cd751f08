package com.example.sharen.ui.admin;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0012\u0010\u0011\u001a\u00020\u000e2\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u0014J\b\u0010\u0014\u001a\u00020\u0015H\u0016J\b\u0010\u0016\u001a\u00020\u000eH\u0002J\b\u0010\u0017\u001a\u00020\u000eH\u0002J\b\u0010\u0018\u001a\u00020\u000eH\u0002J\b\u0010\u0019\u001a\u00020\u000eH\u0002J\u0010\u0010\u001a\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0010\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0015\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u0004H\u0002\u00a2\u0006\u0002\u0010\u001eR\u0010\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0004\n\u0002\u0010\u0005R\u0010\u0010\u0006\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0004\n\u0002\u0010\u0005R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\n\u00a8\u0006\u001f"}, d2 = {"Lcom/example/sharen/ui/admin/UserManagementActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "adapter", "error/NonExistentClass", "Lerror/NonExistentClass;", "binding", "viewModel", "Lcom/example/sharen/ui/admin/UserManagementViewModel;", "getViewModel", "()Lcom/example/sharen/ui/admin/UserManagementViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "approveUser", "", "userId", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onSupportNavigateUp", "", "setupObservers", "setupToolbar", "setupUI", "showAddUserDialog", "showDeleteConfirmation", "showRoleSelectionDialog", "showUserDetails", "user", "(Lerror/NonExistentClass;)V", "app_debug"})
public final class UserManagementActivity extends androidx.appcompat.app.AppCompatActivity {
    private error.NonExistentClass binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private error.NonExistentClass adapter;
    
    public UserManagementActivity() {
        super();
    }
    
    private final com.example.sharen.ui.admin.UserManagementViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupUI() {
    }
    
    private final void setupObservers() {
    }
    
    private final void showUserDetails(error.NonExistentClass user) {
    }
    
    private final void approveUser(java.lang.String userId) {
    }
    
    private final void showDeleteConfirmation(java.lang.String userId) {
    }
    
    private final void showRoleSelectionDialog(java.lang.String userId) {
    }
    
    private final void showAddUserDialog() {
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
}