package com.example.sharen.presentation.ui.customer

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.example.sharen.R
import com.example.sharen.databinding.ActivityCustomerFormBinding
import com.example.sharen.domain.model.Customer
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class CustomerFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCustomerFormBinding
    private val viewModel: CustomerFormViewModel by viewModels()
    private var isEditMode = false

    companion object {
        private const val EXTRA_CUSTOMER_ID = "customer_id"

        fun newIntent(context: Context, customerId: String? = null): Intent {
            return Intent(context, CustomerFormActivity::class.java).apply {
                customerId?.let { putExtra(EXTRA_CUSTOMER_ID, it) }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCustomerFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val customerId = intent.getStringExtra(EXTRA_CUSTOMER_ID)
        isEditMode = customerId != null

        setupToolbar()
        setupClickListeners()
        observeState()

        if (isEditMode && customerId != null) {
            viewModel.loadCustomer(customerId)
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(
            if (isEditMode) R.string.edit_customer else R.string.add_customer
        )
    }

    private fun setupClickListeners() {
        binding.saveButton.setOnClickListener {
            saveCustomer()
        }
    }

    private fun saveCustomer() {
        val name = binding.nameEditText.text.toString().trim()
        val phone = binding.phoneEditText.text.toString().trim()
        val email = binding.emailEditText.text.toString().trim().takeIf { it.isNotEmpty() }
        val address = binding.addressEditText.text.toString().trim().takeIf { it.isNotEmpty() }

        // Validation
        if (name.isEmpty()) {
            binding.nameEditText.error = getString(R.string.required_field)
            return
        }

        if (phone.isEmpty()) {
            binding.phoneEditText.error = getString(R.string.required_field)
            return
        }

        if (isEditMode) {
            viewModel.updateCustomer(name, phone, email, address)
        } else {
            viewModel.createCustomer(name, phone, email, address)
        }
    }

    private fun observeState() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.state.collect { state ->
                    when (state) {
                        is CustomerFormState.Loading -> {
                            binding.saveButton.isEnabled = false
                            binding.progressBar.visibility = android.view.View.VISIBLE
                        }
                        is CustomerFormState.CustomerLoaded -> {
                            binding.saveButton.isEnabled = true
                            binding.progressBar.visibility = android.view.View.GONE
                            bindCustomerData(state.customer)
                        }
                        is CustomerFormState.Saved -> {
                            setResult(RESULT_OK)
                            finish()
                        }
                        is CustomerFormState.Error -> {
                            binding.saveButton.isEnabled = true
                            binding.progressBar.visibility = android.view.View.GONE
                            Snackbar.make(
                                binding.root,
                                state.message,
                                Snackbar.LENGTH_LONG
                            ).show()
                        }
                        else -> {
                            binding.saveButton.isEnabled = true
                            binding.progressBar.visibility = android.view.View.GONE
                        }
                    }
                }
            }
        }
    }

    private fun bindCustomerData(customer: Customer) {
        binding.apply {
            nameEditText.setText(customer.name)
            phoneEditText.setText(customer.phone)
            emailEditText.setText(customer.email ?: "")
            addressEditText.setText(customer.address ?: "")
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}

// ViewModel State
sealed class CustomerFormState {
    object Initial : CustomerFormState()
    object Loading : CustomerFormState()
    data class CustomerLoaded(val customer: Customer) : CustomerFormState()
    object Saved : CustomerFormState()
    data class Error(val message: String) : CustomerFormState()
}
