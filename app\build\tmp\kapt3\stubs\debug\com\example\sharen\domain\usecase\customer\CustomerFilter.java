package com.example.sharen.domain.usecase.customer;

/**
 * فیلترهای مشتری
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\b\u0003\u0004\u0005\u0006\u0007\b\t\nB\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\b\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u00a8\u0006\u0013"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "", "()V", "Active", "All", "BySeller", "Inactive", "LowCredit", "Search", "WithDebt", "WithoutDebt", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$Active;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$All;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$BySeller;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$Inactive;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$LowCredit;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$Search;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$WithDebt;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter$WithoutDebt;", "app_debug"})
public abstract class CustomerFilter {
    
    private CustomerFilter() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$Active;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "()V", "app_debug"})
    public static final class Active extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        public static final com.example.sharen.domain.usecase.customer.CustomerFilter.Active INSTANCE = null;
        
        private Active() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$All;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "()V", "app_debug"})
    public static final class All extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        public static final com.example.sharen.domain.usecase.customer.CustomerFilter.All INSTANCE = null;
        
        private All() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$BySeller;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "sellerId", "", "(Ljava/lang/String;)V", "getSellerId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class BySeller extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String sellerId = null;
        
        public BySeller(@org.jetbrains.annotations.NotNull
        java.lang.String sellerId) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getSellerId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.sharen.domain.usecase.customer.CustomerFilter.BySeller copy(@org.jetbrains.annotations.NotNull
        java.lang.String sellerId) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$Inactive;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "daysSinceLastPurchase", "", "(I)V", "getDaysSinceLastPurchase", "()I", "component1", "copy", "equals", "", "other", "", "hashCode", "toString", "", "app_debug"})
    public static final class Inactive extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        private final int daysSinceLastPurchase = 0;
        
        public Inactive(int daysSinceLastPurchase) {
        }
        
        public final int getDaysSinceLastPurchase() {
            return 0;
        }
        
        public Inactive() {
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.sharen.domain.usecase.customer.CustomerFilter.Inactive copy(int daysSinceLastPurchase) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$LowCredit;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "()V", "app_debug"})
    public static final class LowCredit extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        public static final com.example.sharen.domain.usecase.customer.CustomerFilter.LowCredit INSTANCE = null;
        
        private LowCredit() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$Search;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "query", "", "(Ljava/lang/String;)V", "getQuery", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class Search extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String query = null;
        
        public Search(@org.jetbrains.annotations.NotNull
        java.lang.String query) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getQuery() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.sharen.domain.usecase.customer.CustomerFilter.Search copy(@org.jetbrains.annotations.NotNull
        java.lang.String query) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$WithDebt;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "()V", "app_debug"})
    public static final class WithDebt extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        public static final com.example.sharen.domain.usecase.customer.CustomerFilter.WithDebt INSTANCE = null;
        
        private WithDebt() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/example/sharen/domain/usecase/customer/CustomerFilter$WithoutDebt;", "Lcom/example/sharen/domain/usecase/customer/CustomerFilter;", "()V", "app_debug"})
    public static final class WithoutDebt extends com.example.sharen.domain.usecase.customer.CustomerFilter {
        @org.jetbrains.annotations.NotNull
        public static final com.example.sharen.domain.usecase.customer.CustomerFilter.WithoutDebt INSTANCE = null;
        
        private WithoutDebt() {
        }
    }
}