<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_invoice_product" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_invoice_product.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_invoice_product_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="161" endOffset="35"/></Target><Target id="@+id/ivProduct" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="55"/></Target><Target id="@+id/tvProductName" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="34" endOffset="40"/></Target><Target id="@+id/tvProductCode" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="46" endOffset="36"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="48" startOffset="8" endLine="54" endOffset="66"/></Target><Target id="@+id/tvQuantity" view="TextView"><Expressions/><location startLine="78" startOffset="16" endLine="84" endOffset="36"/></Target><Target id="@+id/tvUnitPrice" view="TextView"><Expressions/><location startLine="101" startOffset="16" endLine="107" endOffset="48"/></Target><Target id="@+id/tvDiscount" view="TextView"><Expressions/><location startLine="124" startOffset="16" endLine="131" endOffset="47"/></Target><Target id="@+id/tvTotalPrice" view="TextView"><Expressions/><location startLine="148" startOffset="16" endLine="156" endOffset="48"/></Target></Targets></Layout>