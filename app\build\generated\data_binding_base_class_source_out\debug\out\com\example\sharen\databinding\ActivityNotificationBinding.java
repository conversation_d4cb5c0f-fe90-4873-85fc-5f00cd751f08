// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityNotificationBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final Button btnClearAll;

  @NonNull
  public final Chip chipAll;

  @NonNull
  public final ChipGroup chipGroup;

  @NonNull
  public final Chip chipInstallment;

  @NonNull
  public final Chip chipInvoice;

  @NonNull
  public final Chip chipPayment;

  @NonNull
  public final Chip chipSystem;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final LinearLayout statsLayout;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvEmptyState;

  @NonNull
  public final TextView tvUnreadCount;

  private ActivityNotificationBinding(@NonNull ConstraintLayout rootView,
      @NonNull Button btnClearAll, @NonNull Chip chipAll, @NonNull ChipGroup chipGroup,
      @NonNull Chip chipInstallment, @NonNull Chip chipInvoice, @NonNull Chip chipPayment,
      @NonNull Chip chipSystem, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerView, @NonNull LinearLayout statsLayout,
      @NonNull Toolbar toolbar, @NonNull TextView tvEmptyState, @NonNull TextView tvUnreadCount) {
    this.rootView = rootView;
    this.btnClearAll = btnClearAll;
    this.chipAll = chipAll;
    this.chipGroup = chipGroup;
    this.chipInstallment = chipInstallment;
    this.chipInvoice = chipInvoice;
    this.chipPayment = chipPayment;
    this.chipSystem = chipSystem;
    this.progressBar = progressBar;
    this.recyclerView = recyclerView;
    this.statsLayout = statsLayout;
    this.toolbar = toolbar;
    this.tvEmptyState = tvEmptyState;
    this.tvUnreadCount = tvUnreadCount;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnClearAll;
      Button btnClearAll = ViewBindings.findChildViewById(rootView, id);
      if (btnClearAll == null) {
        break missingId;
      }

      id = R.id.chipAll;
      Chip chipAll = ViewBindings.findChildViewById(rootView, id);
      if (chipAll == null) {
        break missingId;
      }

      id = R.id.chipGroup;
      ChipGroup chipGroup = ViewBindings.findChildViewById(rootView, id);
      if (chipGroup == null) {
        break missingId;
      }

      id = R.id.chipInstallment;
      Chip chipInstallment = ViewBindings.findChildViewById(rootView, id);
      if (chipInstallment == null) {
        break missingId;
      }

      id = R.id.chipInvoice;
      Chip chipInvoice = ViewBindings.findChildViewById(rootView, id);
      if (chipInvoice == null) {
        break missingId;
      }

      id = R.id.chipPayment;
      Chip chipPayment = ViewBindings.findChildViewById(rootView, id);
      if (chipPayment == null) {
        break missingId;
      }

      id = R.id.chipSystem;
      Chip chipSystem = ViewBindings.findChildViewById(rootView, id);
      if (chipSystem == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.statsLayout;
      LinearLayout statsLayout = ViewBindings.findChildViewById(rootView, id);
      if (statsLayout == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvEmptyState;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tvUnreadCount;
      TextView tvUnreadCount = ViewBindings.findChildViewById(rootView, id);
      if (tvUnreadCount == null) {
        break missingId;
      }

      return new ActivityNotificationBinding((ConstraintLayout) rootView, btnClearAll, chipAll,
          chipGroup, chipInstallment, chipInvoice, chipPayment, chipSystem, progressBar,
          recyclerView, statsLayout, toolbar, tvEmptyState, tvUnreadCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
