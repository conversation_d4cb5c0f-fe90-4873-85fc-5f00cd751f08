// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout layoutBackup;

  @NonNull
  public final LinearLayout layoutDisplaySettings;

  @NonNull
  public final LinearLayout layoutNotificationSettings;

  @NonNull
  public final LinearLayout layoutRestore;

  @NonNull
  public final LinearLayout layoutSecuritySettings;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final Toolbar toolbar;

  private ActivitySettingsBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout layoutBackup, @NonNull LinearLayout layoutDisplaySettings,
      @NonNull LinearLayout layoutNotificationSettings, @NonNull LinearLayout layoutRestore,
      @NonNull LinearLayout layoutSecuritySettings, @NonNull ProgressBar progressBar,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.layoutBackup = layoutBackup;
    this.layoutDisplaySettings = layoutDisplaySettings;
    this.layoutNotificationSettings = layoutNotificationSettings;
    this.layoutRestore = layoutRestore;
    this.layoutSecuritySettings = layoutSecuritySettings;
    this.progressBar = progressBar;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layoutBackup;
      LinearLayout layoutBackup = ViewBindings.findChildViewById(rootView, id);
      if (layoutBackup == null) {
        break missingId;
      }

      id = R.id.layoutDisplaySettings;
      LinearLayout layoutDisplaySettings = ViewBindings.findChildViewById(rootView, id);
      if (layoutDisplaySettings == null) {
        break missingId;
      }

      id = R.id.layoutNotificationSettings;
      LinearLayout layoutNotificationSettings = ViewBindings.findChildViewById(rootView, id);
      if (layoutNotificationSettings == null) {
        break missingId;
      }

      id = R.id.layoutRestore;
      LinearLayout layoutRestore = ViewBindings.findChildViewById(rootView, id);
      if (layoutRestore == null) {
        break missingId;
      }

      id = R.id.layoutSecuritySettings;
      LinearLayout layoutSecuritySettings = ViewBindings.findChildViewById(rootView, id);
      if (layoutSecuritySettings == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((ConstraintLayout) rootView, layoutBackup,
          layoutDisplaySettings, layoutNotificationSettings, layoutRestore, layoutSecuritySettings,
          progressBar, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
