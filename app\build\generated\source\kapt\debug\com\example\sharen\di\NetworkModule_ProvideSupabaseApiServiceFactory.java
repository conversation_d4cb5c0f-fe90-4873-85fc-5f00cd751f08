package com.example.sharen.di;

import com.example.sharen.data.remote.SupabaseApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideSupabaseApiServiceFactory implements Factory<SupabaseApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideSupabaseApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public SupabaseApiService get() {
    return provideSupabaseApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideSupabaseApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideSupabaseApiServiceFactory(retrofitProvider);
  }

  public static SupabaseApiService provideSupabaseApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideSupabaseApiService(retrofit));
  }
}
