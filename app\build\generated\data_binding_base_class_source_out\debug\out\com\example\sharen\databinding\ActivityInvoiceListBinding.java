// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.chip.Chip;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityInvoiceListBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final Chip chipAll;

  @NonNull
  public final Chip chipPaid;

  @NonNull
  public final Chip chipPartiallyPaid;

  @NonNull
  public final Chip chipPending;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final FloatingActionButton fabAddInvoice;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final SearchView searchView;

  @NonNull
  public final Toolbar toolbar;

  private ActivityInvoiceListBinding(@NonNull CoordinatorLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull Chip chipAll, @NonNull Chip chipPaid,
      @NonNull Chip chipPartiallyPaid, @NonNull Chip chipPending, @NonNull TextView emptyView,
      @NonNull FloatingActionButton fabAddInvoice, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerView, @NonNull SearchView searchView,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.chipAll = chipAll;
    this.chipPaid = chipPaid;
    this.chipPartiallyPaid = chipPartiallyPaid;
    this.chipPending = chipPending;
    this.emptyView = emptyView;
    this.fabAddInvoice = fabAddInvoice;
    this.progressBar = progressBar;
    this.recyclerView = recyclerView;
    this.searchView = searchView;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityInvoiceListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityInvoiceListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_invoice_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityInvoiceListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottomNavigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.chipAll;
      Chip chipAll = ViewBindings.findChildViewById(rootView, id);
      if (chipAll == null) {
        break missingId;
      }

      id = R.id.chipPaid;
      Chip chipPaid = ViewBindings.findChildViewById(rootView, id);
      if (chipPaid == null) {
        break missingId;
      }

      id = R.id.chipPartiallyPaid;
      Chip chipPartiallyPaid = ViewBindings.findChildViewById(rootView, id);
      if (chipPartiallyPaid == null) {
        break missingId;
      }

      id = R.id.chipPending;
      Chip chipPending = ViewBindings.findChildViewById(rootView, id);
      if (chipPending == null) {
        break missingId;
      }

      id = R.id.emptyView;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.fabAddInvoice;
      FloatingActionButton fabAddInvoice = ViewBindings.findChildViewById(rootView, id);
      if (fabAddInvoice == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.searchView;
      SearchView searchView = ViewBindings.findChildViewById(rootView, id);
      if (searchView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityInvoiceListBinding((CoordinatorLayout) rootView, bottomNavigation, chipAll,
          chipPaid, chipPartiallyPaid, chipPending, emptyView, fabAddInvoice, progressBar,
          recyclerView, searchView, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
