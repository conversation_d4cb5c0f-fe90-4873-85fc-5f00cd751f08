package com.example.sharen.data.local.entity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b8\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u0000 R2\u00020\u0001:\u0001RB\u00b9\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\f\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\n\u0012\u0006\u0010\u000e\u001a\u00020\n\u0012\u0006\u0010\u000f\u001a\u00020\n\u0012\u0006\u0010\u0010\u001a\u00020\u0003\u0012\u0006\u0010\u0011\u001a\u00020\u0003\u0012\b\u0010\u0012\u001a\u0004\u0018\u00010\n\u0012\b\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0014\u001a\u00020\u0015\u0012\u0006\u0010\u0016\u001a\u00020\n\u0012\u0006\u0010\u0017\u001a\u00020\n\u0012\b\u0010\u0018\u001a\u0004\u0018\u00010\n\u0012\b\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u001aJ\t\u00104\u001a\u00020\u0003H\u00c6\u0003J\t\u00105\u001a\u00020\nH\u00c6\u0003J\t\u00106\u001a\u00020\nH\u00c6\u0003J\t\u00107\u001a\u00020\nH\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\t\u00109\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010:\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001cJ\u000b\u0010;\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010<\u001a\u00020\u0015H\u00c6\u0003J\t\u0010=\u001a\u00020\nH\u00c6\u0003J\t\u0010>\u001a\u00020\nH\u00c6\u0003J\t\u0010?\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010@\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001cJ\u000b\u0010A\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010D\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010E\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010F\u001a\u00020\nH\u00c6\u0003J\t\u0010G\u001a\u00020\nH\u00c6\u0003J\t\u0010H\u001a\u00020\nH\u00c6\u0003J\u00ec\u0001\u0010I\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\n2\b\b\u0002\u0010\u000e\u001a\u00020\n2\b\b\u0002\u0010\u000f\u001a\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u00032\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\n2\b\b\u0002\u0010\u0017\u001a\u00020\n2\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u0010JJ\u0013\u0010K\u001a\u00020\u00152\b\u0010L\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010M\u001a\u00020NH\u00d6\u0001J\u0006\u0010O\u001a\u00020PJ\t\u0010Q\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\u0018\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u001d\u001a\u0004\b\u001b\u0010\u001cR\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0016\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001fR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001fR\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010!R\u0015\u0010\u0012\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u001d\u001a\u0004\b%\u0010\u001cR\u0011\u0010\r\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010!R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001fR\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010)R\u0013\u0010\u0013\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001fR\u0011\u0010\u000e\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010!R\u0011\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u001fR\u0011\u0010\u000f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010!R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\u001fR\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001fR\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u001fR\u0011\u0010\f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010!R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010!R\u0011\u0010\u0017\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010!\u00a8\u0006S"}, d2 = {"Lcom/example/sharen/data/local/entity/InvoiceEntity;", "", "id", "", "invoiceNumber", "customerId", "customerName", "sellerId", "sellerName", "totalAmount", "", "discount", "tax", "finalAmount", "paidAmount", "remainingAmount", "status", "paymentType", "dueDate", "notes", "isDeleted", "", "createdAt", "updatedAt", "approvedAt", "approvedBy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JJJJJJLjava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;ZJJLjava/lang/Long;Ljava/lang/String;)V", "getApprovedAt", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getApprovedBy", "()Ljava/lang/String;", "getCreatedAt", "()J", "getCustomerId", "getCustomerName", "getDiscount", "getDueDate", "getFinalAmount", "getId", "getInvoiceNumber", "()Z", "getNotes", "getPaidAmount", "getPaymentType", "getRemainingAmount", "getSellerId", "getSellerName", "getStatus", "getTax", "getTotalAmount", "getUpdatedAt", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JJJJJJLjava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;ZJJLjava/lang/Long;Ljava/lang/String;)Lcom/example/sharen/data/local/entity/InvoiceEntity;", "equals", "other", "hashCode", "", "toDomainModel", "Lcom/example/sharen/domain/model/Invoice;", "toString", "Companion", "app_debug"})
@androidx.room.Entity(tableName = "invoices", foreignKeys = {@androidx.room.ForeignKey(entity = com.example.sharen.data.local.entity.CustomerEntity.class, parentColumns = {"id"}, childColumns = {"customerId"}, onDelete = 5), @androidx.room.ForeignKey(entity = com.example.sharen.data.local.entity.UserEntity.class, parentColumns = {"id"}, childColumns = {"sellerId"}, onDelete = 5), @androidx.room.ForeignKey(entity = com.example.sharen.data.local.entity.UserEntity.class, parentColumns = {"id"}, childColumns = {"approvedBy"}, onDelete = 3)}, indices = {@androidx.room.Index(value = {"customerId"}), @androidx.room.Index(value = {"sellerId"}), @androidx.room.Index(value = {"approvedBy"})})
public final class InvoiceEntity {
    @androidx.room.PrimaryKey
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String invoiceNumber = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String customerId = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String customerName = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sellerId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sellerName = null;
    private final long totalAmount = 0L;
    private final long discount = 0L;
    private final long tax = 0L;
    private final long finalAmount = 0L;
    private final long paidAmount = 0L;
    private final long remainingAmount = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String status = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String paymentType = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long dueDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    private final boolean isDeleted = false;
    private final long createdAt = 0L;
    private final long updatedAt = 0L;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long approvedAt = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String approvedBy = null;
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.data.local.entity.InvoiceEntity.Companion Companion = null;
    
    public InvoiceEntity(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName, @org.jetbrains.annotations.Nullable
    java.lang.String sellerId, @org.jetbrains.annotations.Nullable
    java.lang.String sellerName, long totalAmount, long discount, long tax, long finalAmount, long paidAmount, long remainingAmount, @org.jetbrains.annotations.NotNull
    java.lang.String status, @org.jetbrains.annotations.NotNull
    java.lang.String paymentType, @org.jetbrains.annotations.Nullable
    java.lang.Long dueDate, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean isDeleted, long createdAt, long updatedAt, @org.jetbrains.annotations.Nullable
    java.lang.Long approvedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getInvoiceNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCustomerId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCustomerName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSellerId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSellerName() {
        return null;
    }
    
    public final long getTotalAmount() {
        return 0L;
    }
    
    public final long getDiscount() {
        return 0L;
    }
    
    public final long getTax() {
        return 0L;
    }
    
    public final long getFinalAmount() {
        return 0L;
    }
    
    public final long getPaidAmount() {
        return 0L;
    }
    
    public final long getRemainingAmount() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPaymentType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getDueDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long getUpdatedAt() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getApprovedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getApprovedBy() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.Invoice toDomainModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component10() {
        return 0L;
    }
    
    public final long component11() {
        return 0L;
    }
    
    public final long component12() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final long component18() {
        return 0L;
    }
    
    public final long component19() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component6() {
        return null;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.local.entity.InvoiceEntity copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName, @org.jetbrains.annotations.Nullable
    java.lang.String sellerId, @org.jetbrains.annotations.Nullable
    java.lang.String sellerName, long totalAmount, long discount, long tax, long finalAmount, long paidAmount, long remainingAmount, @org.jetbrains.annotations.NotNull
    java.lang.String status, @org.jetbrains.annotations.NotNull
    java.lang.String paymentType, @org.jetbrains.annotations.Nullable
    java.lang.Long dueDate, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean isDeleted, long createdAt, long updatedAt, @org.jetbrains.annotations.Nullable
    java.lang.Long approvedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/sharen/data/local/entity/InvoiceEntity$Companion;", "", "()V", "fromDomainModel", "Lcom/example/sharen/data/local/entity/InvoiceEntity;", "invoice", "Lcom/example/sharen/domain/model/Invoice;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.example.sharen.data.local.entity.InvoiceEntity fromDomainModel(@org.jetbrains.annotations.NotNull
        com.example.sharen.domain.model.Invoice invoice) {
            return null;
        }
    }
}