package com.example.sharen.domain.usecase.product;

import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateProductStockUseCase_Factory implements Factory<UpdateProductStockUseCase> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public UpdateProductStockUseCase_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public UpdateProductStockUseCase get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static UpdateProductStockUseCase_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new UpdateProductStockUseCase_Factory(productRepositoryProvider);
  }

  public static UpdateProductStockUseCase newInstance(ProductRepository productRepository) {
    return new UpdateProductStockUseCase(productRepository);
  }
}
