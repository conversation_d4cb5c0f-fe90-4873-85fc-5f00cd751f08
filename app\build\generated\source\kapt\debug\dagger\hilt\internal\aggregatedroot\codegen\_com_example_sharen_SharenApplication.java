package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.sharen.SharenApplication",
    rootPackage = "com.example.sharen",
    originatingRoot = "com.example.sharen.SharenApplication",
    originatingRootPackage = "com.example.sharen",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "SharenApplication",
    originatingRootSimpleNames = "SharenApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_sharen_SharenApplication {
}
