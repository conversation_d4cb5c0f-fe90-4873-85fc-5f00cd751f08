package com.example.sharen.data.repository;

import com.example.sharen.data.remote.ReportRemoteDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReportRepositoryImpl_Factory implements Factory<ReportRepositoryImpl> {
  private final Provider<ReportRemoteDataSource> remoteDataSourceProvider;

  public ReportRepositoryImpl_Factory(Provider<ReportRemoteDataSource> remoteDataSourceProvider) {
    this.remoteDataSourceProvider = remoteDataSourceProvider;
  }

  @Override
  public ReportRepositoryImpl get() {
    return newInstance(remoteDataSourceProvider.get());
  }

  public static ReportRepositoryImpl_Factory create(
      Provider<ReportRemoteDataSource> remoteDataSourceProvider) {
    return new ReportRepositoryImpl_Factory(remoteDataSourceProvider);
  }

  public static ReportRepositoryImpl newInstance(ReportRemoteDataSource remoteDataSource) {
    return new ReportRepositoryImpl(remoteDataSource);
  }
}
