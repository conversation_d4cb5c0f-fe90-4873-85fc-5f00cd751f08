// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCustomerDetailsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBar;

  @NonNull
  public final CardView cardCustomerInfo;

  @NonNull
  public final CardView cardFinancialInfo;

  @NonNull
  public final CollapsingToolbarLayout collapsingToolbar;

  @NonNull
  public final FloatingActionButton fabEdit;

  @NonNull
  public final FloatingActionButton fabNewInvoice;

  @NonNull
  public final ImageView ivCustomerHeader;

  @NonNull
  public final RecyclerView rvRecentInvoices;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCustomerAddress;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvCustomerPhone;

  @NonNull
  public final TextView tvCustomerSince;

  @NonNull
  public final TextView tvDebtAmount;

  @NonNull
  public final TextView tvLastPurchase;

  @NonNull
  public final TextView tvRecentInvoicesTitle;

  @NonNull
  public final TextView tvTotalPurchases;

  private ActivityCustomerDetailsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBar, @NonNull CardView cardCustomerInfo,
      @NonNull CardView cardFinancialInfo, @NonNull CollapsingToolbarLayout collapsingToolbar,
      @NonNull FloatingActionButton fabEdit, @NonNull FloatingActionButton fabNewInvoice,
      @NonNull ImageView ivCustomerHeader, @NonNull RecyclerView rvRecentInvoices,
      @NonNull Toolbar toolbar, @NonNull TextView tvCustomerAddress,
      @NonNull TextView tvCustomerName, @NonNull TextView tvCustomerPhone,
      @NonNull TextView tvCustomerSince, @NonNull TextView tvDebtAmount,
      @NonNull TextView tvLastPurchase, @NonNull TextView tvRecentInvoicesTitle,
      @NonNull TextView tvTotalPurchases) {
    this.rootView = rootView;
    this.appBar = appBar;
    this.cardCustomerInfo = cardCustomerInfo;
    this.cardFinancialInfo = cardFinancialInfo;
    this.collapsingToolbar = collapsingToolbar;
    this.fabEdit = fabEdit;
    this.fabNewInvoice = fabNewInvoice;
    this.ivCustomerHeader = ivCustomerHeader;
    this.rvRecentInvoices = rvRecentInvoices;
    this.toolbar = toolbar;
    this.tvCustomerAddress = tvCustomerAddress;
    this.tvCustomerName = tvCustomerName;
    this.tvCustomerPhone = tvCustomerPhone;
    this.tvCustomerSince = tvCustomerSince;
    this.tvDebtAmount = tvDebtAmount;
    this.tvLastPurchase = tvLastPurchase;
    this.tvRecentInvoicesTitle = tvRecentInvoicesTitle;
    this.tvTotalPurchases = tvTotalPurchases;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCustomerDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCustomerDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_customer_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCustomerDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar;
      AppBarLayout appBar = ViewBindings.findChildViewById(rootView, id);
      if (appBar == null) {
        break missingId;
      }

      id = R.id.card_customer_info;
      CardView cardCustomerInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardCustomerInfo == null) {
        break missingId;
      }

      id = R.id.card_financial_info;
      CardView cardFinancialInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardFinancialInfo == null) {
        break missingId;
      }

      id = R.id.collapsing_toolbar;
      CollapsingToolbarLayout collapsingToolbar = ViewBindings.findChildViewById(rootView, id);
      if (collapsingToolbar == null) {
        break missingId;
      }

      id = R.id.fab_edit;
      FloatingActionButton fabEdit = ViewBindings.findChildViewById(rootView, id);
      if (fabEdit == null) {
        break missingId;
      }

      id = R.id.fab_new_invoice;
      FloatingActionButton fabNewInvoice = ViewBindings.findChildViewById(rootView, id);
      if (fabNewInvoice == null) {
        break missingId;
      }

      id = R.id.iv_customer_header;
      ImageView ivCustomerHeader = ViewBindings.findChildViewById(rootView, id);
      if (ivCustomerHeader == null) {
        break missingId;
      }

      id = R.id.rv_recent_invoices;
      RecyclerView rvRecentInvoices = ViewBindings.findChildViewById(rootView, id);
      if (rvRecentInvoices == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_customer_address;
      TextView tvCustomerAddress = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerAddress == null) {
        break missingId;
      }

      id = R.id.tv_customer_name;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tv_customer_phone;
      TextView tvCustomerPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerPhone == null) {
        break missingId;
      }

      id = R.id.tv_customer_since;
      TextView tvCustomerSince = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerSince == null) {
        break missingId;
      }

      id = R.id.tv_debt_amount;
      TextView tvDebtAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvDebtAmount == null) {
        break missingId;
      }

      id = R.id.tv_last_purchase;
      TextView tvLastPurchase = ViewBindings.findChildViewById(rootView, id);
      if (tvLastPurchase == null) {
        break missingId;
      }

      id = R.id.tv_recent_invoices_title;
      TextView tvRecentInvoicesTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvRecentInvoicesTitle == null) {
        break missingId;
      }

      id = R.id.tv_total_purchases;
      TextView tvTotalPurchases = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalPurchases == null) {
        break missingId;
      }

      return new ActivityCustomerDetailsBinding((CoordinatorLayout) rootView, appBar,
          cardCustomerInfo, cardFinancialInfo, collapsingToolbar, fabEdit, fabNewInvoice,
          ivCustomerHeader, rvRecentInvoices, toolbar, tvCustomerAddress, tvCustomerName,
          tvCustomerPhone, tvCustomerSince, tvDebtAmount, tvLastPurchase, tvRecentInvoicesTitle,
          tvTotalPurchases);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
