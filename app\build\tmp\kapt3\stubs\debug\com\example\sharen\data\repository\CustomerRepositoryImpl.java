package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\b2\u0006\u0010\t\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ*\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\b2\u0006\u0010\u000e\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u000e\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u0002J!\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u00142\u0006\u0010\u000e\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0011J\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\u0014H\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0017J\u001d\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0014H\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0017J%\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u00142\u0006\u0010\u001a\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0011J*\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00060\b2\u0006\u0010\t\u001a\u00020\u0006H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001c\u0010\u000bR\u001a\u0010\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u001d"}, d2 = {"Lcom/example/sharen/data/repository/CustomerRepositoryImpl;", "Lcom/example/sharen/data/repository/CustomerRepository;", "()V", "customers", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/example/sharen/data/model/Customer;", "createCustomer", "Lkotlin/Result;", "customer", "createCustomer-gIAlu-s", "(Lcom/example/sharen/data/model/Customer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCustomer", "", "customerId", "", "deleteCustomer-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateMockCustomers", "getCustomerById", "Lkotlinx/coroutines/flow/Flow;", "getCustomerCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCustomers", "searchCustomers", "query", "updateCustomer", "updateCustomer-gIAlu-s", "app_debug"})
public final class CustomerRepositoryImpl implements com.example.sharen.data.repository.CustomerRepository {
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharen.data.model.Customer>> customers = null;
    
    @javax.inject.Inject
    public CustomerRepositoryImpl() {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getCustomers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Customer>>> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object searchCustomers(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Customer>>> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getCustomerById(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Customer>> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getCustomerCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<java.lang.Integer>> $completion) {
        return null;
    }
    
    private final java.util.List<com.example.sharen.data.model.Customer> generateMockCustomers() {
        return null;
    }
}