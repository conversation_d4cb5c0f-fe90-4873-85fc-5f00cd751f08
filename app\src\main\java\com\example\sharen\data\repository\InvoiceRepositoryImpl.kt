package com.example.sharen.data.repository

import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.data.model.InvoiceStatus
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentMethod
import com.example.sharen.data.model.PaymentStatus
import com.example.sharen.data.model.PaymentType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.random.Random

/**
 * پیاده‌سازی رپوزیتوری فاکتورها با داده‌های آزمایشی
 */
@Singleton
class InvoiceRepositoryImpl @Inject constructor() : InvoiceRepository {

    // لیست فاکتورهای آزمایشی
    private val mockInvoices = mutableListOf<Invoice>()
    
    // لیست آیتم‌های فاکتور آزمایشی
    private val mockInvoiceItems = mutableMapOf<String, MutableList<InvoiceItem>>()
    
    // لیست پرداخت‌های آزمایشی
    private val mockPayments = mutableListOf<Payment>()
    
    // شماره فاکتور برای فاکتورهای جدید
    private var lastInvoiceNumber = 1000
    
    init {
        // ایجاد داده‌های آزمایشی
        createMockData()
    }
    
    /**
     * ایجاد داده‌های آزمایشی
     */
    private fun createMockData() {
        val customers = listOf(
            Pair("c1", "محمد احمدی"),
            Pair("c2", "فاطمه حسینی"),
            Pair("c3", "علی نجفی"),
            Pair("c4", "مریم محمدی"),
            Pair("c5", "سعید کریمی")
        )
        
        val productNames = listOf(
            Pair("p1", "پیراهن مردانه"),
            Pair("p2", "شلوار جین زنانه"),
            Pair("p3", "کفش ورزشی"),
            Pair("p4", "کیف دستی زنانه"),
            Pair("p5", "تیشرت آستین کوتاه"),
            Pair("p6", "شال زنانه"),
            Pair("p7", "کت تک مردانه"),
            Pair("p8", "مانتو زنانه")
        )
        
        val calendar = Calendar.getInstance()
        
        // ایجاد ۱۰ فاکتور آزمایشی
        for (i in 1..10) {
            val invoiceId = "inv$i"
            val customerIndex = Random.nextInt(customers.size)
            val invoiceDate = getRandomDate(60) // تاریخ تصادفی در ۶۰ روز گذشته
            
            // تعیین تصادفی وضعیت فاکتور
            val status = when (Random.nextInt(5)) {
                0 -> InvoiceStatus.DRAFT
                1 -> InvoiceStatus.PENDING
                2 -> InvoiceStatus.APPROVED
                3 -> InvoiceStatus.PAID
                4 -> InvoiceStatus.PARTIALLY_PAID
                else -> InvoiceStatus.APPROVED
            }
            
            // تعیین تصادفی روش پرداخت
            val paymentType = when (Random.nextInt(4)) {
                0 -> PaymentType.CASH
                1 -> PaymentType.CARD
                2 -> PaymentType.INSTALLMENT
                3 -> PaymentType.MIXED
                else -> PaymentType.CASH
            }
            
            // ایجاد آیتم‌های فاکتور
            val items = mutableListOf<InvoiceItem>()
            val itemCount = Random.nextInt(1, 5) // ۱ تا ۴ آیتم در هر فاکتور
            
            var totalAmount = 0L
            
            for (j in 1..itemCount) {
                val productIndex = Random.nextInt(productNames.size)
                val quantity = Random.nextInt(1, 5) // ۱ تا ۴ عدد
                val unitPrice = Random.nextLong(50000, 500000) // قیمت بین ۵۰ هزار تا ۵۰۰ هزار تومان
                val discount = if (Random.nextBoolean()) Random.nextLong(5000, 50000) else 0
                
                val item = InvoiceItem(
                    id = "item${i}_$j",
                    invoiceId = invoiceId,
                    productId = productNames[productIndex].first,
                    productName = productNames[productIndex].second,
                    productCode = "CODE-${productNames[productIndex].first}",
                    quantity = quantity,
                    unitPrice = unitPrice,
                    discount = discount
                )
                
                items.add(item)
                totalAmount += item.totalPrice
            }
            
            mockInvoiceItems[invoiceId] = items
            
            // محاسبه تخفیف و مالیات فاکتور
            val discount = if (Random.nextBoolean()) Random.nextLong(10000, 50000) else 0
            val tax = if (Random.nextBoolean()) (totalAmount * 0.09).toLong() else 0
            val finalAmount = totalAmount - discount + tax
            
            // تعیین مبلغ پرداخت شده بر اساس وضعیت فاکتور
            val paidAmount = when (status) {
                InvoiceStatus.PAID -> finalAmount
                InvoiceStatus.PARTIALLY_PAID -> (finalAmount * Random.nextDouble(0.1, 0.9)).toLong()
                else -> 0
            }
            
            val remainingAmount = finalAmount - paidAmount
            
            // ایجاد فاکتور
            val invoice = Invoice(
                id = invoiceId,
                invoiceNumber = "INV-${1000 + i}",
                customerId = customers[customerIndex].first,
                customerName = customers[customerIndex].second,
                totalAmount = totalAmount,
                discount = discount,
                tax = tax,
                finalAmount = finalAmount,
                paidAmount = paidAmount,
                remainingAmount = remainingAmount,
                status = status,
                paymentType = paymentType,
                createdAt = invoiceDate,
                updatedAt = invoiceDate,
                items = items
            )
            
            mockInvoices.add(invoice)
            
            // اگر فاکتور پرداخت شده یا پرداخت جزئی دارد، یک پرداخت ایجاد کنیم
            if (paidAmount > 0) {
                val payment = Payment(
                    id = "payment$i",
                    invoiceId = invoiceId,
                    amount = paidAmount,
                    method = if (paymentType == PaymentType.CARD) PaymentMethod.CREDIT_CARD else PaymentMethod.CASH,
                    status = PaymentStatus.COMPLETED,
                    referenceNumber = if (paymentType == PaymentType.CARD) "REF-${10000 + i}" else null,
                    paymentDate = getDateAfter(invoiceDate, Random.nextInt(0, 5)), // ۰ تا ۵ روز بعد از فاکتور
                    createdAt = invoiceDate
                )
                
                mockPayments.add(payment)
            }
        }
        
        // بروزرسانی شماره آخرین فاکتور
        lastInvoiceNumber = 1010
    }
    
    override fun getAllInvoices(): Flow<List<Invoice>> = flow {
        emit(mockInvoices.filter { !it.isDeleted })
    }
    
    override fun getInvoiceById(invoiceId: String): Flow<Invoice> = flow {
        val invoice = mockInvoices.find { it.id == invoiceId }
            ?: throw NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد")
        emit(invoice)
    }
    
    override fun searchInvoices(query: String): Flow<List<Invoice>> = flow {
        val searchQuery = query.trim().lowercase()
        val result = mockInvoices.filter { invoice ->
            !invoice.isDeleted && (
                invoice.invoiceNumber.lowercase().contains(searchQuery) ||
                invoice.customerName.lowercase().contains(searchQuery) ||
                invoice.id.lowercase().contains(searchQuery)
            )
        }
        emit(result)
    }
    
    override fun getInvoicesByCustomerId(customerId: String): Flow<List<Invoice>> = flow {
        val result = mockInvoices.filter { it.customerId == customerId && !it.isDeleted }
        emit(result)
    }
    
    override fun getInvoicesByDateRange(startDate: Date, endDate: Date): Flow<List<Invoice>> = flow {
        val result = mockInvoices.filter { 
            !it.isDeleted && (it.createdAt.after(startDate) || it.createdAt == startDate) && 
            (it.createdAt.before(endDate) || it.createdAt == endDate)
        }
        emit(result)
    }
    
    override suspend fun createInvoice(invoice: Invoice, items: List<InvoiceItem>): Result<Invoice> {
        return try {
            // ایجاد شماره فاکتور جدید
            lastInvoiceNumber++
            
            // کپی فاکتور با شماره جدید
            val newInvoice = invoice.copy(
                id = "inv${lastInvoiceNumber}",
                invoiceNumber = "INV-$lastInvoiceNumber",
                createdAt = Date(),
                updatedAt = Date()
            )
            
            // افزودن فاکتور به لیست
            mockInvoices.add(newInvoice)
            
            // افزودن آیتم‌ها به لیست
            val newItems = items.map { item ->
                item.copy(
                    id = "item_${newInvoice.id}_${mockInvoiceItems[newInvoice.id]?.size ?: 0 + 1}",
                    invoiceId = newInvoice.id
                )
            }
            
            mockInvoiceItems[newInvoice.id] = newItems.toMutableList()
            
            Result.success(newInvoice)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateInvoice(invoice: Invoice): Result<Invoice> {
        return try {
            val index = mockInvoices.indexOfFirst { it.id == invoice.id }
            if (index != -1) {
                // بروزرسانی فاکتور
                val updatedInvoice = invoice.copy(updatedAt = Date())
                mockInvoices[index] = updatedInvoice
                Result.success(updatedInvoice)
            } else {
                Result.failure(NoSuchElementException("فاکتور با شناسه ${invoice.id} یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteInvoice(invoiceId: String): Result<Unit> {
        return try {
            val index = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (index != -1) {
                // حذف منطقی فاکتور
                val invoice = mockInvoices[index]
                mockInvoices[index] = invoice.copy(isDeleted = true, updatedAt = Date())
                Result.success(Unit)
            } else {
                Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateInvoiceStatus(invoiceId: String, newStatus: InvoiceStatus): Result<Invoice> {
        return try {
            val index = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (index != -1) {
                // بروزرسانی وضعیت فاکتور
                val invoice = mockInvoices[index]
                val updatedInvoice = invoice.copy(
                    status = newStatus,
                    updatedAt = Date(),
                    approvedAt = if (newStatus == InvoiceStatus.APPROVED) Date() else invoice.approvedAt,
                    approvedBy = if (newStatus == InvoiceStatus.APPROVED) "admin" else invoice.approvedBy
                )
                mockInvoices[index] = updatedInvoice
                Result.success(updatedInvoice)
            } else {
                Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun addPayment(payment: Payment): Result<Payment> {
        return try {
            // افزودن پرداخت به لیست
            val newPayment = payment.copy(
                id = "payment${mockPayments.size + 1}",
                createdAt = Date(),
                updatedAt = Date()
            )
            mockPayments.add(newPayment)
            
            // بروزرسانی مبلغ پرداخت شده در فاکتور
            val invoiceIndex = mockInvoices.indexOfFirst { it.id == payment.invoiceId }
            if (invoiceIndex != -1) {
                val invoice = mockInvoices[invoiceIndex]
                val newPaidAmount = invoice.paidAmount + payment.amount
                val newRemainingAmount = invoice.finalAmount - newPaidAmount
                
                // تعیین وضعیت جدید فاکتور بر اساس مبلغ پرداخت شده
                val newStatus = when {
                    newPaidAmount >= invoice.finalAmount -> InvoiceStatus.PAID
                    newPaidAmount > 0 -> InvoiceStatus.PARTIALLY_PAID
                    else -> invoice.status
                }
                
                mockInvoices[invoiceIndex] = invoice.copy(
                    paidAmount = newPaidAmount,
                    remainingAmount = newRemainingAmount,
                    status = newStatus,
                    updatedAt = Date()
                )
            }
            
            Result.success(newPayment)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override fun getPaymentsByInvoiceId(invoiceId: String): Flow<List<Payment>> = flow {
        val payments = mockPayments.filter { it.invoiceId == invoiceId }
        emit(payments)
    }
    
    override fun getInvoiceItems(invoiceId: String): Flow<List<InvoiceItem>> = flow {
        val items = mockInvoiceItems[invoiceId] ?: emptyList()
        emit(items)
    }
    
    override suspend fun addInvoiceItem(invoiceId: String, item: InvoiceItem): Result<InvoiceItem> {
        return try {
            val invoiceIndex = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (invoiceIndex == -1) {
                return Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
            
            // ایجاد آیتم جدید
            val newItem = item.copy(
                id = "item_${invoiceId}_${mockInvoiceItems[invoiceId]?.size ?: 0 + 1}",
                invoiceId = invoiceId
            )
            
            // افزودن آیتم به لیست
            if (mockInvoiceItems.containsKey(invoiceId)) {
                mockInvoiceItems[invoiceId]?.add(newItem)
            } else {
                mockInvoiceItems[invoiceId] = mutableListOf(newItem)
            }
            
            // بروزرسانی مبلغ کل فاکتور
            val invoice = mockInvoices[invoiceIndex]
            val newTotalAmount = invoice.totalAmount + newItem.totalPrice
            val newFinalAmount = newTotalAmount - invoice.discount + invoice.tax
            val newRemainingAmount = newFinalAmount - invoice.paidAmount
            
            mockInvoices[invoiceIndex] = invoice.copy(
                totalAmount = newTotalAmount,
                finalAmount = newFinalAmount,
                remainingAmount = newRemainingAmount,
                updatedAt = Date()
            )
            
            Result.success(newItem)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun removeInvoiceItem(invoiceId: String, itemId: String): Result<Unit> {
        return try {
            val invoiceIndex = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (invoiceIndex == -1) {
                return Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
            
            // حذف آیتم از لیست
            val items = mockInvoiceItems[invoiceId]
            val itemIndex = items?.indexOfFirst { it.id == itemId } ?: -1
            
            if (itemIndex == -1) {
                return Result.failure(NoSuchElementException("آیتم با شناسه $itemId یافت نشد"))
            }
            
            val removedItem = items!![itemIndex]
            items.removeAt(itemIndex)
            
            // بروزرسانی مبلغ کل فاکتور
            val invoice = mockInvoices[invoiceIndex]
            val newTotalAmount = invoice.totalAmount - removedItem.totalPrice
            val newFinalAmount = newTotalAmount - invoice.discount + invoice.tax
            val newRemainingAmount = newFinalAmount - invoice.paidAmount
            
            mockInvoices[invoiceIndex] = invoice.copy(
                totalAmount = newTotalAmount,
                finalAmount = newFinalAmount,
                remainingAmount = newRemainingAmount,
                updatedAt = Date()
            )
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateInvoiceItem(invoiceId: String, item: InvoiceItem): Result<InvoiceItem> {
        return try {
            val invoiceIndex = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (invoiceIndex == -1) {
                return Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
            
            // بروزرسانی آیتم در لیست
            val items = mockInvoiceItems[invoiceId]
            val itemIndex = items?.indexOfFirst { it.id == item.id } ?: -1
            
            if (itemIndex == -1) {
                return Result.failure(NoSuchElementException("آیتم با شناسه ${item.id} یافت نشد"))
            }
            
            val oldItem = items!![itemIndex]
            items[itemIndex] = item
            
            // بروزرسانی مبلغ کل فاکتور
            val invoice = mockInvoices[invoiceIndex]
            val newTotalAmount = invoice.totalAmount - oldItem.totalPrice + item.totalPrice
            val newFinalAmount = newTotalAmount - invoice.discount + invoice.tax
            val newRemainingAmount = newFinalAmount - invoice.paidAmount
            
            mockInvoices[invoiceIndex] = invoice.copy(
                totalAmount = newTotalAmount,
                finalAmount = newFinalAmount,
                remainingAmount = newRemainingAmount,
                updatedAt = Date()
            )
            
            Result.success(item)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun recalculateInvoice(invoiceId: String): Result<Invoice> {
        return try {
            val invoiceIndex = mockInvoices.indexOfFirst { it.id == invoiceId }
            if (invoiceIndex == -1) {
                return Result.failure(NoSuchElementException("فاکتور با شناسه $invoiceId یافت نشد"))
            }
            
            // محاسبه مجدد مبلغ کل فاکتور
            val invoice = mockInvoices[invoiceIndex]
            val items = mockInvoiceItems[invoiceId] ?: emptyList()
            
            val totalAmount = items.sumOf { it.totalPrice }
            val finalAmount = totalAmount - invoice.discount + invoice.tax
            val remainingAmount = finalAmount - invoice.paidAmount
            
            // بروزرسانی وضعیت فاکتور بر اساس مبلغ پرداخت شده
            val newStatus = when {
                remainingAmount <= 0 -> InvoiceStatus.PAID
                invoice.paidAmount > 0 -> InvoiceStatus.PARTIALLY_PAID
                else -> invoice.status
            }
            
            val updatedInvoice = invoice.copy(
                totalAmount = totalAmount,
                finalAmount = finalAmount,
                remainingAmount = remainingAmount,
                status = newStatus,
                updatedAt = Date()
            )
            
            mockInvoices[invoiceIndex] = updatedInvoice
            
            Result.success(updatedInvoice)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override fun getInvoiceCount(): Flow<Int> = flow {
        emit(mockInvoices.count { !it.isDeleted })
    }
    
    override fun getTotalSales(): Flow<Long> = flow {
        val total = mockInvoices
            .filter { !it.isDeleted && (it.status == InvoiceStatus.PAID || it.status == InvoiceStatus.PARTIALLY_PAID) }
            .sumOf { it.paidAmount }
        emit(total)
    }
    
    override fun getRecentInvoices(limit: Int): Flow<List<Invoice>> = flow {
        val result = mockInvoices
            .filter { !it.isDeleted }
            .sortedByDescending { it.createdAt }
            .take(limit)
        emit(result)
    }
    
    /**
     * دریافت تاریخ تصادفی در بازه روزهای گذشته
     */
    private fun getRandomDate(daysAgo: Int): Date {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -Random.nextInt(0, daysAgo))
        return calendar.time
    }
    
    /**
     * دریافت تاریخ با افزودن تعداد روز به تاریخ مبدا
     */
    private fun getDateAfter(baseDate: Date, daysAfter: Int): Date {
        val calendar = Calendar.getInstance()
        calendar.time = baseDate
        calendar.add(Calendar.DAY_OF_YEAR, daysAfter)
        return calendar.time
    }
} 