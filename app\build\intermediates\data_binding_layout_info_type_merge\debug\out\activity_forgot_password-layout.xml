<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_forgot_password" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_forgot_password.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_forgot_password_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="97" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="51"/></Target><Target id="@+id/tv_forgot_password_title" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="32" endOffset="60"/></Target><Target id="@+id/tv_instruction" view="TextView"><Expressions/><location startLine="34" startOffset="4" endLine="44" endOffset="77"/></Target><Target id="@+id/til_email" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="46" startOffset="4" endLine="62" endOffset="59"/></Target><Target id="@+id/et_email" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="8" endLine="61" endOffset="50"/></Target><Target id="@+id/btn_reset_password" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="64" startOffset="4" endLine="74" endOffset="62"/></Target><Target id="@+id/tv_back_to_login" view="TextView"><Expressions/><location startLine="76" startOffset="4" endLine="85" endOffset="71"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="87" startOffset="4" endLine="95" endOffset="51"/></Target></Targets></Layout>