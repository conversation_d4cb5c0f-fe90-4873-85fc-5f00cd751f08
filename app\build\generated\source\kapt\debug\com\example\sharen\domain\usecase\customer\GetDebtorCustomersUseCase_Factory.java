package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetDebtorCustomersUseCase_Factory implements Factory<GetDebtorCustomersUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public GetDebtorCustomersUseCase_Factory(
      Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public GetDebtorCustomersUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static GetDebtorCustomersUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new GetDebtorCustomersUseCase_Factory(customerRepositoryProvider);
  }

  public static GetDebtorCustomersUseCase newInstance(CustomerRepository customerRepository) {
    return new GetDebtorCustomersUseCase(customerRepository);
  }
}
