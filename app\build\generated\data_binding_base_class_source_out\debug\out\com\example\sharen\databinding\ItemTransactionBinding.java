// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTransactionBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvTransactionAmount;

  @NonNull
  public final TextView tvTransactionDate;

  @NonNull
  public final TextView tvTransactionNumber;

  @NonNull
  public final TextView tvTransactionStatus;

  private ItemTransactionBinding(@NonNull CardView rootView, @NonNull TextView tvCustomerName,
      @NonNull TextView tvTransactionAmount, @NonNull TextView tvTransactionDate,
      @NonNull TextView tvTransactionNumber, @NonNull TextView tvTransactionStatus) {
    this.rootView = rootView;
    this.tvCustomerName = tvCustomerName;
    this.tvTransactionAmount = tvTransactionAmount;
    this.tvTransactionDate = tvTransactionDate;
    this.tvTransactionNumber = tvTransactionNumber;
    this.tvTransactionStatus = tvTransactionStatus;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_transaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_customer_name;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tv_transaction_amount;
      TextView tvTransactionAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionAmount == null) {
        break missingId;
      }

      id = R.id.tv_transaction_date;
      TextView tvTransactionDate = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionDate == null) {
        break missingId;
      }

      id = R.id.tv_transaction_number;
      TextView tvTransactionNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionNumber == null) {
        break missingId;
      }

      id = R.id.tv_transaction_status;
      TextView tvTransactionStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvTransactionStatus == null) {
        break missingId;
      }

      return new ItemTransactionBinding((CardView) rootView, tvCustomerName, tvTransactionAmount,
          tvTransactionDate, tvTransactionNumber, tvTransactionStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
