package com.example.sharen.data.remote;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010 \n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0018\bf\u0018\u00002\u00020\u0001J!\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0004H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J!\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0001\u0010\t\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ!\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\b\b\u0001\u0010\r\u001a\u00020\fH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000eJ!\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0001\u0010\u0011\u001a\u00020\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0012J!\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u00032\b\b\u0001\u0010\u0015\u001a\u00020\u0014H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0016J!\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u00032\b\b\u0001\u0010\u0019\u001a\u00020\u0018H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001aJ+\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00032\b\b\u0001\u0010\u001d\u001a\u00020\u001e2\b\b\u0001\u0010\u001f\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010 J!\u0010!\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00032\b\b\u0001\u0010\"\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J+\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\u00032\b\b\u0001\u0010\u001d\u001a\u00020\u001e2\b\b\u0001\u0010\u001f\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010 J!\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\'\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u0010+\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0001\u0010,\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u0010.\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\b\b\u0001\u0010/\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u00101\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0001\u00102\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u00103\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u00104\u001a\b\u0012\u0004\u0012\u00020\u00140\u00032\b\b\u0001\u0010\"\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u00105\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u00106\u001a\b\u0012\u0004\u0012\u00020\u00180\u00032\b\b\u0001\u00107\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u00108\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u00109\u001a\b\u0012\u0004\u0012\u00020:0\u00032\b\b\u0001\u0010;\u001a\u00020\u001eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u001d\u0010<\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020:0)0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J\u0017\u0010=\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010*J!\u0010>\u001a\b\u0012\u0004\u0012\u00020%0\u00032\b\b\u0001\u0010?\u001a\u00020%H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010@J!\u0010A\u001a\b\u0012\u0004\u0012\u00020%0\u00032\b\b\u0001\u0010?\u001a\u00020%H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010@J+\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\'\u001a\u00020\u001e2\b\b\u0001\u0010\u0005\u001a\u00020\u0004H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010CJ+\u0010D\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0001\u0010,\u001a\u00020\u001e2\b\b\u0001\u0010\t\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010EJ+\u0010F\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\b\b\u0001\u0010/\u001a\u00020\u001e2\b\b\u0001\u0010\r\u001a\u00020\fH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010GJ+\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0001\u00102\u001a\u00020\u001e2\b\b\u0001\u0010\u0011\u001a\u00020\u0010H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010IJ+\u0010J\u001a\b\u0012\u0004\u0012\u00020\u00140\u00032\b\b\u0001\u0010\"\u001a\u00020\u001e2\b\b\u0001\u0010\u0015\u001a\u00020\u0014H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010KJ+\u0010L\u001a\b\u0012\u0004\u0012\u00020:0\u00032\b\b\u0001\u0010;\u001a\u00020\u001e2\b\b\u0001\u0010M\u001a\u00020:H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010NJ+\u0010O\u001a\b\u0012\u0004\u0012\u00020%0\u00032\b\b\u0001\u0010\u001d\u001a\u00020\u001e2\b\b\u0001\u0010P\u001a\u00020%H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010Q\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006R"}, d2 = {"Lcom/example/sharen/data/remote/SupabaseApiService;", "", "createCustomer", "Lretrofit2/Response;", "Lcom/example/sharen/data/model/Customer;", "customer", "(Lcom/example/sharen/data/model/Customer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInstallment", "Lcom/example/sharen/data/model/Installment;", "installment", "(Lcom/example/sharen/data/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createOrder", "Lcom/example/sharen/data/model/Order;", "order", "(Lcom/example/sharen/data/model/Order;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createPayment", "Lcom/example/sharen/data/model/Payment;", "payment", "(Lcom/example/sharen/data/model/Payment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createProduct", "Lcom/example/sharen/data/model/Product;", "product", "(Lcom/example/sharen/data/model/Product;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createReport", "Lcom/example/sharen/data/model/Report;", "report", "(Lcom/example/sharen/data/model/Report;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFile", "", "bucket", "", "path", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProduct", "productId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "downloadFile", "error/NonExistentClass", "getCustomer", "customerId", "getCustomers", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstallment", "installmentId", "getInstallments", "getOrder", "orderId", "getOrders", "getPayment", "paymentId", "getPayments", "getProduct", "getProducts", "getReport", "reportId", "getReports", "getUser", "Lcom/example/sharen/data/model/User;", "userId", "getUsers", "logout", "signIn", "request", "(Lerror/NonExistentClass;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signUp", "updateCustomer", "(Ljava/lang/String;Lcom/example/sharen/data/model/Customer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInstallment", "(Ljava/lang/String;Lcom/example/sharen/data/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateOrder", "(Ljava/lang/String;Lcom/example/sharen/data/model/Order;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePayment", "(Ljava/lang/String;Lcom/example/sharen/data/model/Payment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProduct", "(Ljava/lang/String;Lcom/example/sharen/data/model/Product;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "user", "(Ljava/lang/String;Lcom/example/sharen/data/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadFile", "file", "(Ljava/lang/String;Lerror/NonExistentClass;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface SupabaseApiService {
    
    @retrofit2.http.POST(value = "auth/v1/signup")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object signUp(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    error.NonExistentClass request, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<error.NonExistentClass>> $completion);
    
    @retrofit2.http.POST(value = "auth/v1/signin")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object signIn(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    error.NonExistentClass request, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<error.NonExistentClass>> $completion);
    
    @retrofit2.http.POST(value = "auth/v1/logout")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object logout(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<kotlin.Unit>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/users")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUsers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.User>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/users/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUser(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.User>> $completion);
    
    @retrofit2.http.PUT(value = "rest/v1/users/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUser(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String userId, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.User user, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.User>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/products")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getProducts(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.Product>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/products/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getProduct(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String productId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Product>> $completion);
    
    @retrofit2.http.POST(value = "rest/v1/products")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createProduct(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Product product, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Product>> $completion);
    
    @retrofit2.http.PUT(value = "rest/v1/products/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateProduct(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String productId, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Product product, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Product>> $completion);
    
    @retrofit2.http.DELETE(value = "rest/v1/products/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteProduct(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String productId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<kotlin.Unit>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/customers")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.Customer>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/customers/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomer(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Customer>> $completion);
    
    @retrofit2.http.POST(value = "rest/v1/customers")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createCustomer(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Customer customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Customer>> $completion);
    
    @retrofit2.http.PUT(value = "rest/v1/customers/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCustomer(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Customer customer, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Customer>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/orders")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getOrders(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.Order>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/orders/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getOrder(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String orderId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Order>> $completion);
    
    @retrofit2.http.POST(value = "rest/v1/orders")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createOrder(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Order order, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Order>> $completion);
    
    @retrofit2.http.PUT(value = "rest/v1/orders/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateOrder(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String orderId, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Order order, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Order>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/payments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPayments(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.Payment>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/payments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPayment(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String paymentId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Payment>> $completion);
    
    @retrofit2.http.POST(value = "rest/v1/payments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createPayment(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Payment>> $completion);
    
    @retrofit2.http.PUT(value = "rest/v1/payments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updatePayment(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String paymentId, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Payment>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/installments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getInstallments(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.Installment>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/installments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getInstallment(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String installmentId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Installment>> $completion);
    
    @retrofit2.http.POST(value = "rest/v1/installments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createInstallment(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Installment>> $completion);
    
    @retrofit2.http.PUT(value = "rest/v1/installments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateInstallment(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String installmentId, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Installment>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/reports")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getReports(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<java.util.List<com.example.sharen.data.model.Report>>> $completion);
    
    @retrofit2.http.GET(value = "rest/v1/reports/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getReport(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull
    java.lang.String reportId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Report>> $completion);
    
    @retrofit2.http.POST(value = "rest/v1/reports")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createReport(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Report report, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.sharen.data.model.Report>> $completion);
    
    @retrofit2.http.Multipart
    @retrofit2.http.POST(value = "storage/v1/object/{bucket}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object uploadFile(@retrofit2.http.Path(value = "bucket")
    @org.jetbrains.annotations.NotNull
    java.lang.String bucket, @retrofit2.http.Part(value = "file")
    @org.jetbrains.annotations.NotNull
    error.NonExistentClass file, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<error.NonExistentClass>> $completion);
    
    @retrofit2.http.GET(value = "storage/v1/object/{bucket}/{path}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object downloadFile(@retrofit2.http.Path(value = "bucket")
    @org.jetbrains.annotations.NotNull
    java.lang.String bucket, @retrofit2.http.Path(value = "path")
    @org.jetbrains.annotations.NotNull
    java.lang.String path, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<error.NonExistentClass>> $completion);
    
    @retrofit2.http.DELETE(value = "storage/v1/object/{bucket}/{path}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteFile(@retrofit2.http.Path(value = "bucket")
    @org.jetbrains.annotations.NotNull
    java.lang.String bucket, @retrofit2.http.Path(value = "path")
    @org.jetbrains.annotations.NotNull
    java.lang.String path, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super retrofit2.Response<kotlin.Unit>> $completion);
}