package com.example.sharen.data.repository

import com.example.sharen.data.local.dao.PaymentDao
import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentStatus
import com.example.sharen.data.remote.PaymentRemoteDataSource
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PaymentRepositoryImpl @Inject constructor(
    override val paymentDao: PaymentDao,
    private val remoteDataSource: PaymentRemoteDataSource
) : PaymentRepository() {
    
    override suspend fun createPayment(payment: Payment): Result<Payment> {
        return remoteDataSource.createPayment(payment)
    }
    
    override suspend fun updatePayment(payment: Payment): Result<Payment> {
        return remoteDataSource.updatePayment(payment)
    }
    
    override suspend fun deletePayment(paymentId: String): Result<Unit> {
        return remoteDataSource.deletePayment(paymentId)
    }
    
    override suspend fun getPayment(paymentId: String): Result<Payment> {
        return remoteDataSource.getPayment(paymentId)
    }
    
    override fun getAllPayments(): Flow<List<Payment>> {
        return remoteDataSource.getAllPayments()
    }
    
    override fun getPaymentsByCustomer(customerId: String): Flow<List<Payment>> {
        return remoteDataSource.getPaymentsByCustomer(customerId)
    }
    
    override fun getPaymentsByDateRange(startDate: Date, endDate: Date): Flow<List<Payment>> {
        return remoteDataSource.getPaymentsByDateRange(startDate, endDate)
    }
    
    override fun getPaymentsByStatus(status: PaymentStatus): Flow<List<Payment>> {
        return remoteDataSource.getPaymentsByStatus(status)
    }
    
    override suspend fun confirmPayment(paymentId: String): Result<Payment> {
        return remoteDataSource.confirmPayment(paymentId)
    }
    
    override suspend fun rejectPayment(paymentId: String, reason: String): Result<Payment> {
        return remoteDataSource.rejectPayment(paymentId, reason)
    }
    
    override suspend fun getTotalPaymentsByDateRange(startDate: Date, endDate: Date): Result<Double> {
        return remoteDataSource.getTotalPaymentsByDateRange(startDate, endDate)
    }
    
    override suspend fun getCustomerTotalPayments(customerId: String): Result<Double> {
        return remoteDataSource.getCustomerTotalPayments(customerId)
    }
    
    override suspend fun getPaymentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>> {
        return remoteDataSource.getPaymentStatistics(startDate, endDate)
    }
} 