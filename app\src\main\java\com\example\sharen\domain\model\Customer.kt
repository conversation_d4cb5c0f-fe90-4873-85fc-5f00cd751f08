package com.example.sharen.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Domain Model برای مشتری
 */
@Parcelize
data class Customer(
    val id: String,
    val userId: String,
    val name: String,
    val phone: String,
    val address: String? = null,
    val notes: String? = null,
    val creditLimit: Long = 0,
    val totalPurchases: Long = 0,
    val totalPayments: Long = 0,
    val totalDebt: Long = 0,
    val lastPurchaseDate: Date? = null,
    val lastPaymentDate: Date? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) : Parcelable {
    
    /**
     * محاسبه اعتبار باقیمانده
     */
    val remainingCredit: Long
        get() = creditLimit - totalDebt
    
    /**
     * آیا مشتری اعتبار کافی دارد؟
     */
    fun hasEnoughCredit(amount: Long): Boolean {
        return remainingCredit >= amount
    }
    
    /**
     * آیا مشتری بدهکار است؟
     */
    val isDebtor: Boolean
        get() = totalDebt > 0
    
    /**
     * وضعیت اعتباری مشتری
     */
    val creditStatus: CreditStatus
        get() = when {
            totalDebt == 0L -> CreditStatus.CLEAR
            totalDebt > 0 && totalDebt <= creditLimit * 0.5 -> CreditStatus.GOOD
            totalDebt > creditLimit * 0.5 && totalDebt <= creditLimit * 0.8 -> CreditStatus.WARNING
            totalDebt > creditLimit * 0.8 && totalDebt < creditLimit -> CreditStatus.CRITICAL
            else -> CreditStatus.EXCEEDED
        }
}

/**
 * وضعیت اعتباری مشتری
 */
enum class CreditStatus(val displayName: String, val colorRes: Int) {
    CLEAR("تسویه", android.R.color.holo_green_dark),
    GOOD("خوب", android.R.color.holo_blue_dark),
    WARNING("هشدار", android.R.color.holo_orange_dark),
    CRITICAL("بحرانی", android.R.color.holo_red_dark),
    EXCEEDED("تجاوز از حد", android.R.color.black)
}
