package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetAllCustomersUseCase_Factory implements Factory<GetAllCustomersUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public GetAllCustomersUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public GetAllCustomersUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static GetAllCustomersUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new GetAllCustomersUseCase_Factory(customerRepositoryProvider);
  }

  public static GetAllCustomersUseCase newInstance(CustomerRepository customerRepository) {
    return new GetAllCustomersUseCase(customerRepository);
  }
}
