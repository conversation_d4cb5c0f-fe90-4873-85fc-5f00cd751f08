<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_installment" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_installment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_installment_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="239" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="43"/></Target><Target id="@+id/tvInvoiceNumber" view="TextView"><Expressions/><location startLine="67" startOffset="24" endLine="72" endOffset="53"/></Target><Target id="@+id/tvCustomerName" view="TextView"><Expressions/><location startLine="87" startOffset="24" endLine="92" endOffset="52"/></Target><Target id="@+id/tvTotalAmount" view="TextView"><Expressions/><location startLine="113" startOffset="28" endLine="120" endOffset="62"/></Target><Target id="@+id/tvPaidAmount" view="TextView"><Expressions/><location startLine="136" startOffset="28" endLine="143" endOffset="60"/></Target><Target id="@+id/tvRemainingAmount" view="TextView"><Expressions/><location startLine="159" startOffset="28" endLine="166" endOffset="60"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="199" startOffset="20" endLine="205" endOffset="67"/></Target><Target id="@+id/tvEmptyState" view="TextView"><Expressions/><location startLine="207" startOffset="20" endLine="215" endOffset="51"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="217" startOffset="20" endLine="223" endOffset="51"/></Target><Target id="@+id/fabAddInstallment" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="229" startOffset="4" endLine="237" endOffset="33"/></Target></Targets></Layout>