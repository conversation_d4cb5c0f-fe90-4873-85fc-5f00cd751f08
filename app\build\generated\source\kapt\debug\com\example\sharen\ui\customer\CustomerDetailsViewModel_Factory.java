package com.example.sharen.ui.customer;

import com.example.sharen.data.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerDetailsViewModel_Factory implements Factory<CustomerDetailsViewModel> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public CustomerDetailsViewModel_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public CustomerDetailsViewModel get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static CustomerDetailsViewModel_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new CustomerDetailsViewModel_Factory(customerRepositoryProvider);
  }

  public static CustomerDetailsViewModel newInstance(CustomerRepository customerRepository) {
    return new CustomerDetailsViewModel(customerRepository);
  }
}
