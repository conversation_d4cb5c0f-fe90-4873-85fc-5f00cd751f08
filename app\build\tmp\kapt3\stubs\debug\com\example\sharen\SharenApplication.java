package com.example.sharen;

@dagger.hilt.android.HiltAndroidApp
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u000b\u001a\u00020\fH\u0016R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u0005\u001a\u00020\u00068\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\n\u00a8\u0006\r"}, d2 = {"Lcom/example/sharen/SharenApplication;", "Landroid/app/Application;", "()V", "applicationScope", "Lkotlinx/coroutines/CoroutineScope;", "dataMigrationHelper", "Lcom/example/sharen/data/migration/DataMigrationHelper;", "getDataMigrationHelper", "()Lcom/example/sharen/data/migration/DataMigrationHelper;", "setDataMigrationHelper", "(Lcom/example/sharen/data/migration/DataMigrationHelper;)V", "onCreate", "", "app_debug"})
public final class SharenApplication extends android.app.Application {
    @javax.inject.Inject
    public com.example.sharen.data.migration.DataMigrationHelper dataMigrationHelper;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.CoroutineScope applicationScope = null;
    
    public SharenApplication() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.migration.DataMigrationHelper getDataMigrationHelper() {
        return null;
    }
    
    public final void setDataMigrationHelper(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.migration.DataMigrationHelper p0) {
    }
    
    @java.lang.Override
    public void onCreate() {
    }
}