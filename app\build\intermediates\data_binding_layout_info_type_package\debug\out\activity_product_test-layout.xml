<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_test" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_product_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_product_test_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="105" endOffset="51"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="22" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="70"/></Target><Target id="@+id/tvTitle" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="34" endOffset="65"/></Target><Target id="@+id/tvDescription" view="TextView"><Expressions/><location startLine="36" startOffset="4" endLine="48" endOffset="60"/></Target><Target id="@+id/btnCreateProduct" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="50" startOffset="4" endLine="63" endOffset="66"/></Target><Target id="@+id/btnShowProducts" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="65" startOffset="4" endLine="78" endOffset="69"/></Target><Target id="@+id/btnInfo" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="80" startOffset="4" endLine="93" endOffset="68"/></Target><Target id="@+id/imageView" view="ImageView"><Expressions/><location startLine="95" startOffset="4" endLine="103" endOffset="58"/></Target></Targets></Layout>