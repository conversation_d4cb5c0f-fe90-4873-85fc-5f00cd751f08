// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityInstallmentBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton fabAddInstallment;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvEmptyState;

  @NonNull
  public final TextView tvInvoiceNumber;

  @NonNull
  public final TextView tvPaidAmount;

  @NonNull
  public final TextView tvRemainingAmount;

  @NonNull
  public final TextView tvTotalAmount;

  private ActivityInstallmentBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton fabAddInstallment, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerView, @NonNull Toolbar toolbar,
      @NonNull TextView tvCustomerName, @NonNull TextView tvEmptyState,
      @NonNull TextView tvInvoiceNumber, @NonNull TextView tvPaidAmount,
      @NonNull TextView tvRemainingAmount, @NonNull TextView tvTotalAmount) {
    this.rootView = rootView;
    this.fabAddInstallment = fabAddInstallment;
    this.progressBar = progressBar;
    this.recyclerView = recyclerView;
    this.toolbar = toolbar;
    this.tvCustomerName = tvCustomerName;
    this.tvEmptyState = tvEmptyState;
    this.tvInvoiceNumber = tvInvoiceNumber;
    this.tvPaidAmount = tvPaidAmount;
    this.tvRemainingAmount = tvRemainingAmount;
    this.tvTotalAmount = tvTotalAmount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityInstallmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityInstallmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_installment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityInstallmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fabAddInstallment;
      FloatingActionButton fabAddInstallment = ViewBindings.findChildViewById(rootView, id);
      if (fabAddInstallment == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerView;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCustomerName;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tvEmptyState;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tvInvoiceNumber;
      TextView tvInvoiceNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceNumber == null) {
        break missingId;
      }

      id = R.id.tvPaidAmount;
      TextView tvPaidAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvPaidAmount == null) {
        break missingId;
      }

      id = R.id.tvRemainingAmount;
      TextView tvRemainingAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvRemainingAmount == null) {
        break missingId;
      }

      id = R.id.tvTotalAmount;
      TextView tvTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalAmount == null) {
        break missingId;
      }

      return new ActivityInstallmentBinding((CoordinatorLayout) rootView, fabAddInstallment,
          progressBar, recyclerView, toolbar, tvCustomerName, tvEmptyState, tvInvoiceNumber,
          tvPaidAmount, tvRemainingAmount, tvTotalAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
