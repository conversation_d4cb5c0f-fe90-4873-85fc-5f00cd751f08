package com.example.sharen.ui.payment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001Bf\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012M\u0010\u0006\u001aI\u0012\u0013\u0012\u00110\b\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0013\u0012\u00110\f\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\r\u0012\u0015\u0012\u0013\u0018\u00010\u000e\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000f\u0012\u0004\u0012\u00020\u00100\u0007\u00a2\u0006\u0002\u0010\u0011J\b\u0010\u001e\u001a\u00020\u0010H\u0002J\u0012\u0010\u001f\u001a\u00020\u00102\b\u0010 \u001a\u0004\u0018\u00010!H\u0014J\u0010\u0010\"\u001a\u00020\u00102\u0006\u0010\u0004\u001a\u00020\u0005H\u0002J\b\u0010#\u001a\u00020\u0010H\u0002J\b\u0010$\u001a\u00020\u0010H\u0002J\b\u0010%\u001a\u00020\u0010H\u0002J\b\u0010&\u001a\u00020\u0010H\u0002R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000RU\u0010\u0006\u001aI\u0012\u0013\u0012\u00110\b\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0013\u0012\u00110\f\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\r\u0012\u0015\u0012\u0013\u0018\u00010\u000e\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000f\u0012\u0004\u0012\u00020\u00100\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/example/sharen/ui/payment/InstallmentFormDialog;", "Landroid/app/Dialog;", "context", "Landroid/content/Context;", "installment", "Lcom/example/sharen/data/model/Installment;", "onSubmit", "Lkotlin/Function3;", "", "Lkotlin/ParameterName;", "name", "amount", "Ljava/util/Date;", "dueDate", "", "notes", "", "(Landroid/content/Context;Lcom/example/sharen/data/model/Installment;Lkotlin/jvm/functions/Function3;)V", "btnCancel", "Landroid/widget/Button;", "btnSelectDate", "btnSubmit", "dateFormatter", "Ljava/text/SimpleDateFormat;", "etAmount", "Landroid/widget/EditText;", "etNotes", "selectedDate", "tvDueDate", "Landroid/widget/TextView;", "initViews", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "populateForm", "setListeners", "showDatePicker", "submitForm", "updateDateDisplay", "app_debug"})
public final class InstallmentFormDialog extends android.app.Dialog {
    @org.jetbrains.annotations.Nullable
    private final com.example.sharen.data.model.Installment installment = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function3<java.lang.Long, java.util.Date, java.lang.String, kotlin.Unit> onSubmit = null;
    private android.widget.EditText etAmount;
    private android.widget.TextView tvDueDate;
    private android.widget.EditText etNotes;
    private android.widget.Button btnSelectDate;
    private android.widget.Button btnCancel;
    private android.widget.Button btnSubmit;
    @org.jetbrains.annotations.NotNull
    private final java.text.SimpleDateFormat dateFormatter = null;
    @org.jetbrains.annotations.NotNull
    private java.util.Date selectedDate;
    
    public InstallmentFormDialog(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function3<? super java.lang.Long, ? super java.util.Date, ? super java.lang.String, kotlin.Unit> onSubmit) {
        super(null);
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void setListeners() {
    }
    
    private final void populateForm(com.example.sharen.data.model.Installment installment) {
    }
    
    private final void showDatePicker() {
    }
    
    private final void updateDateDisplay() {
    }
    
    private final void submitForm() {
    }
}