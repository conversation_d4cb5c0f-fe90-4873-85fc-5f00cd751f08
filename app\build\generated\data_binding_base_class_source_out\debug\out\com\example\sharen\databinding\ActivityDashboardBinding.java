// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDashboardBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final CardView cardAddCustomer;

  @NonNull
  public final CardView cardNewInvoice;

  @NonNull
  public final CardView cardTestImageUpload;

  @NonNull
  public final FloatingActionButton fabAdd;

  @NonNull
  public final ImageView ivNotifications;

  @NonNull
  public final LinearLayout layoutQuickActions;

  @NonNull
  public final LinearLayout layoutStats;

  @NonNull
  public final RecyclerView rvRecentTransactions;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvQuickActionsHeader;

  @NonNull
  public final TextView tvRecentTransactionsHeader;

  @NonNull
  public final TextView tvTotalCustomersValue;

  @NonNull
  public final TextView tvTotalSalesValue;

  @NonNull
  public final TextView tvViewAll;

  private ActivityDashboardBinding(@NonNull CoordinatorLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull CardView cardAddCustomer,
      @NonNull CardView cardNewInvoice, @NonNull CardView cardTestImageUpload,
      @NonNull FloatingActionButton fabAdd, @NonNull ImageView ivNotifications,
      @NonNull LinearLayout layoutQuickActions, @NonNull LinearLayout layoutStats,
      @NonNull RecyclerView rvRecentTransactions, @NonNull Toolbar toolbar,
      @NonNull TextView tvQuickActionsHeader, @NonNull TextView tvRecentTransactionsHeader,
      @NonNull TextView tvTotalCustomersValue, @NonNull TextView tvTotalSalesValue,
      @NonNull TextView tvViewAll) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.cardAddCustomer = cardAddCustomer;
    this.cardNewInvoice = cardNewInvoice;
    this.cardTestImageUpload = cardTestImageUpload;
    this.fabAdd = fabAdd;
    this.ivNotifications = ivNotifications;
    this.layoutQuickActions = layoutQuickActions;
    this.layoutStats = layoutStats;
    this.rvRecentTransactions = rvRecentTransactions;
    this.toolbar = toolbar;
    this.tvQuickActionsHeader = tvQuickActionsHeader;
    this.tvRecentTransactionsHeader = tvRecentTransactionsHeader;
    this.tvTotalCustomersValue = tvTotalCustomersValue;
    this.tvTotalSalesValue = tvTotalSalesValue;
    this.tvViewAll = tvViewAll;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottom_navigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.card_add_customer;
      CardView cardAddCustomer = ViewBindings.findChildViewById(rootView, id);
      if (cardAddCustomer == null) {
        break missingId;
      }

      id = R.id.card_new_invoice;
      CardView cardNewInvoice = ViewBindings.findChildViewById(rootView, id);
      if (cardNewInvoice == null) {
        break missingId;
      }

      id = R.id.card_test_image_upload;
      CardView cardTestImageUpload = ViewBindings.findChildViewById(rootView, id);
      if (cardTestImageUpload == null) {
        break missingId;
      }

      id = R.id.fab_add;
      FloatingActionButton fabAdd = ViewBindings.findChildViewById(rootView, id);
      if (fabAdd == null) {
        break missingId;
      }

      id = R.id.iv_notifications;
      ImageView ivNotifications = ViewBindings.findChildViewById(rootView, id);
      if (ivNotifications == null) {
        break missingId;
      }

      id = R.id.layout_quick_actions;
      LinearLayout layoutQuickActions = ViewBindings.findChildViewById(rootView, id);
      if (layoutQuickActions == null) {
        break missingId;
      }

      id = R.id.layout_stats;
      LinearLayout layoutStats = ViewBindings.findChildViewById(rootView, id);
      if (layoutStats == null) {
        break missingId;
      }

      id = R.id.rv_recent_transactions;
      RecyclerView rvRecentTransactions = ViewBindings.findChildViewById(rootView, id);
      if (rvRecentTransactions == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_quick_actions_header;
      TextView tvQuickActionsHeader = ViewBindings.findChildViewById(rootView, id);
      if (tvQuickActionsHeader == null) {
        break missingId;
      }

      id = R.id.tv_recent_transactions_header;
      TextView tvRecentTransactionsHeader = ViewBindings.findChildViewById(rootView, id);
      if (tvRecentTransactionsHeader == null) {
        break missingId;
      }

      id = R.id.tv_total_customers_value;
      TextView tvTotalCustomersValue = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalCustomersValue == null) {
        break missingId;
      }

      id = R.id.tv_total_sales_value;
      TextView tvTotalSalesValue = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSalesValue == null) {
        break missingId;
      }

      id = R.id.tv_view_all;
      TextView tvViewAll = ViewBindings.findChildViewById(rootView, id);
      if (tvViewAll == null) {
        break missingId;
      }

      return new ActivityDashboardBinding((CoordinatorLayout) rootView, bottomNavigation,
          cardAddCustomer, cardNewInvoice, cardTestImageUpload, fabAdd, ivNotifications,
          layoutQuickActions, layoutStats, rvRecentTransactions, toolbar, tvQuickActionsHeader,
          tvRecentTransactionsHeader, tvTotalCustomersValue, tvTotalSalesValue, tvViewAll);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
