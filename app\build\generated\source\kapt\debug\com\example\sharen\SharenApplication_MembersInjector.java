package com.example.sharen;

import com.example.sharen.data.migration.DataMigrationHelper;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SharenApplication_MembersInjector implements MembersInjector<SharenApplication> {
  private final Provider<DataMigrationHelper> dataMigrationHelperProvider;

  public SharenApplication_MembersInjector(
      Provider<DataMigrationHelper> dataMigrationHelperProvider) {
    this.dataMigrationHelperProvider = dataMigrationHelperProvider;
  }

  public static MembersInjector<SharenApplication> create(
      Provider<DataMigrationHelper> dataMigrationHelperProvider) {
    return new SharenApplication_MembersInjector(dataMigrationHelperProvider);
  }

  @Override
  public void injectMembers(SharenApplication instance) {
    injectDataMigrationHelper(instance, dataMigrationHelperProvider.get());
  }

  @InjectedFieldSignature("com.example.sharen.SharenApplication.dataMigrationHelper")
  public static void injectDataMigrationHelper(SharenApplication instance,
      DataMigrationHelper dataMigrationHelper) {
    instance.dataMigrationHelper = dataMigrationHelper;
  }
}
