package com.example.sharen.domain.usecase.invoice;

/**
 * Use Case برای ایجاد فاکتور جدید
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\t\u001a\u00020\nH\u0002Jj\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\n2\u0006\u0010\u000f\u001a\u00020\n2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u00112\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u00142\b\b\u0002\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\nH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0019\u0010\u001aR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u001b"}, d2 = {"Lcom/example/sharen/domain/usecase/invoice/CreateInvoiceUseCase;", "", "invoiceRepository", "Lcom/example/sharen/domain/repository/InvoiceRepository;", "productRepository", "Lcom/example/sharen/domain/repository/ProductRepository;", "customerRepository", "Lcom/example/sharen/domain/repository/CustomerRepository;", "(Lcom/example/sharen/domain/repository/InvoiceRepository;Lcom/example/sharen/domain/repository/ProductRepository;Lcom/example/sharen/domain/repository/CustomerRepository;)V", "generateInvoiceNumber", "", "invoke", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Invoice;", "customerId", "sellerId", "items", "", "Lcom/example/sharen/domain/model/InvoiceItem;", "discount", "", "tax", "paymentMethod", "Lcom/example/sharen/domain/model/PaymentMethod;", "notes", "invoke-eH_QyT8", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;JJLcom/example/sharen/domain/model/PaymentMethod;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class CreateInvoiceUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.InvoiceRepository invoiceRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.ProductRepository productRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.CustomerRepository customerRepository = null;
    
    @javax.inject.Inject
    public CreateInvoiceUseCase(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.InvoiceRepository invoiceRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.ProductRepository productRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.CustomerRepository customerRepository) {
        super();
    }
    
    private final java.lang.String generateInvoiceNumber() {
        return null;
    }
}