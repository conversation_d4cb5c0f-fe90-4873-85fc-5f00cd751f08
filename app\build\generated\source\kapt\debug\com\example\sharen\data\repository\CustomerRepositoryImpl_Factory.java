package com.example.sharen.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerRepositoryImpl_Factory implements Factory<CustomerRepositoryImpl> {
  @Override
  public CustomerRepositoryImpl get() {
    return newInstance();
  }

  public static CustomerRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CustomerRepositoryImpl newInstance() {
    return new CustomerRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final CustomerRepositoryImpl_Factory INSTANCE = new CustomerRepositoryImpl_Factory();
  }
}
