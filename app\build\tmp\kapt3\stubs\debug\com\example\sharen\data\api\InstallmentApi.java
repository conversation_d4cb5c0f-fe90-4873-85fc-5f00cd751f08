package com.example.sharen.data.api;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010$\n\u0002\b\u000b\bf\u0018\u00002\u00020\u0001J\u001b\u0010\u0002\u001a\u00020\u00032\b\b\u0001\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u001b\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\t\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u001b\u0010\u000b\u001a\u00020\f2\b\b\u0001\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J?\u0010\r\u001a\b\u0012\u0004\u0012\u00020\b0\u000e2\b\b\u0001\u0010\u000f\u001a\u00020\u00032\b\b\u0001\u0010\u0010\u001a\u00020\u00112\b\b\u0001\u0010\u0012\u001a\u00020\u00132\b\b\u0001\u0010\u0014\u001a\u00020\u0011H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0015J\u001b\u0010\u0016\u001a\u00020\b2\b\b\u0001\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J1\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00030\u00182\b\b\u0001\u0010\u0012\u001a\u00020\u00132\b\b\u0001\u0010\u0019\u001a\u00020\u0013H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001aJ\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\b0\u000eH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001cJ%\u0010\u001d\u001a\u00020\b2\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\u001e\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001fJ\u001b\u0010 \u001a\u00020\f2\b\b\u0001\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J%\u0010!\u001a\u00020\b2\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\t\u001a\u00020\bH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\"\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006#"}, d2 = {"Lcom/example/sharen/data/api/InstallmentApi;", "", "calculateRemainingAmount", "", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInstallment", "Lcom/example/sharen/data/model/Installment;", "installment", "(Lcom/example/sharen/data/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInstallment", "", "generateInstallmentSchedule", "", "totalAmount", "numberOfInstallments", "", "startDate", "", "intervalInDays", "(DILjava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstallment", "getInstallmentStatistics", "", "endDate", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstallments", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "payInstallment", "amount", "(JDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendReminder", "updateInstallment", "(JLcom/example/sharen/data/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface InstallmentApi {
    
    @retrofit2.http.GET(value = "installments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getInstallments(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.sharen.data.model.Installment>> $completion);
    
    @retrofit2.http.GET(value = "installments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getInstallment(@retrofit2.http.Path(value = "id")
    long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion);
    
    @retrofit2.http.POST(value = "installments")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object createInstallment(@retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion);
    
    @retrofit2.http.PUT(value = "installments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateInstallment(@retrofit2.http.Path(value = "id")
    long id, @retrofit2.http.Body
    @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion);
    
    @retrofit2.http.DELETE(value = "installments/{id}")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteInstallment(@retrofit2.http.Path(value = "id")
    long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @retrofit2.http.POST(value = "installments/{id}/pay")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object payInstallment(@retrofit2.http.Path(value = "id")
    long id, @retrofit2.http.Query(value = "amount")
    double amount, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion);
    
    @retrofit2.http.POST(value = "installments/{id}/reminder")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object sendReminder(@retrofit2.http.Path(value = "id")
    long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @retrofit2.http.GET(value = "installments/statistics")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getInstallmentStatistics(@retrofit2.http.Query(value = "startDate")
    @org.jetbrains.annotations.NotNull
    java.lang.String startDate, @retrofit2.http.Query(value = "endDate")
    @org.jetbrains.annotations.NotNull
    java.lang.String endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Double>> $completion);
    
    @retrofit2.http.GET(value = "installments/{id}/remaining")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object calculateRemainingAmount(@retrofit2.http.Path(value = "id")
    long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @retrofit2.http.POST(value = "installments/schedule")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object generateInstallmentSchedule(@retrofit2.http.Query(value = "totalAmount")
    double totalAmount, @retrofit2.http.Query(value = "numberOfInstallments")
    int numberOfInstallments, @retrofit2.http.Query(value = "startDate")
    @org.jetbrains.annotations.NotNull
    java.lang.String startDate, @retrofit2.http.Query(value = "intervalInDays")
    int intervalInDays, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.sharen.data.model.Installment>> $completion);
}