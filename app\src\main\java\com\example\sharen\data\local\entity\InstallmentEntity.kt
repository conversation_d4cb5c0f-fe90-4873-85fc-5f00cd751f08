package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.example.sharen.data.db.converter.DateConverter
import com.example.sharen.data.db.converter.InstallmentStatusConverter
import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import java.util.Date

@Entity(tableName = "installments")
@TypeConverters(DateConverter::class, InstallmentStatusConverter::class)
data class InstallmentEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val customerId: Long,
    val totalAmount: Double,
    val paidAmount: Double,
    val dueDate: Date,
    val notes: String? = null,
    val status: InstallmentStatus = InstallmentStatus.PENDING,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) {
    fun toInstallment() = Installment(
        id = id,
        customerId = customerId,
        totalAmount = totalAmount,
        paidAmount = paidAmount,
        dueDate = dueDate,
        notes = notes,
        status = status
    )

    companion object {
        fun fromInstallment(installment: Installment) = InstallmentEntity(
            id = installment.id,
            customerId = installment.customerId,
            totalAmount = installment.totalAmount,
            paidAmount = installment.paidAmount,
            dueDate = installment.dueDate,
            notes = installment.notes,
            status = installment.status
        )
    }
} 