package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Installment
import com.example.sharen.domain.model.InstallmentStatus
import java.util.Date

@Entity(
    tableName = "installments",
    foreignKeys = [
        ForeignKey(
            entity = InvoiceEntity::class,
            parentColumns = ["id"],
            childColumns = ["invoiceId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("invoiceId"),
        Index("customerId")
    ]
)
data class InstallmentEntity(
    @PrimaryKey
    val id: String,
    val invoiceId: String,
    val customerId: String,
    val amount: Long,
    val dueDate: Long,
    val paidDate: Long? = null,
    val status: String,
    val notes: String? = null,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toDomainModel(): Installment {
        return Installment(
            id = id,
            invoiceId = invoiceId,
            customerId = customerId,
            amount = amount,
            dueDate = Date(dueDate),
            paidDate = paidDate?.let { Date(it) },
            status = InstallmentStatus.valueOf(status),
            notes = notes,
            createdAt = Date(createdAt),
            updatedAt = Date(updatedAt)
        )
    }

    companion object {
        fun fromDomainModel(installment: Installment): InstallmentEntity {
            return InstallmentEntity(
                id = installment.id,
                invoiceId = installment.invoiceId,
                customerId = installment.customerId,
                amount = installment.amount,
                dueDate = installment.dueDate.time,
                paidDate = installment.paidDate?.time,
                status = installment.status.name,
                notes = installment.notes,
                createdAt = installment.createdAt.time,
                updatedAt = installment.updatedAt.time
            )
        }
    }
}