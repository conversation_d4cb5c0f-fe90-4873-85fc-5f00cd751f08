package com.example.sharen.ui.invoice;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u0012\u0012\u0004\u0012\u00020\u0002\u0012\b\u0012\u00060\u0003R\u00020\u00000\u0001:\u0002\u0014\u0015B;\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0018\u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007\u0012\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\r\u001a\u00020\t2\n\u0010\u000e\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u000f\u001a\u00020\bH\u0016J\u001c\u0010\u0010\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\bH\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\u0006\u001a\u0014\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/sharen/ui/invoice/SalesInvoiceItemAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/sharen/data/model/InvoiceItem;", "Lcom/example/sharen/ui/invoice/SalesInvoiceItemAdapter$InvoiceItemViewHolder;", "numberFormatter", "Ljava/text/NumberFormat;", "onEditQuantity", "Lkotlin/Function2;", "", "", "onRemoveItem", "Lkotlin/Function1;", "(Ljava/text/NumberFormat;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "InvoiceItemDiffCallback", "InvoiceItemViewHolder", "app_debug"})
public final class SalesInvoiceItemAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.sharen.data.model.InvoiceItem, com.example.sharen.ui.invoice.SalesInvoiceItemAdapter.InvoiceItemViewHolder> {
    @org.jetbrains.annotations.NotNull
    private final java.text.NumberFormat numberFormatter = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function2<com.example.sharen.data.model.InvoiceItem, java.lang.Integer, kotlin.Unit> onEditQuantity = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onRemoveItem = null;
    
    public SalesInvoiceItemAdapter(@org.jetbrains.annotations.NotNull
    java.text.NumberFormat numberFormatter, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super com.example.sharen.data.model.InvoiceItem, ? super java.lang.Integer, kotlin.Unit> onEditQuantity, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onRemoveItem) {
        super(null);
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.ui.invoice.SalesInvoiceItemAdapter.InvoiceItemViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.example.sharen.ui.invoice.SalesInvoiceItemAdapter.InvoiceItemViewHolder holder, int position) {
    }
    
    /**
     * برای مقایسه آیتم‌ها و بهینه‌سازی رندرینگ
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/example/sharen/ui/invoice/SalesInvoiceItemAdapter$InvoiceItemDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/sharen/data/model/InvoiceItem;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    public static final class InvoiceItemDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.sharen.data.model.InvoiceItem> {
        
        public InvoiceItemDiffCallback() {
            super();
        }
        
        @java.lang.Override
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.InvoiceItem oldItem, @org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.InvoiceItem newItem) {
            return false;
        }
        
        @java.lang.Override
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.InvoiceItem oldItem, @org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.InvoiceItem newItem) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bR\u0010\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0005\u00a8\u0006\f"}, d2 = {"Lcom/example/sharen/ui/invoice/SalesInvoiceItemAdapter$InvoiceItemViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "binding", "error/NonExistentClass", "(Lcom/example/sharen/ui/invoice/SalesInvoiceItemAdapter;Lerror/NonExistentClass;)V", "Lerror/NonExistentClass;", "bind", "", "item", "Lcom/example/sharen/data/model/InvoiceItem;", "position", "", "app_debug"})
    public final class InvoiceItemViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull
        private final error.NonExistentClass binding = null;
        
        public InvoiceItemViewHolder(@org.jetbrains.annotations.NotNull
        error.NonExistentClass binding) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull
        com.example.sharen.data.model.InvoiceItem item, int position) {
        }
    }
}