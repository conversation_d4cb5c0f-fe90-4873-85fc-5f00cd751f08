package com.example.sharen.data.repository

import com.example.sharen.data.model.Invoice
import com.example.sharen.data.model.InvoiceItem
import com.example.sharen.data.model.Payment
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * رپوزیتوری مدیریت فاکتورها
 */
interface InvoiceRepository {
    /**
     * دریافت همه فاکتورها
     */
    fun getAllInvoices(): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتور با شناسه مشخص
     */
    fun getInvoiceById(invoiceId: String): Flow<Invoice>
    
    /**
     * جستجوی فاکتورها با کلمه کلیدی
     */
    fun searchInvoices(query: String): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورهای یک مشتری
     */
    fun getInvoicesByCustomerId(customerId: String): Flow<List<Invoice>>
    
    /**
     * دریافت فاکتورهای با تاریخ مشخص
     */
    fun getInvoicesByDateRange(startDate: Date, endDate: Date): Flow<List<Invoice>>
    
    /**
     * ایجاد فاکتور جدید
     */
    suspend fun createInvoice(invoice: Invoice, items: List<InvoiceItem>): Result<Invoice>
    
    /**
     * بروزرسانی اطلاعات فاکتور
     */
    suspend fun updateInvoice(invoice: Invoice): Result<Invoice>
    
    /**
     * حذف فاکتور
     */
    suspend fun deleteInvoice(invoiceId: String): Result<Unit>
    
    /**
     * تغییر وضعیت فاکتور
     */
    suspend fun updateInvoiceStatus(invoiceId: String, newStatus: com.example.sharen.data.model.InvoiceStatus): Result<Invoice>
    
    /**
     * ثبت پرداخت برای فاکتور
     */
    suspend fun addPayment(payment: Payment): Result<Payment>
    
    /**
     * دریافت پرداخت‌های یک فاکتور
     */
    fun getPaymentsByInvoiceId(invoiceId: String): Flow<List<Payment>>
    
    /**
     * دریافت اقلام یک فاکتور
     */
    fun getInvoiceItems(invoiceId: String): Flow<List<InvoiceItem>>
    
    /**
     * افزودن آیتم به فاکتور
     */
    suspend fun addInvoiceItem(invoiceId: String, item: InvoiceItem): Result<InvoiceItem>
    
    /**
     * حذف آیتم از فاکتور
     */
    suspend fun removeInvoiceItem(invoiceId: String, itemId: String): Result<Unit>
    
    /**
     * به‌روزرسانی آیتم فاکتور
     */
    suspend fun updateInvoiceItem(invoiceId: String, item: InvoiceItem): Result<InvoiceItem>
    
    /**
     * محاسبه مجدد مبالغ فاکتور
     */
    suspend fun recalculateInvoice(invoiceId: String): Result<Invoice>
    
    /**
     * دریافت تعداد کل فاکتورها
     */
    fun getInvoiceCount(): Flow<Int>
    
    /**
     * دریافت مجموع فروش
     */
    fun getTotalSales(): Flow<Long>
    
    /**
     * دریافت فاکتورهای اخیر (مثلاً ۱۰ مورد آخر)
     */
    fun getRecentInvoices(limit: Int = 10): Flow<List<Invoice>>
    
    /**
     * دریافت همه پرداخت‌ها
     */
    fun getAllPayments(): Flow<List<Payment>>
    
    /**
     * دریافت پرداخت با شناسه مشخص
     */
    fun getPaymentById(paymentId: String): Flow<Payment?>
} 