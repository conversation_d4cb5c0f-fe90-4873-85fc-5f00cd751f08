package com.example.sharen.domain.usecase.product;

import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetLowStockProductsUseCase_Factory implements Factory<GetLowStockProductsUseCase> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public GetLowStockProductsUseCase_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public GetLowStockProductsUseCase get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static GetLowStockProductsUseCase_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new GetLowStockProductsUseCase_Factory(productRepositoryProvider);
  }

  public static GetLowStockProductsUseCase newInstance(ProductRepository productRepository) {
    return new GetLowStockProductsUseCase(productRepository);
  }
}
