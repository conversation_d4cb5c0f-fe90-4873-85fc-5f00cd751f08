// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCustomerBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final MaterialButton btnCall;

  @NonNull
  public final MaterialButton btnMessage;

  @NonNull
  public final MaterialButton btnNewInvoice;

  @NonNull
  public final ImageView ivCustomerAvatar;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvCustomerPhone;

  @NonNull
  public final TextView tvDebtAmount;

  @NonNull
  public final TextView tvLastPurchase;

  private ItemCustomerBinding(@NonNull CardView rootView, @NonNull MaterialButton btnCall,
      @NonNull MaterialButton btnMessage, @NonNull MaterialButton btnNewInvoice,
      @NonNull ImageView ivCustomerAvatar, @NonNull TextView tvCustomerName,
      @NonNull TextView tvCustomerPhone, @NonNull TextView tvDebtAmount,
      @NonNull TextView tvLastPurchase) {
    this.rootView = rootView;
    this.btnCall = btnCall;
    this.btnMessage = btnMessage;
    this.btnNewInvoice = btnNewInvoice;
    this.ivCustomerAvatar = ivCustomerAvatar;
    this.tvCustomerName = tvCustomerName;
    this.tvCustomerPhone = tvCustomerPhone;
    this.tvDebtAmount = tvDebtAmount;
    this.tvLastPurchase = tvLastPurchase;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCustomerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCustomerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_customer, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCustomerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_call;
      MaterialButton btnCall = ViewBindings.findChildViewById(rootView, id);
      if (btnCall == null) {
        break missingId;
      }

      id = R.id.btn_message;
      MaterialButton btnMessage = ViewBindings.findChildViewById(rootView, id);
      if (btnMessage == null) {
        break missingId;
      }

      id = R.id.btn_new_invoice;
      MaterialButton btnNewInvoice = ViewBindings.findChildViewById(rootView, id);
      if (btnNewInvoice == null) {
        break missingId;
      }

      id = R.id.iv_customer_avatar;
      ImageView ivCustomerAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivCustomerAvatar == null) {
        break missingId;
      }

      id = R.id.tv_customer_name;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tv_customer_phone;
      TextView tvCustomerPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerPhone == null) {
        break missingId;
      }

      id = R.id.tv_debt_amount;
      TextView tvDebtAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvDebtAmount == null) {
        break missingId;
      }

      id = R.id.tv_last_purchase;
      TextView tvLastPurchase = ViewBindings.findChildViewById(rootView, id);
      if (tvLastPurchase == null) {
        break missingId;
      }

      return new ItemCustomerBinding((CardView) rootView, btnCall, btnMessage, btnNewInvoice,
          ivCustomerAvatar, tvCustomerName, tvCustomerPhone, tvDebtAmount, tvLastPurchase);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
