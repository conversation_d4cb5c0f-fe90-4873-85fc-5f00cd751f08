<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_payment_details" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_payment_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_payment_details_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="11" startOffset="8" endLine="18" endOffset="47"/></Target><Target id="@+id/amountTextView" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="40" endOffset="51"/></Target><Target id="@+id/statusChip" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="42" startOffset="12" endLine="50" endOffset="40"/></Target><Target id="@+id/dateTextView" view="TextView"><Expressions/><location startLine="52" startOffset="12" endLine="61" endOffset="48"/></Target><Target id="@+id/methodTextView" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="72" endOffset="47"/></Target><Target id="@+id/referenceTextView" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="83" endOffset="49"/></Target><Target id="@+id/notesTextView" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="94" endOffset="54"/></Target></Targets></Layout>