package com.example.sharen.ui.installment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.navigation.NavDirections;
import com.example.sharen.R;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class InstallmentListFragmentDirections {
  private InstallmentListFragmentDirections() {
  }

  @NonNull
  public static ActionInstallmentListToInstallmentDetail actionInstallmentListToInstallmentDetail(
      long installmentId) {
    return new ActionInstallmentListToInstallmentDetail(installmentId);
  }

  @NonNull
  public static ActionInstallmentListToInstallmentEdit actionInstallmentListToInstallmentEdit() {
    return new ActionInstallmentListToInstallmentEdit();
  }

  public static class ActionInstallmentListToInstallmentDetail implements NavDirections {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    private ActionInstallmentListToInstallmentDetail(long installmentId) {
      this.arguments.put("installmentId", installmentId);
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionInstallmentListToInstallmentDetail setInstallmentId(long installmentId) {
      this.arguments.put("installmentId", installmentId);
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    @NonNull
    public Bundle getArguments() {
      Bundle __result = new Bundle();
      if (arguments.containsKey("installmentId")) {
        long installmentId = (long) arguments.get("installmentId");
        __result.putLong("installmentId", installmentId);
      }
      return __result;
    }

    @Override
    public int getActionId() {
      return R.id.action_installmentList_to_installmentDetail;
    }

    @SuppressWarnings("unchecked")
    public long getInstallmentId() {
      return (long) arguments.get("installmentId");
    }

    @Override
    public boolean equals(Object object) {
      if (this == object) {
          return true;
      }
      if (object == null || getClass() != object.getClass()) {
          return false;
      }
      ActionInstallmentListToInstallmentDetail that = (ActionInstallmentListToInstallmentDetail) object;
      if (arguments.containsKey("installmentId") != that.arguments.containsKey("installmentId")) {
        return false;
      }
      if (getInstallmentId() != that.getInstallmentId()) {
        return false;
      }
      if (getActionId() != that.getActionId()) {
        return false;
      }
      return true;
    }

    @Override
    public int hashCode() {
      int result = 1;
      result = 31 * result + (int)(getInstallmentId() ^ (getInstallmentId() >>> 32));
      result = 31 * result + getActionId();
      return result;
    }

    @Override
    public String toString() {
      return "ActionInstallmentListToInstallmentDetail(actionId=" + getActionId() + "){"
          + "installmentId=" + getInstallmentId()
          + "}";
    }
  }

  public static class ActionInstallmentListToInstallmentEdit implements NavDirections {
    private final HashMap arguments = new HashMap();

    private ActionInstallmentListToInstallmentEdit() {
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public ActionInstallmentListToInstallmentEdit setInstallmentId(long installmentId) {
      this.arguments.put("installmentId", installmentId);
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    @NonNull
    public Bundle getArguments() {
      Bundle __result = new Bundle();
      if (arguments.containsKey("installmentId")) {
        long installmentId = (long) arguments.get("installmentId");
        __result.putLong("installmentId", installmentId);
      } else {
        __result.putLong("installmentId", 0L);
      }
      return __result;
    }

    @Override
    public int getActionId() {
      return R.id.action_installmentList_to_installmentEdit;
    }

    @SuppressWarnings("unchecked")
    public long getInstallmentId() {
      return (long) arguments.get("installmentId");
    }

    @Override
    public boolean equals(Object object) {
      if (this == object) {
          return true;
      }
      if (object == null || getClass() != object.getClass()) {
          return false;
      }
      ActionInstallmentListToInstallmentEdit that = (ActionInstallmentListToInstallmentEdit) object;
      if (arguments.containsKey("installmentId") != that.arguments.containsKey("installmentId")) {
        return false;
      }
      if (getInstallmentId() != that.getInstallmentId()) {
        return false;
      }
      if (getActionId() != that.getActionId()) {
        return false;
      }
      return true;
    }

    @Override
    public int hashCode() {
      int result = 1;
      result = 31 * result + (int)(getInstallmentId() ^ (getInstallmentId() >>> 32));
      result = 31 * result + getActionId();
      return result;
    }

    @Override
    public String toString() {
      return "ActionInstallmentListToInstallmentEdit(actionId=" + getActionId() + "){"
          + "installmentId=" + getInstallmentId()
          + "}";
    }
  }
}
