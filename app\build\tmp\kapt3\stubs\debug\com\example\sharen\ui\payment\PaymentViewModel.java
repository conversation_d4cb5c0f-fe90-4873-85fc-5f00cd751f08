package com.example.sharen.ui.payment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010$\u001a\u00020%J\u000e\u0010&\u001a\u00020%2\u0006\u0010\'\u001a\u00020\tJ\u000e\u0010(\u001a\u00020%2\u0006\u0010)\u001a\u00020\u0014J\u000e\u0010*\u001a\u00020%2\u0006\u0010\'\u001a\u00020\tJ\u0016\u0010+\u001a\u00020%2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020-J\u000e\u0010/\u001a\u00020%2\u0006\u00100\u001a\u00020\tJ\u0016\u00101\u001a\u00020%2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020-J\u000e\u00102\u001a\u00020%2\u0006\u00103\u001a\u000204J\u0006\u00105\u001a\u00020%J\u0006\u00106\u001a\u00020%J\u0016\u00107\u001a\u00020%2\u0006\u0010\'\u001a\u00020\t2\u0006\u00108\u001a\u00020\tJ\u000e\u00109\u001a\u00020%2\u0006\u0010:\u001a\u00020\tJ*\u0010;\u001a\u00020%2\u0006\u0010<\u001a\u00020=2\u0006\u0010>\u001a\u00020?2\b\u0010@\u001a\u0004\u0018\u00010\t2\b\u0010A\u001a\u0004\u0018\u00010\tJ\u000e\u0010B\u001a\u00020%2\u0006\u0010)\u001a\u00020\u0014R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0019\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u000e0\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000e0\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0018R\u001d\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140\u00130\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001f\u00a8\u0006C"}, d2 = {"Lcom/example/sharen/ui/payment/PaymentViewModel;", "Landroidx/lifecycle/ViewModel;", "invoiceRepository", "Lcom/example/sharen/data/repository/InvoiceRepository;", "paymentRepository", "Lcom/example/sharen/data/repository/PaymentRepository;", "(Lcom/example/sharen/data/repository/InvoiceRepository;Lcom/example/sharen/data/repository/PaymentRepository;)V", "_error", "Landroidx/lifecycle/MutableLiveData;", "", "_invoiceDetails", "Lcom/example/sharen/data/model/Invoice;", "_invoiceId", "_isLoading", "", "_loading", "Lkotlinx/coroutines/flow/MutableStateFlow;", "_paymentResult", "_payments", "", "Lcom/example/sharen/data/model/Payment;", "error", "Landroidx/lifecycle/LiveData;", "getError", "()Landroidx/lifecycle/LiveData;", "invoiceDetails", "getInvoiceDetails", "isLoading", "loading", "Lkotlinx/coroutines/flow/StateFlow;", "getLoading", "()Lkotlinx/coroutines/flow/StateFlow;", "paymentResult", "getPaymentResult", "payments", "getPayments", "clearError", "", "confirmPayment", "paymentId", "createPayment", "payment", "deletePayment", "getPaymentStatistics", "startDate", "Ljava/util/Date;", "endDate", "getPaymentsByCustomer", "customerId", "getPaymentsByDateRange", "getPaymentsByStatus", "status", "Lcom/example/sharen/data/model/PaymentStatus;", "loadInvoiceDetails", "loadPayments", "rejectPayment", "reason", "setInvoiceId", "invoiceId", "submitPayment", "amount", "", "method", "Lcom/example/sharen/data/model/PaymentMethod;", "referenceNumber", "notes", "updatePayment", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class PaymentViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.repository.InvoiceRepository invoiceRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.repository.PaymentRepository paymentRepository = null;
    @org.jetbrains.annotations.Nullable
    private java.lang.String _invoiceId;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<com.example.sharen.data.model.Invoice> _invoiceDetails = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<com.example.sharen.data.model.Invoice> invoiceDetails = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _paymentResult = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.Boolean> paymentResult = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharen.data.model.Payment>> _payments = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.data.model.Payment>> payments = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _loading = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> loading = null;
    
    @javax.inject.Inject
    public PaymentViewModel(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.repository.InvoiceRepository invoiceRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.repository.PaymentRepository paymentRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<com.example.sharen.data.model.Invoice> getInvoiceDetails() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getPaymentResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.data.model.Payment>> getPayments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getLoading() {
        return null;
    }
    
    public final void setInvoiceId(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId) {
    }
    
    public final void loadInvoiceDetails() {
    }
    
    public final void submitPayment(long amount, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentMethod method, @org.jetbrains.annotations.Nullable
    java.lang.String referenceNumber, @org.jetbrains.annotations.Nullable
    java.lang.String notes) {
    }
    
    public final void loadPayments() {
    }
    
    public final void createPayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment) {
    }
    
    public final void updatePayment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Payment payment) {
    }
    
    public final void deletePayment(@org.jetbrains.annotations.NotNull
    java.lang.String paymentId) {
    }
    
    public final void confirmPayment(@org.jetbrains.annotations.NotNull
    java.lang.String paymentId) {
    }
    
    public final void rejectPayment(@org.jetbrains.annotations.NotNull
    java.lang.String paymentId, @org.jetbrains.annotations.NotNull
    java.lang.String reason) {
    }
    
    public final void getPaymentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
    }
    
    public final void getPaymentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void getPaymentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentStatus status) {
    }
    
    public final void getPaymentStatistics(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void clearError() {
    }
}