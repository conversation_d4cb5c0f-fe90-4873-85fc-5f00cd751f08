package com.example.sharen.data.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J*\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rJ!\u0010\u000e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u000f2\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\rJ\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u000fH\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0012J\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00140\u000fH\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0012J%\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00140\u000f2\u0006\u0010\u0016\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\rJ*\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0018\u0010\u0007\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u0019"}, d2 = {"Lcom/example/sharen/data/repository/CustomerRepository;", "", "createCustomer", "Lkotlin/Result;", "Lcom/example/sharen/data/model/Customer;", "customer", "createCustomer-gIAlu-s", "(Lcom/example/sharen/data/model/Customer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteCustomer", "", "customerId", "", "deleteCustomer-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCustomerById", "Lkotlinx/coroutines/flow/Flow;", "getCustomerCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCustomers", "", "searchCustomers", "query", "updateCustomer", "updateCustomer-gIAlu-s", "app_debug"})
public abstract interface CustomerRepository {
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomers(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Customer>>> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object searchCustomers(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Customer>>> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomerById(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Customer>> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCustomerCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<java.lang.Integer>> $completion);
}