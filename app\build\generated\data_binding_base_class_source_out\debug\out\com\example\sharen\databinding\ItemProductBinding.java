// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProductBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageView ivProductImage;

  @NonNull
  public final TextView tvProductCategory;

  @NonNull
  public final TextView tvProductCode;

  @NonNull
  public final TextView tvProductName;

  @NonNull
  public final TextView tvProductPrice;

  @NonNull
  public final TextView tvStockStatus;

  private ItemProductBinding(@NonNull MaterialCardView rootView, @NonNull ImageView ivProductImage,
      @NonNull TextView tvProductCategory, @NonNull TextView tvProductCode,
      @NonNull TextView tvProductName, @NonNull TextView tvProductPrice,
      @NonNull TextView tvStockStatus) {
    this.rootView = rootView;
    this.ivProductImage = ivProductImage;
    this.tvProductCategory = tvProductCategory;
    this.tvProductCode = tvProductCode;
    this.tvProductName = tvProductName;
    this.tvProductPrice = tvProductPrice;
    this.tvStockStatus = tvStockStatus;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProductBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProductBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_product, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProductBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivProductImage;
      ImageView ivProductImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProductImage == null) {
        break missingId;
      }

      id = R.id.tvProductCategory;
      TextView tvProductCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvProductCategory == null) {
        break missingId;
      }

      id = R.id.tvProductCode;
      TextView tvProductCode = ViewBindings.findChildViewById(rootView, id);
      if (tvProductCode == null) {
        break missingId;
      }

      id = R.id.tvProductName;
      TextView tvProductName = ViewBindings.findChildViewById(rootView, id);
      if (tvProductName == null) {
        break missingId;
      }

      id = R.id.tvProductPrice;
      TextView tvProductPrice = ViewBindings.findChildViewById(rootView, id);
      if (tvProductPrice == null) {
        break missingId;
      }

      id = R.id.tvStockStatus;
      TextView tvStockStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStockStatus == null) {
        break missingId;
      }

      return new ItemProductBinding((MaterialCardView) rootView, ivProductImage, tvProductCategory,
          tvProductCode, tvProductName, tvProductPrice, tvStockStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
