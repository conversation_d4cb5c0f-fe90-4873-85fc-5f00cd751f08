<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_payment_list" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_payment_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_payment_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="38" endOffset="53"/></Target><Target id="@+id/swipeRefreshLayout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="6" startOffset="4" endLine="18" endOffset="59"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="11" startOffset="8" endLine="16" endOffset="35"/></Target><Target id="@+id/fabAddPayment" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="42"/></Target><Target id="@+id/emptyView" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="36" endOffset="35"/></Target></Targets></Layout>