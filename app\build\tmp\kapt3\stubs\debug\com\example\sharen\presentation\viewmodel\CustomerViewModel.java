package com.example.sharen.presentation.viewmodel;

/**
 * ViewModel برای مدیریت مشتریان
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u000f\b\u0007\u0018\u00002\u00020\u0001BG\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\u0002\u0010\u0012J@\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020!2\u0006\u0010#\u001a\u00020!2\n\b\u0002\u0010$\u001a\u0004\u0018\u00010!2\b\b\u0002\u0010%\u001a\u00020&2\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010!J\u000e\u0010(\u001a\u00020\u001f2\u0006\u0010)\u001a\u00020!J\u0006\u0010*\u001a\u00020\u001fJ\u000e\u0010+\u001a\u00020\u001f2\u0006\u0010)\u001a\u00020!J\u0006\u0010,\u001a\u00020\u001fJ\u0006\u0010-\u001a\u00020\u001fJ\u0006\u0010.\u001a\u00020\u001fJ\u000e\u0010/\u001a\u00020\u001f2\u0006\u00100\u001a\u00020!J\u000e\u00101\u001a\u00020\u001f2\u0006\u00102\u001a\u00020\u0016J\u0016\u00103\u001a\u00020\u001f2\u0006\u0010)\u001a\u00020!2\u0006\u00104\u001a\u00020&R\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0017\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00150\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001bR\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00065"}, d2 = {"Lcom/example/sharen/presentation/viewmodel/CustomerViewModel;", "Lcom/example/sharen/core/base/BaseViewModel;", "getAllCustomersUseCase", "Lcom/example/sharen/domain/usecase/customer/GetAllCustomersUseCase;", "getCustomerByIdUseCase", "Lcom/example/sharen/domain/usecase/customer/GetCustomerByIdUseCase;", "searchCustomersUseCase", "Lcom/example/sharen/domain/usecase/customer/SearchCustomersUseCase;", "addCustomerUseCase", "Lcom/example/sharen/domain/usecase/customer/AddCustomerUseCase;", "updateCustomerUseCase", "Lcom/example/sharen/domain/usecase/customer/UpdateCustomerUseCase;", "deleteCustomerUseCase", "Lcom/example/sharen/domain/usecase/customer/DeleteCustomerUseCase;", "getDebtorCustomersUseCase", "Lcom/example/sharen/domain/usecase/customer/GetDebtorCustomersUseCase;", "updateCustomerCreditUseCase", "Lcom/example/sharen/domain/usecase/customer/UpdateCustomerCreditUseCase;", "(Lcom/example/sharen/domain/usecase/customer/GetAllCustomersUseCase;Lcom/example/sharen/domain/usecase/customer/GetCustomerByIdUseCase;Lcom/example/sharen/domain/usecase/customer/SearchCustomersUseCase;Lcom/example/sharen/domain/usecase/customer/AddCustomerUseCase;Lcom/example/sharen/domain/usecase/customer/UpdateCustomerUseCase;Lcom/example/sharen/domain/usecase/customer/DeleteCustomerUseCase;Lcom/example/sharen/domain/usecase/customer/GetDebtorCustomersUseCase;Lcom/example/sharen/domain/usecase/customer/UpdateCustomerCreditUseCase;)V", "_customers", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/example/sharen/domain/model/Customer;", "_selectedCustomer", "customers", "Lkotlinx/coroutines/flow/StateFlow;", "getCustomers", "()Lkotlinx/coroutines/flow/StateFlow;", "selectedCustomer", "getSelectedCustomer", "addCustomer", "", "userId", "", "name", "phone", "address", "creditLimit", "", "notes", "deleteCustomer", "customerId", "getClearCustomers", "getCustomerById", "getDebtorCustomers", "loadCustomers", "refreshCustomers", "searchCustomers", "query", "updateCustomer", "customer", "updateCustomerCredit", "newCreditLimit", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class CustomerViewModel extends com.example.sharen.core.base.BaseViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.GetAllCustomersUseCase getAllCustomersUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.GetCustomerByIdUseCase getCustomerByIdUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.SearchCustomersUseCase searchCustomersUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.AddCustomerUseCase addCustomerUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.UpdateCustomerUseCase updateCustomerUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.DeleteCustomerUseCase deleteCustomerUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.GetDebtorCustomersUseCase getDebtorCustomersUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.usecase.customer.UpdateCustomerCreditUseCase updateCustomerCreditUseCase = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharen.domain.model.Customer>> _customers = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.domain.model.Customer>> customers = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.sharen.domain.model.Customer> _selectedCustomer = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.example.sharen.domain.model.Customer> selectedCustomer = null;
    
    @javax.inject.Inject
    public CustomerViewModel(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.GetAllCustomersUseCase getAllCustomersUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.GetCustomerByIdUseCase getCustomerByIdUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.SearchCustomersUseCase searchCustomersUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.AddCustomerUseCase addCustomerUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.UpdateCustomerUseCase updateCustomerUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.DeleteCustomerUseCase deleteCustomerUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.GetDebtorCustomersUseCase getDebtorCustomersUseCase, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.usecase.customer.UpdateCustomerCreditUseCase updateCustomerCreditUseCase) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.domain.model.Customer>> getCustomers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.example.sharen.domain.model.Customer> getSelectedCustomer() {
        return null;
    }
    
    /**
     * بارگذاری لیست مشتریان
     */
    public final void loadCustomers() {
    }
    
    /**
     * جستجوی مشتریان
     */
    public final void searchCustomers(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
    }
    
    /**
     * دریافت مشتری با شناسه
     */
    public final void getCustomerById(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
    }
    
    /**
     * افزودن مشتری جدید
     */
    public final void addCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String phone, @org.jetbrains.annotations.Nullable
    java.lang.String address, long creditLimit, @org.jetbrains.annotations.Nullable
    java.lang.String notes) {
    }
    
    /**
     * بروزرسانی مشتری
     */
    public final void updateCustomer(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.model.Customer customer) {
    }
    
    /**
     * حذف مشتری
     */
    public final void deleteCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
    }
    
    /**
     * بروزرسانی اعتبار مشتری
     */
    public final void updateCustomerCredit(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, long newCreditLimit) {
    }
    
    /**
     * بروزرسانی داده‌ها
     */
    public final void refreshCustomers() {
    }
    
    /**
     * فیلتر مشتریان بدهکار
     */
    public final void getDebtorCustomers() {
    }
    
    /**
     * فیلتر مشتریان بدون بدهی
     */
    public final void getClearCustomers() {
    }
}