package com.example.sharen.data.remote

import com.example.sharen.data.model.Payment
import com.example.sharen.data.model.PaymentStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PaymentRemoteDataSource @Inject constructor(
    private val apiService: SupabaseApiService
) {
    suspend fun createPayment(payment: Payment): Result<Payment> = runCatching {
        apiService.createPayment(payment).body() ?: throw Exception("Failed to create payment")
    }
    
    suspend fun updatePayment(payment: Payment): Result<Payment> = runCatching {
        apiService.updatePayment(payment.id, payment).body() ?: throw Exception("Failed to update payment")
    }
    
    suspend fun deletePayment(paymentId: String): Result<Unit> = runCatching {
        apiService.deletePayment(paymentId).body() ?: throw Exception("Failed to delete payment")
    }
    
    suspend fun getPayment(paymentId: String): Result<Payment> = runCatching {
        apiService.getPayment(paymentId).body() ?: throw Exception("Payment not found")
    }
    
    fun getAllPayments(): Flow<List<Payment>> = flow {
        val response = apiService.getPayments()
        if (response.isSuccessful) {
            emit(response.body() ?: emptyList())
        } else {
            throw Exception("Failed to fetch payments")
        }
    }
    
    fun getPaymentsByCustomer(customerId: String): Flow<List<Payment>> = flow {
        val response = apiService.getPayments()
        if (response.isSuccessful) {
            val payments = response.body() ?: emptyList()
            emit(payments.filter { it.customerId == customerId })
        } else {
            throw Exception("Failed to fetch customer payments")
        }
    }
    
    fun getPaymentsByDateRange(startDate: Date, endDate: Date): Flow<List<Payment>> = flow {
        val response = apiService.getPayments()
        if (response.isSuccessful) {
            val payments = response.body() ?: emptyList()
            emit(payments.filter { 
                it.date in startDate..endDate 
            })
        } else {
            throw Exception("Failed to fetch payments by date range")
        }
    }
    
    fun getPaymentsByStatus(status: PaymentStatus): Flow<List<Payment>> = flow {
        val response = apiService.getPayments()
        if (response.isSuccessful) {
            val payments = response.body() ?: emptyList()
            emit(payments.filter { it.status == status })
        } else {
            throw Exception("Failed to fetch payments by status")
        }
    }
    
    suspend fun confirmPayment(paymentId: String): Result<Payment> = runCatching {
        val payment = getPayment(paymentId).getOrThrow()
        val updatedPayment = payment.copy(status = PaymentStatus.CONFIRMED)
        updatePayment(updatedPayment).getOrThrow()
    }
    
    suspend fun rejectPayment(paymentId: String, reason: String): Result<Payment> = runCatching {
        val payment = getPayment(paymentId).getOrThrow()
        val updatedPayment = payment.copy(
            status = PaymentStatus.REJECTED,
            notes = reason
        )
        updatePayment(updatedPayment).getOrThrow()
    }
    
    suspend fun getTotalPaymentsByDateRange(startDate: Date, endDate: Date): Result<Double> = runCatching {
        val payments = getPaymentsByDateRange(startDate, endDate).collect { paymentList ->
            paymentList.sumOf { it.amount }
        }
        payments
    }
    
    suspend fun getCustomerTotalPayments(customerId: String): Result<Double> = runCatching {
        val payments = getPaymentsByCustomer(customerId).collect { paymentList ->
            paymentList.sumOf { it.amount }
        }
        payments
    }
    
    suspend fun getPaymentStatistics(startDate: Date, endDate: Date): Result<Map<String, Any>> = runCatching {
        val payments = getPaymentsByDateRange(startDate, endDate).collect { paymentList ->
            mapOf(
                "totalAmount" to paymentList.sumOf { it.amount },
                "totalCount" to paymentList.size,
                "confirmedCount" to paymentList.count { it.status == PaymentStatus.CONFIRMED },
                "rejectedCount" to paymentList.count { it.status == PaymentStatus.REJECTED },
                "pendingCount" to paymentList.count { it.status == PaymentStatus.PENDING }
            )
        }
        payments
    }
} 