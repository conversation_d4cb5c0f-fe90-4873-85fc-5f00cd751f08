<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/tvCurrentStock"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/current_stock"
        android:textSize="14sp"
        android:layout_marginBottom="8dp" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/new_stock"
        app:startIconDrawable="@android:drawable/ic_menu_agenda">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etNewStock"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number" />

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout> 