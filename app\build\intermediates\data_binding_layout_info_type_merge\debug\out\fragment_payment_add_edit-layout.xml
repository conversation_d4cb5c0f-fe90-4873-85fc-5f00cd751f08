<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_payment_add_edit" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_payment_add_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_payment_add_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="166" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="8" endLine="16" endOffset="47"/></Target><Target id="@+id/textInputLayoutAmount" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="31" startOffset="12" endLine="45" endOffset="67"/></Target><Target id="@+id/editTextAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="39" startOffset="16" endLine="43" endOffset="55"/></Target><Target id="@+id/textInputLayoutPaymentMethod" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="47" startOffset="12" endLine="61" endOffset="67"/></Target><Target id="@+id/spinnerPaymentMethod" view="AutoCompleteTextView"><Expressions/><location startLine="55" startOffset="16" endLine="59" endOffset="46"/></Target><Target id="@+id/textViewPaymentMethodError" view="TextView"><Expressions/><location startLine="63" startOffset="12" endLine="70" endOffset="43"/></Target><Target id="@+id/textInputLayoutPaymentStatus" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="72" startOffset="12" endLine="86" endOffset="67"/></Target><Target id="@+id/spinnerPaymentStatus" view="AutoCompleteTextView"><Expressions/><location startLine="80" startOffset="16" endLine="84" endOffset="46"/></Target><Target id="@+id/textViewPaymentStatusError" view="TextView"><Expressions/><location startLine="88" startOffset="12" endLine="95" endOffset="43"/></Target><Target id="@+id/textInputLayoutReference" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="97" startOffset="12" endLine="111" endOffset="67"/></Target><Target id="@+id/editTextReference" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="105" startOffset="16" endLine="109" endOffset="46"/></Target><Target id="@+id/textInputLayoutNotes" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="113" startOffset="12" endLine="129" endOffset="67"/></Target><Target id="@+id/editTextNotes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="121" startOffset="16" endLine="127" endOffset="39"/></Target><Target id="@+id/buttonCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="136" startOffset="16" endLine="143" endOffset="51"/></Target><Target id="@+id/buttonSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="145" startOffset="16" endLine="151" endOffset="49"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="159" startOffset="4" endLine="164" endOffset="35"/></Target></Targets></Layout>