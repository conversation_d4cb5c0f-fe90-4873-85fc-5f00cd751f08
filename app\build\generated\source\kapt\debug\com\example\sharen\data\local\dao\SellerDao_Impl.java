package com.example.sharen.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.sharen.data.local.entity.SellerEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SellerDao_Impl implements SellerDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SellerEntity> __insertionAdapterOfSellerEntity;

  private final EntityDeletionOrUpdateAdapter<SellerEntity> __deletionAdapterOfSellerEntity;

  private final EntityDeletionOrUpdateAdapter<SellerEntity> __updateAdapterOfSellerEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSellers;

  public SellerDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSellerEntity = new EntityInsertionAdapter<SellerEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `sellers` (`id`,`userId`,`name`,`phone`,`address`,`email`,`totalSales`,`totalCommission`,`totalDebt`,`commissionRate`,`isActive`,`bankAccount`,`bankName`,`bankSheba`,`createdAt`,`updatedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SellerEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUserId());
        }
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhone());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAddress());
        }
        if (entity.getEmail() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getEmail());
        }
        statement.bindLong(7, entity.getTotalSales());
        statement.bindLong(8, entity.getTotalCommission());
        statement.bindLong(9, entity.getTotalDebt());
        statement.bindLong(10, entity.getCommissionRate());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(11, _tmp);
        if (entity.getBankAccount() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getBankAccount());
        }
        if (entity.getBankName() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getBankName());
        }
        if (entity.getBankSheba() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getBankSheba());
        }
        statement.bindLong(15, entity.getCreatedAt());
        statement.bindLong(16, entity.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfSellerEntity = new EntityDeletionOrUpdateAdapter<SellerEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `sellers` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SellerEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfSellerEntity = new EntityDeletionOrUpdateAdapter<SellerEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `sellers` SET `id` = ?,`userId` = ?,`name` = ?,`phone` = ?,`address` = ?,`email` = ?,`totalSales` = ?,`totalCommission` = ?,`totalDebt` = ?,`commissionRate` = ?,`isActive` = ?,`bankAccount` = ?,`bankName` = ?,`bankSheba` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SellerEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getUserId());
        }
        if (entity.getName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getPhone());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAddress());
        }
        if (entity.getEmail() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getEmail());
        }
        statement.bindLong(7, entity.getTotalSales());
        statement.bindLong(8, entity.getTotalCommission());
        statement.bindLong(9, entity.getTotalDebt());
        statement.bindLong(10, entity.getCommissionRate());
        final int _tmp = entity.isActive() ? 1 : 0;
        statement.bindLong(11, _tmp);
        if (entity.getBankAccount() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getBankAccount());
        }
        if (entity.getBankName() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getBankName());
        }
        if (entity.getBankSheba() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getBankSheba());
        }
        statement.bindLong(15, entity.getCreatedAt());
        statement.bindLong(16, entity.getUpdatedAt());
        if (entity.getId() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteAllSellers = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM sellers";
        return _query;
      }
    };
  }

  @Override
  public Object insertSeller(final SellerEntity seller,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSellerEntity.insert(seller);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertSellers(final List<SellerEntity> sellers,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSellerEntity.insert(sellers);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSeller(final SellerEntity seller,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfSellerEntity.handle(seller);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSeller(final SellerEntity seller,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSellerEntity.handle(seller);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllSellers(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSellers.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSellers.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SellerEntity>> getAllSellers() {
    final String _sql = "SELECT * FROM sellers";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"sellers"}, new Callable<List<SellerEntity>>() {
      @Override
      @NonNull
      public List<SellerEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfTotalSales = CursorUtil.getColumnIndexOrThrow(_cursor, "totalSales");
          final int _cursorIndexOfTotalCommission = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCommission");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfCommissionRate = CursorUtil.getColumnIndexOrThrow(_cursor, "commissionRate");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfBankAccount = CursorUtil.getColumnIndexOrThrow(_cursor, "bankAccount");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankSheba = CursorUtil.getColumnIndexOrThrow(_cursor, "bankSheba");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<SellerEntity> _result = new ArrayList<SellerEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SellerEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final long _tmpTotalSales;
            _tmpTotalSales = _cursor.getLong(_cursorIndexOfTotalSales);
            final long _tmpTotalCommission;
            _tmpTotalCommission = _cursor.getLong(_cursorIndexOfTotalCommission);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final long _tmpCommissionRate;
            _tmpCommissionRate = _cursor.getLong(_cursorIndexOfCommissionRate);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpBankAccount;
            if (_cursor.isNull(_cursorIndexOfBankAccount)) {
              _tmpBankAccount = null;
            } else {
              _tmpBankAccount = _cursor.getString(_cursorIndexOfBankAccount);
            }
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            final String _tmpBankSheba;
            if (_cursor.isNull(_cursorIndexOfBankSheba)) {
              _tmpBankSheba = null;
            } else {
              _tmpBankSheba = _cursor.getString(_cursorIndexOfBankSheba);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new SellerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpEmail,_tmpTotalSales,_tmpTotalCommission,_tmpTotalDebt,_tmpCommissionRate,_tmpIsActive,_tmpBankAccount,_tmpBankName,_tmpBankSheba,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSellerById(final String id,
      final Continuation<? super SellerEntity> $completion) {
    final String _sql = "SELECT * FROM sellers WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SellerEntity>() {
      @Override
      @Nullable
      public SellerEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfTotalSales = CursorUtil.getColumnIndexOrThrow(_cursor, "totalSales");
          final int _cursorIndexOfTotalCommission = CursorUtil.getColumnIndexOrThrow(_cursor, "totalCommission");
          final int _cursorIndexOfTotalDebt = CursorUtil.getColumnIndexOrThrow(_cursor, "totalDebt");
          final int _cursorIndexOfCommissionRate = CursorUtil.getColumnIndexOrThrow(_cursor, "commissionRate");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfBankAccount = CursorUtil.getColumnIndexOrThrow(_cursor, "bankAccount");
          final int _cursorIndexOfBankName = CursorUtil.getColumnIndexOrThrow(_cursor, "bankName");
          final int _cursorIndexOfBankSheba = CursorUtil.getColumnIndexOrThrow(_cursor, "bankSheba");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final SellerEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            final long _tmpTotalSales;
            _tmpTotalSales = _cursor.getLong(_cursorIndexOfTotalSales);
            final long _tmpTotalCommission;
            _tmpTotalCommission = _cursor.getLong(_cursorIndexOfTotalCommission);
            final long _tmpTotalDebt;
            _tmpTotalDebt = _cursor.getLong(_cursorIndexOfTotalDebt);
            final long _tmpCommissionRate;
            _tmpCommissionRate = _cursor.getLong(_cursorIndexOfCommissionRate);
            final boolean _tmpIsActive;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp != 0;
            final String _tmpBankAccount;
            if (_cursor.isNull(_cursorIndexOfBankAccount)) {
              _tmpBankAccount = null;
            } else {
              _tmpBankAccount = _cursor.getString(_cursorIndexOfBankAccount);
            }
            final String _tmpBankName;
            if (_cursor.isNull(_cursorIndexOfBankName)) {
              _tmpBankName = null;
            } else {
              _tmpBankName = _cursor.getString(_cursorIndexOfBankName);
            }
            final String _tmpBankSheba;
            if (_cursor.isNull(_cursorIndexOfBankSheba)) {
              _tmpBankSheba = null;
            } else {
              _tmpBankSheba = _cursor.getString(_cursorIndexOfBankSheba);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new SellerEntity(_tmpId,_tmpUserId,_tmpName,_tmpPhone,_tmpAddress,_tmpEmail,_tmpTotalSales,_tmpTotalCommission,_tmpTotalDebt,_tmpCommissionRate,_tmpIsActive,_tmpBankAccount,_tmpBankName,_tmpBankSheba,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
