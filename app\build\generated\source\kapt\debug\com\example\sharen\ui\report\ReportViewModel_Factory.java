package com.example.sharen.ui.report;

import com.example.sharen.data.repository.ReportRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReportViewModel_Factory implements Factory<ReportViewModel> {
  private final Provider<NonExistentClass> invoiceRepositoryProvider;

  private final Provider<NonExistentClass> productRepositoryProvider;

  private final Provider<NonExistentClass> customerRepositoryProvider;

  private final Provider<ReportRepository> reportRepositoryProvider;

  public ReportViewModel_Factory(Provider<NonExistentClass> invoiceRepositoryProvider,
      Provider<NonExistentClass> productRepositoryProvider,
      Provider<NonExistentClass> customerRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.customerRepositoryProvider = customerRepositoryProvider;
    this.reportRepositoryProvider = reportRepositoryProvider;
  }

  @Override
  public ReportViewModel get() {
    return newInstance(invoiceRepositoryProvider.get(), productRepositoryProvider.get(), customerRepositoryProvider.get(), reportRepositoryProvider.get());
  }

  public static ReportViewModel_Factory create(Provider<NonExistentClass> invoiceRepositoryProvider,
      Provider<NonExistentClass> productRepositoryProvider,
      Provider<NonExistentClass> customerRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider) {
    return new ReportViewModel_Factory(invoiceRepositoryProvider, productRepositoryProvider, customerRepositoryProvider, reportRepositoryProvider);
  }

  public static ReportViewModel newInstance(NonExistentClass invoiceRepository,
      NonExistentClass productRepository, NonExistentClass customerRepository,
      ReportRepository reportRepository) {
    return new ReportViewModel(invoiceRepository, productRepository, customerRepository, reportRepository);
  }
}
