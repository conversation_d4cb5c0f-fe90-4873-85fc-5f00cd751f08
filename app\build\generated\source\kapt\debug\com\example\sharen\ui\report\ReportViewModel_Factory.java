package com.example.sharen.ui.report;

import com.example.sharen.data.repository.CustomerRepository;
import com.example.sharen.data.repository.InvoiceRepository;
import com.example.sharen.data.repository.ProductRepository;
import com.example.sharen.data.repository.ReportRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ReportViewModel_Factory implements Factory<ReportViewModel> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<CustomerRepository> customerRepositoryProvider;

  private final Provider<ReportRepository> reportRepositoryProvider;

  public ReportViewModel_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<CustomerRepository> customerRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.customerRepositoryProvider = customerRepositoryProvider;
    this.reportRepositoryProvider = reportRepositoryProvider;
  }

  @Override
  public ReportViewModel get() {
    return newInstance(invoiceRepositoryProvider.get(), productRepositoryProvider.get(), customerRepositoryProvider.get(), reportRepositoryProvider.get());
  }

  public static ReportViewModel_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<CustomerRepository> customerRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider) {
    return new ReportViewModel_Factory(invoiceRepositoryProvider, productRepositoryProvider, customerRepositoryProvider, reportRepositoryProvider);
  }

  public static ReportViewModel newInstance(InvoiceRepository invoiceRepository,
      ProductRepository productRepository, CustomerRepository customerRepository,
      ReportRepository reportRepository) {
    return new ReportViewModel(invoiceRepository, productRepository, customerRepository, reportRepository);
  }
}
