// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInvoiceBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final View divider;

  @NonNull
  public final ImageView ivDocIcon;

  @NonNull
  public final ImageView ivPaid;

  @NonNull
  public final TextView tvAmount;

  @NonNull
  public final TextView tvCustomerName;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvInvoiceNumber;

  @NonNull
  public final TextView tvPaymentType;

  @NonNull
  public final TextView tvStatus;

  private ItemInvoiceBinding(@NonNull CardView rootView, @NonNull View divider,
      @NonNull ImageView ivDocIcon, @NonNull ImageView ivPaid, @NonNull TextView tvAmount,
      @NonNull TextView tvCustomerName, @NonNull TextView tvDate, @NonNull TextView tvInvoiceNumber,
      @NonNull TextView tvPaymentType, @NonNull TextView tvStatus) {
    this.rootView = rootView;
    this.divider = divider;
    this.ivDocIcon = ivDocIcon;
    this.ivPaid = ivPaid;
    this.tvAmount = tvAmount;
    this.tvCustomerName = tvCustomerName;
    this.tvDate = tvDate;
    this.tvInvoiceNumber = tvInvoiceNumber;
    this.tvPaymentType = tvPaymentType;
    this.tvStatus = tvStatus;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInvoiceBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInvoiceBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_invoice, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInvoiceBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.divider;
      View divider = ViewBindings.findChildViewById(rootView, id);
      if (divider == null) {
        break missingId;
      }

      id = R.id.ivDocIcon;
      ImageView ivDocIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivDocIcon == null) {
        break missingId;
      }

      id = R.id.ivPaid;
      ImageView ivPaid = ViewBindings.findChildViewById(rootView, id);
      if (ivPaid == null) {
        break missingId;
      }

      id = R.id.tvAmount;
      TextView tvAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvAmount == null) {
        break missingId;
      }

      id = R.id.tvCustomerName;
      TextView tvCustomerName = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerName == null) {
        break missingId;
      }

      id = R.id.tvDate;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tvInvoiceNumber;
      TextView tvInvoiceNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvInvoiceNumber == null) {
        break missingId;
      }

      id = R.id.tvPaymentType;
      TextView tvPaymentType = ViewBindings.findChildViewById(rootView, id);
      if (tvPaymentType == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      return new ItemInvoiceBinding((CardView) rootView, divider, ivDocIcon, ivPaid, tvAmount,
          tvCustomerName, tvDate, tvInvoiceNumber, tvPaymentType, tvStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
