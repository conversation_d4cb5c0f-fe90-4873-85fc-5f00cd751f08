package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u000f\b\u0007\u0018\u0000 42\u00020\u0001:\u00014B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ2\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J\b\u0010\u0013\u001a\u00020\rH\u0002J;\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u000f2\u0006\u0010\u0019\u001a\u00020\u000f2\b\u0010\u001a\u001a\u0004\u0018\u00010\u000fH\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001bJ\u001b\u0010\u001c\u001a\u0004\u0018\u00010\u00152\u0006\u0010\u0016\u001a\u00020\u000fH\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001dJ\u001b\u0010\u001e\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u001a\u001a\u00020\u000fH\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001dJ\b\u0010\u001f\u001a\u00020\u000fH\u0002J\u0010\u0010 \u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\"0!H\u0016J\u0013\u0010#\u001a\u0004\u0018\u00010\u000fH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010$J\u0011\u0010%\u001a\u00020&H\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010$J2\u0010\'\u001a\b\u0012\u0004\u0012\u00020\"0\f2\u0006\u0010(\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b*\u0010\u0012J\u0011\u0010+\u001a\u00020\rH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010$J*\u0010,\u001a\b\u0012\u0004\u0012\u00020\"0\f2\u0006\u0010-\u001a\u00020\"H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b.\u0010/J\u0019\u00100\u001a\u00020&2\u0006\u0010\u0018\u001a\u00020\u000fH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u001dJ\u0010\u00101\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u000fH\u0002J*\u00102\u001a\b\u0012\u0004\u0012\u00020\"0\f2\u0006\u0010-\u001a\u00020\"H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b3\u0010/R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00065"}, d2 = {"Lcom/example/sharen/data/repository/AuthRepositoryImpl;", "Lcom/example/sharen/data/repository/AuthRepository;", "supabaseClient", "Lio/github/jan/supabase/SupabaseClient;", "userDao", "Lcom/example/sharen/data/local/dao/UserDao;", "sharedPreferences", "Landroid/content/SharedPreferences;", "remoteDataSource", "Lcom/example/sharen/data/remote/AuthRemoteDataSource;", "(Lio/github/jan/supabase/SupabaseClient;Lcom/example/sharen/data/local/dao/UserDao;Landroid/content/SharedPreferences;Lcom/example/sharen/data/remote/AuthRemoteDataSource;)V", "changePassword", "Lkotlin/Result;", "", "oldPassword", "", "newPassword", "changePassword-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearLoginState", "createUserProfile", "Lcom/example/sharen/data/local/entity/UserEntity;", "userId", "name", "email", "phone", "referrerCode", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "fetchUserProfile", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "findReferrerId", "generateReferrerCode", "getCurrentUser", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/sharen/data/model/User;", "getCurrentUserId", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isUserLoggedIn", "", "login", "username", "password", "login-0E7RQCE", "logout", "register", "user", "register-gIAlu-s", "(Lcom/example/sharen/data/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resetPassword", "saveLoginState", "updateUser", "updateUser-gIAlu-s", "Companion", "app_debug"})
public final class AuthRepositoryImpl implements com.example.sharen.data.repository.AuthRepository {
    @org.jetbrains.annotations.NotNull
    private final io.github.jan.supabase.SupabaseClient supabaseClient = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.UserDao userDao = null;
    @org.jetbrains.annotations.NotNull
    private final android.content.SharedPreferences sharedPreferences = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.AuthRemoteDataSource remoteDataSource = null;
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String KEY_USER_ID = "user_id";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String KEY_IS_LOGGED_IN = "is_logged_in";
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.data.repository.AuthRepositoryImpl.Companion Companion = null;
    
    @javax.inject.Inject
    public AuthRepositoryImpl(@org.jetbrains.annotations.NotNull
    io.github.jan.supabase.SupabaseClient supabaseClient, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.UserDao userDao, @org.jetbrains.annotations.NotNull
    android.content.SharedPreferences sharedPreferences, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.AuthRemoteDataSource remoteDataSource) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object logout(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.User> getCurrentUser() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public java.lang.Object resetPassword(@org.jetbrains.annotations.NotNull
    java.lang.String email, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public java.lang.Object isUserLoggedIn(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getCurrentUserId(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.Object fetchUserProfile(java.lang.String userId, kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.UserEntity> $completion) {
        return null;
    }
    
    private final java.lang.Object createUserProfile(java.lang.String userId, java.lang.String name, java.lang.String email, java.lang.String phone, java.lang.String referrerCode, kotlin.coroutines.Continuation<? super com.example.sharen.data.local.entity.UserEntity> $completion) {
        return null;
    }
    
    private final java.lang.Object findReferrerId(java.lang.String referrerCode, kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.String generateReferrerCode() {
        return null;
    }
    
    private final void saveLoginState(java.lang.String userId) {
    }
    
    private final void clearLoginState() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/sharen/data/repository/AuthRepositoryImpl$Companion;", "", "()V", "KEY_IS_LOGGED_IN", "", "KEY_USER_ID", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}