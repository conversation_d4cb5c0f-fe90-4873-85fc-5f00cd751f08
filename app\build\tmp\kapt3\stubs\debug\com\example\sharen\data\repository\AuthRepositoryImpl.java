package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0015\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J2\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\r\u0010\u000eJ\u0011\u0010\u000f\u001a\u00020\tH\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0010J\u0010\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00130\u0012H\u0016J\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u000bH\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0010J\u0011\u0010\u0015\u001a\u00020\u0016H\u0096@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0010J2\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00130\b2\u0006\u0010\u0018\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001a\u0010\u000eJ\"\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001c\u0010\u0010JB\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00130\b2\u0006\u0010\u001e\u001a\u00020\u000b2\u0006\u0010\u0018\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u001f\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b \u0010!J*\u0010\"\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\u0018\u001a\u00020\u000bH\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b#\u0010$J\u0019\u0010%\u001a\u00020\t2\u0006\u0010&\u001a\u00020\u000bH\u0082@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010$J*\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00130\b2\u0006\u0010(\u001a\u00020\u0013H\u0096@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b)\u0010*R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006+"}, d2 = {"Lcom/example/sharen/data/repository/AuthRepositoryImpl;", "Lcom/example/sharen/data/repository/AuthRepository;", "userDao", "Lcom/example/sharen/data/local/dao/UserDao;", "remoteDataSource", "Lcom/example/sharen/data/remote/AuthRemoteDataSource;", "(Lcom/example/sharen/data/local/dao/UserDao;Lcom/example/sharen/data/remote/AuthRemoteDataSource;)V", "changePassword", "Lkotlin/Result;", "", "oldPassword", "", "newPassword", "changePassword-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearLoginState", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentUser", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/sharen/domain/model/User;", "getCurrentUserId", "isUserLoggedIn", "", "login", "email", "password", "login-0E7RQCE", "logout", "logout-IoAF18A", "register", "name", "phone", "register-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resetPassword", "resetPassword-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveLoginState", "userId", "updateUser", "user", "updateUser-gIAlu-s", "(Lcom/example/sharen/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class AuthRepositoryImpl implements com.example.sharen.data.repository.AuthRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.UserDao userDao = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.AuthRemoteDataSource remoteDataSource = null;
    
    @javax.inject.Inject
    public AuthRepositoryImpl(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.UserDao userDao, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.AuthRemoteDataSource remoteDataSource) {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public kotlinx.coroutines.flow.Flow<com.example.sharen.domain.model.User> getCurrentUser() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object isUserLoggedIn(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object getCurrentUserId(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.Object saveLoginState(java.lang.String userId, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object clearLoginState(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}