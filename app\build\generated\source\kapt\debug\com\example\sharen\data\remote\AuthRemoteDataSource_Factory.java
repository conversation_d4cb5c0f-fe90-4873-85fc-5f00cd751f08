package com.example.sharen.data.remote;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AuthRemoteDataSource_Factory implements Factory<AuthRemoteDataSource> {
  @Override
  public AuthRemoteDataSource get() {
    return newInstance();
  }

  public static AuthRemoteDataSource_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AuthRemoteDataSource newInstance() {
    return new AuthRemoteDataSource();
  }

  private static final class InstanceHolder {
    private static final AuthRemoteDataSource_Factory INSTANCE = new AuthRemoteDataSource_Factory();
  }
}
