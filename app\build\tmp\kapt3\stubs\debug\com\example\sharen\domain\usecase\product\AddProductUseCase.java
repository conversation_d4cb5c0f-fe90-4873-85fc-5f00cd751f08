package com.example.sharen.domain.usecase.product;

/**
 * Use Case برای افزودن محصول جدید
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u00e8\u0001\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\t2\u0006\u0010\r\u001a\u00020\t2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\t2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\u00162\u000e\b\u0002\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\t0\u00162\u000e\b\u0002\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\t0\u00162\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u001d2\b\b\u0002\u0010\u001e\u001a\u00020\u001d2\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0\u00162\u000e\b\u0002\u0010 \u001a\b\u0012\u0004\u0012\u00020\t0\u0016H\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b!\u0010\"R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006#"}, d2 = {"Lcom/example/sharen/domain/usecase/product/AddProductUseCase;", "", "productRepository", "Lcom/example/sharen/domain/repository/ProductRepository;", "(Lcom/example/sharen/domain/repository/ProductRepository;)V", "invoke", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Product;", "name", "", "code", "barcode", "description", "categoryId", "brandId", "type", "error/NonExistentClass", "season", "Lcom/example/sharen/domain/model/Season;", "gender", "Lcom/example/sharen/domain/model/Gender;", "sizes", "", "colors", "materials", "purchasePrice", "", "sellingPrice", "stock", "", "minimumStock", "imageUrls", "tags", "invoke-Z6QaZII", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lerror/NonExistentClass;Lcom/example/sharen/domain/model/Season;Lcom/example/sharen/domain/model/Gender;Ljava/util/List;Ljava/util/List;Ljava/util/List;JJIILjava/util/List;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class AddProductUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.ProductRepository productRepository = null;
    
    @javax.inject.Inject
    public AddProductUseCase(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.ProductRepository productRepository) {
        super();
    }
}