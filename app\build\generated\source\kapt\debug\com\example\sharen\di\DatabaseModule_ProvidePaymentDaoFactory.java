package com.example.sharen.di;

import com.example.sharen.data.local.SharenDatabase;
import com.example.sharen.data.local.dao.PaymentDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePaymentDaoFactory implements Factory<PaymentDao> {
  private final Provider<SharenDatabase> databaseProvider;

  public DatabaseModule_ProvidePaymentDaoFactory(Provider<SharenDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PaymentDao get() {
    return providePaymentDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePaymentDaoFactory create(
      Provider<SharenDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePaymentDaoFactory(databaseProvider);
  }

  public static PaymentDao providePaymentDao(SharenDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePaymentDao(database));
  }
}
