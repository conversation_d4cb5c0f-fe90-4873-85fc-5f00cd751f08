<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:contentDescription="@string/notification_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@android:drawable/ic_dialog_info" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/tvDate"
            app:layout_constraintStart_toEndOf="@+id/ivIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="فاکتور جدید ایجاد شد" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 