// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.switchmaterial.SwitchMaterial;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityNotificationSettingsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final SwitchMaterial switchNotifications;

  @NonNull
  public final SwitchMaterial switchSound;

  @NonNull
  public final SwitchMaterial switchVibration;

  @NonNull
  public final Toolbar toolbar;

  private ActivityNotificationSettingsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull SwitchMaterial switchNotifications, @NonNull SwitchMaterial switchSound,
      @NonNull SwitchMaterial switchVibration, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.switchNotifications = switchNotifications;
    this.switchSound = switchSound;
    this.switchVibration = switchVibration;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityNotificationSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityNotificationSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_notification_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityNotificationSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.switchNotifications;
      SwitchMaterial switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.switchSound;
      SwitchMaterial switchSound = ViewBindings.findChildViewById(rootView, id);
      if (switchSound == null) {
        break missingId;
      }

      id = R.id.switchVibration;
      SwitchMaterial switchVibration = ViewBindings.findChildViewById(rootView, id);
      if (switchVibration == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityNotificationSettingsBinding((CoordinatorLayout) rootView,
          switchNotifications, switchSound, switchVibration, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
