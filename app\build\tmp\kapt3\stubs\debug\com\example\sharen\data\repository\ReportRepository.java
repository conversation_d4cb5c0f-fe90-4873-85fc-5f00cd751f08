package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0010\b\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\b\u0017\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\b\u0010\tJ*\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u00042\u0006\u0010\u000b\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\tJ*\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00070\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000e\u0010\tJ*\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0010\u0010\tJ*\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00070\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\tJ\"\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0004H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0015\u0010\u0016J*\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001a\u0010\u001bJ*\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001d\u0010\u001bJ\"\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00140\u0004H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001f\u0010\u0016J2\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010!\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b#\u0010$J\"\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00140\u0004H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b&\u0010\u0016J\"\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00140\u0004H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b(\u0010\u0016J2\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020+H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b-\u0010.J2\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020+H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b0\u0010.J2\u00101\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010!\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b2\u0010$J2\u00103\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010!\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b4\u0010$J2\u00105\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010!\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b6\u0010$J2\u00107\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010!\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b8\u0010$J*\u00109\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010*\u001a\u00020+H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b:\u0010;J*\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010*\u001a\u00020+H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b=\u0010;J\u0012\u0010>\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140@0?J\u0012\u0010A\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140@0?J*\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bC\u0010\tJ!\u0010D\u001a\u00020E2\u0006\u0010!\u001a\u00020F2\u0006\u0010\"\u001a\u00020FH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010GJ*\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010\u000b\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bI\u0010\tJ\"\u0010J\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140@0?2\u0006\u0010!\u001a\u00020\u00192\u0006\u0010\"\u001a\u00020\u0019J\u001a\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00140@0?2\u0006\u0010L\u001a\u00020MJ*\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010O\u001a\u00020\u0014H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bP\u0010QJ*\u0010R\u001a\b\u0012\u0004\u0012\u00020\u00140\u00042\u0006\u0010S\u001a\u00020\u0014H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bT\u0010Q\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006U"}, d2 = {"Lcom/example/sharen/data/repository/ReportRepository;", "", "()V", "deleteReport", "Lkotlin/Result;", "", "reportId", "", "deleteReport-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteReportTemplate", "templateId", "deleteReportTemplate-gIAlu-s", "exportReportToCsv", "exportReportToCsv-gIAlu-s", "exportReportToExcel", "exportReportToExcel-gIAlu-s", "exportReportToPdf", "exportReportToPdf-gIAlu-s", "generateCurrentInventoryReport", "Lcom/example/sharen/data/model/Report;", "generateCurrentInventoryReport-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateDailyFinancialReport", "date", "Ljava/util/Date;", "generateDailyFinancialReport-gIAlu-s", "(Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateDailySalesReport", "generateDailySalesReport-gIAlu-s", "generateDebtReport", "generateDebtReport-IoAF18A", "generateInventoryMovementReport", "startDate", "endDate", "generateInventoryMovementReport-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateInventoryValueReport", "generateInventoryValueReport-IoAF18A", "generateLowStockReport", "generateLowStockReport-IoAF18A", "generateMonthlyFinancialReport", "year", "", "month", "generateMonthlyFinancialReport-0E7RQCE", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateMonthlySalesReport", "generateMonthlySalesReport-0E7RQCE", "generateProfitLossReport", "generateProfitLossReport-0E7RQCE", "generateSalesByCategoryReport", "generateSalesByCategoryReport-0E7RQCE", "generateSalesByCustomerReport", "generateSalesByCustomerReport-0E7RQCE", "generateSalesByProductReport", "generateSalesByProductReport-0E7RQCE", "generateYearlyFinancialReport", "generateYearlyFinancialReport-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateYearlySalesReport", "generateYearlySalesReport-gIAlu-s", "getAllReportTemplates", "Lkotlinx/coroutines/flow/Flow;", "", "getAllReports", "getReport", "getReport-gIAlu-s", "getReportData", "Lcom/example/sharen/data/model/ReportData;", "", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReportTemplate", "getReportTemplate-gIAlu-s", "getReportsByDateRange", "getReportsByType", "type", "Lcom/example/sharen/data/model/ReportType;", "saveReport", "report", "saveReport-gIAlu-s", "(Lcom/example/sharen/data/model/Report;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveReportTemplate", "template", "saveReportTemplate-gIAlu-s", "app_debug"})
public class ReportRepository {
    
    @javax.inject.Inject
    public ReportRepository() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getReportData(long startDate, long endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.ReportData> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getAllReports() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getReportsByType(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.ReportType type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getReportsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getAllReportTemplates() {
        return null;
    }
}