package com.example.sharen.domain.usecase.payment;

import com.example.sharen.domain.repository.CustomerRepository;
import com.example.sharen.domain.repository.PaymentRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CreatePaymentUseCase_Factory implements Factory<CreatePaymentUseCase> {
  private final Provider<PaymentRepository> paymentRepositoryProvider;

  private final Provider<CustomerRepository> customerRepositoryProvider;

  public CreatePaymentUseCase_Factory(Provider<PaymentRepository> paymentRepositoryProvider,
      Provider<CustomerRepository> customerRepositoryProvider) {
    this.paymentRepositoryProvider = paymentRepositoryProvider;
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public CreatePaymentUseCase get() {
    return newInstance(paymentRepositoryProvider.get(), customerRepositoryProvider.get());
  }

  public static CreatePaymentUseCase_Factory create(
      Provider<PaymentRepository> paymentRepositoryProvider,
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new CreatePaymentUseCase_Factory(paymentRepositoryProvider, customerRepositoryProvider);
  }

  public static CreatePaymentUseCase newInstance(PaymentRepository paymentRepository,
      CustomerRepository customerRepository) {
    return new CreatePaymentUseCase(paymentRepository, customerRepository);
  }
}
