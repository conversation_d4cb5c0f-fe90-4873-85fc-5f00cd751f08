package com.example.sharen.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\b\b\u0017\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0019\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ\u0019\u0010\u0010\u001a\u00020\u00112\u0006\u0010\t\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ7\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\r0\u00132\u0006\u0010\u0014\u001a\u00020\b2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0016H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001aJ\u001a\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001c2\u0006\u0010\u001d\u001a\u00020\nJ\u0016\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\u001c2\u0006\u0010\t\u001a\u00020\nJ-\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020\b0 2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u0018H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010#J\u0012\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001cJ\u001a\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001c2\u0006\u0010\u001d\u001a\u00020\nJ\"\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001c2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u0018J\u001a\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001c2\u0006\u0010(\u001a\u00020)J\u0012\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001cJ\u001a\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00130\u001c2\u0006\u0010\u001d\u001a\u00020\nJ!\u0010,\u001a\u00020\r2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010-\u001a\u00020\bH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010.J\u0019\u0010/\u001a\u00020\u00112\u0006\u0010\t\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0019\u00100\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u00061"}, d2 = {"Lcom/example/sharen/data/repository/InstallmentRepository;", "", "api", "Lcom/example/sharen/data/api/InstallmentApi;", "dao", "Lcom/example/sharen/data/local/dao/InstallmentDao;", "(Lcom/example/sharen/data/api/InstallmentApi;Lcom/example/sharen/data/local/dao/InstallmentDao;)V", "calculateRemainingAmount", "", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInstallment", "Lcom/example/sharen/data/model/Installment;", "installment", "(Lcom/example/sharen/data/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInstallment", "", "generateInstallmentSchedule", "", "totalAmount", "numberOfInstallments", "", "startDate", "Ljava/util/Date;", "intervalInDays", "(DILjava/util/Date;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCustomerInstallmentHistory", "Lkotlinx/coroutines/flow/Flow;", "customerId", "getInstallment", "getInstallmentStatistics", "", "", "endDate", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstallments", "getInstallmentsByCustomer", "getInstallmentsByDateRange", "getInstallmentsByStatus", "status", "Lcom/example/sharen/data/model/InstallmentStatus;", "getOverdueInstallments", "getUpcomingInstallments", "payInstallment", "amount", "(JDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendReminder", "updateInstallment", "app_debug"})
public class InstallmentRepository {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.api.InstallmentApi api = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.local.dao.InstallmentDao dao = null;
    
    @javax.inject.Inject
    public InstallmentRepository(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.api.InstallmentApi api, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.local.dao.InstallmentDao dao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Installment> getInstallment(long id) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createInstallment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateInstallment(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Installment installment, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteInstallment(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object payInstallment(long id, double amount, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.Installment> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object sendReminder(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallmentsByCustomer(long customerId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallmentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallmentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.InstallmentStatus status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getUpcomingInstallments(long customerId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getOverdueInstallments() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getInstallmentStatistics(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Double>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object calculateRemainingAmount(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getCustomerInstallmentHistory(long customerId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object generateInstallmentSchedule(double totalAmount, int numberOfInstallments, @org.jetbrains.annotations.NotNull
    java.util.Date startDate, int intervalInDays, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.example.sharen.data.model.Installment>> $completion) {
        return null;
    }
}