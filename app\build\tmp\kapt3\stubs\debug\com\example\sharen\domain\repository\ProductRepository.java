package com.example.sharen.domain.repository;

/**
 * Repository Interface برای مدیریت محصولات
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0012\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\n\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J2\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000e\u0010\u000fJ*\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0011\u0010\u0012J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u0014H&J\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u0014H&J\u001e\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\b\b\u0002\u0010\u0018\u001a\u00020\rH&J\u0014\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u0014H&J\u0014\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u0014H&J\u001e\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\b\b\u0002\u0010\u001c\u001a\u00020\rH&J\u0014\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u0014H&J,\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001f\u0010\u0012J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\u0006\u0010!\u001a\u00020\u000bH&J\u001c\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\u0006\u0010#\u001a\u00020\u000bH&J\u001c\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\u0006\u0010%\u001a\u00020\u000bH&J$\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020(H&J\u001c\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\u0006\u0010+\u001a\u00020,H&J2\u0010-\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b.\u0010\u000fJ\u001c\u0010/\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00142\u0006\u00100\u001a\u00020\u000bH&J*\u00101\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b2\u0010\u0007J2\u00103\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000b2\u0006\u00104\u001a\u00020\rH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b5\u0010\u000f\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00066"}, d2 = {"Lcom/example/sharen/domain/repository/ProductRepository;", "", "addProduct", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Product;", "product", "addProduct-gIAlu-s", "(Lcom/example/sharen/domain/model/Product;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "decreaseStock", "", "productId", "", "quantity", "", "decreaseStock-0E7RQCE", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProduct", "deleteProduct-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveProducts", "Lkotlinx/coroutines/flow/Flow;", "", "getAllProducts", "getBestSellingProducts", "limit", "getInactiveProducts", "getLowStockProducts", "getNewProducts", "daysSinceAdded", "getOutOfStockProducts", "getProductById", "getProductById-gIAlu-s", "getProductsByBrand", "brandId", "getProductsByCategory", "categoryId", "getProductsByMaterial", "material", "getProductsByPriceRange", "minPrice", "", "maxPrice", "getProductsByStockStatus", "inStock", "", "increaseStock", "increaseStock-0E7RQCE", "searchProducts", "query", "updateProduct", "updateProduct-gIAlu-s", "updateProductStock", "newStock", "updateProductStock-0E7RQCE", "app_debug"})
public abstract interface ProductRepository {
    
    /**
     * دریافت تمام محصولات
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getAllProducts();
    
    /**
     * دریافت محصولات دسته‌بندی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryId);
    
    /**
     * دریافت محصولات برند
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByBrand(@org.jetbrains.annotations.NotNull
    java.lang.String brandId);
    
    /**
     * جستجوی محصولات
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> searchProducts(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * فیلتر محصولات بر اساس موجودی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByStockStatus(boolean inStock);
    
    /**
     * دریافت محصولات کم موجود
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getLowStockProducts();
    
    /**
     * دریافت محصولات ناموجود
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getOutOfStockProducts();
    
    /**
     * دریافت محصولات بر اساس بازه قیمت
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByPriceRange(long minPrice, long maxPrice);
    
    /**
     * دریافت محصولات فعال
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getActiveProducts();
    
    /**
     * دریافت محصولات غیرفعال
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getInactiveProducts();
    
    /**
     * دریافت محصولات بر اساس جنس
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getProductsByMaterial(@org.jetbrains.annotations.NotNull
    java.lang.String material);
    
    /**
     * دریافت محصولات پرفروش
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getBestSellingProducts(int limit);
    
    /**
     * دریافت محصولات جدید
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.Product>> getNewProducts(int daysSinceAdded);
    
    /**
     * Repository Interface برای مدیریت محصولات
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}