// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogUpdateStockBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextInputEditText etNewStock;

  @NonNull
  public final TextView tvCurrentStock;

  private DialogUpdateStockBinding(@NonNull LinearLayout rootView,
      @NonNull TextInputEditText etNewStock, @NonNull TextView tvCurrentStock) {
    this.rootView = rootView;
    this.etNewStock = etNewStock;
    this.tvCurrentStock = tvCurrentStock;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogUpdateStockBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogUpdateStockBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_update_stock, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogUpdateStockBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.etNewStock;
      TextInputEditText etNewStock = ViewBindings.findChildViewById(rootView, id);
      if (etNewStock == null) {
        break missingId;
      }

      id = R.id.tvCurrentStock;
      TextView tvCurrentStock = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentStock == null) {
        break missingId;
      }

      return new DialogUpdateStockBinding((LinearLayout) rootView, etNewStock, tvCurrentStock);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
