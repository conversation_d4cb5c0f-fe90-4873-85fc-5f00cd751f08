// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPaymentDetailsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextView amountTextView;

  @NonNull
  public final TextView dateTextView;

  @NonNull
  public final TextView methodTextView;

  @NonNull
  public final TextView notesTextView;

  @NonNull
  public final TextView referenceTextView;

  @NonNull
  public final Chip statusChip;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentPaymentDetailsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull TextView amountTextView, @NonNull TextView dateTextView,
      @NonNull TextView methodTextView, @NonNull TextView notesTextView,
      @NonNull TextView referenceTextView, @NonNull Chip statusChip,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.amountTextView = amountTextView;
    this.dateTextView = dateTextView;
    this.methodTextView = methodTextView;
    this.notesTextView = notesTextView;
    this.referenceTextView = referenceTextView;
    this.statusChip = statusChip;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPaymentDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPaymentDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_payment_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPaymentDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amountTextView;
      TextView amountTextView = ViewBindings.findChildViewById(rootView, id);
      if (amountTextView == null) {
        break missingId;
      }

      id = R.id.dateTextView;
      TextView dateTextView = ViewBindings.findChildViewById(rootView, id);
      if (dateTextView == null) {
        break missingId;
      }

      id = R.id.methodTextView;
      TextView methodTextView = ViewBindings.findChildViewById(rootView, id);
      if (methodTextView == null) {
        break missingId;
      }

      id = R.id.notesTextView;
      TextView notesTextView = ViewBindings.findChildViewById(rootView, id);
      if (notesTextView == null) {
        break missingId;
      }

      id = R.id.referenceTextView;
      TextView referenceTextView = ViewBindings.findChildViewById(rootView, id);
      if (referenceTextView == null) {
        break missingId;
      }

      id = R.id.statusChip;
      Chip statusChip = ViewBindings.findChildViewById(rootView, id);
      if (statusChip == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentPaymentDetailsBinding((CoordinatorLayout) rootView, amountTextView,
          dateTextView, methodTextView, notesTextView, referenceTextView, statusChip, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
