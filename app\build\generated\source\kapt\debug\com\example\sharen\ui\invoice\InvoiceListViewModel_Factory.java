package com.example.sharen.ui.invoice;

import com.example.sharen.data.repository.InvoiceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceListViewModel_Factory implements Factory<InvoiceListViewModel> {
  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  public InvoiceListViewModel_Factory(Provider<InvoiceRepository> invoiceRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
  }

  @Override
  public InvoiceListViewModel get() {
    return newInstance(invoiceRepositoryProvider.get());
  }

  public static InvoiceListViewModel_Factory create(
      Provider<InvoiceRepository> invoiceRepositoryProvider) {
    return new InvoiceListViewModel_Factory(invoiceRepositoryProvider);
  }

  public static InvoiceListViewModel newInstance(InvoiceRepository invoiceRepository) {
    return new InvoiceListViewModel(invoiceRepository);
  }
}
