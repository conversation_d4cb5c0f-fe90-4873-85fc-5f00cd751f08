package com.example.sharen.ui.invoice;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InvoiceListViewModel_Factory implements Factory<InvoiceListViewModel> {
  private final Provider<NonExistentClass> invoiceRepositoryProvider;

  public InvoiceListViewModel_Factory(Provider<NonExistentClass> invoiceRepositoryProvider) {
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
  }

  @Override
  public InvoiceListViewModel get() {
    return newInstance(invoiceRepositoryProvider.get());
  }

  public static InvoiceListViewModel_Factory create(
      Provider<NonExistentClass> invoiceRepositoryProvider) {
    return new InvoiceListViewModel_Factory(invoiceRepositoryProvider);
  }

  public static InvoiceListViewModel newInstance(NonExistentClass invoiceRepository) {
    return new InvoiceListViewModel(invoiceRepository);
  }
}
