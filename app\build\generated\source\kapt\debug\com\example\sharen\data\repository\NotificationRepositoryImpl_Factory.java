package com.example.sharen.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationRepositoryImpl_Factory implements Factory<NotificationRepositoryImpl> {
  @Override
  public NotificationRepositoryImpl get() {
    return newInstance();
  }

  public static NotificationRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static NotificationRepositoryImpl newInstance() {
    return new NotificationRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final NotificationRepositoryImpl_Factory INSTANCE = new NotificationRepositoryImpl_Factory();
  }
}
