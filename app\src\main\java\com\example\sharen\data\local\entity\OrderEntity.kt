package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.domain.model.Order
import com.example.sharen.domain.model.OrderStatus
import java.util.Date

@Entity(
    tableName = "orders",
    foreignKeys = [
        ForeignKey(
            entity = CustomerEntity::class,
            parentColumns = ["id"],
            childColumns = ["customerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["sellerId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index("customerId"),
        Index("sellerId")
    ]
)
data class OrderEntity(
    @PrimaryKey
    val id: String,
    val customerId: String,
    val sellerId: String?,
    val totalAmount: Long,
    val discountAmount: Long = 0,
    val finalAmount: Long,
    val status: String,
    val notes: String?,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toOrder(): Order = Order(
        id = id,
        customerId = customerId,
        sellerId = sellerId,
        totalAmount = totalAmount,
        discountAmount = discountAmount,
        finalAmount = finalAmount,
        status = OrderStatus.valueOf(status),
        notes = notes,
        createdAt = Date(this.createdAt),
        updatedAt = Date(this.updatedAt)
    )

    companion object {
        fun fromOrder(order: Order): OrderEntity = OrderEntity(
            id = order.id,
            customerId = order.customerId,
            sellerId = order.sellerId,
            totalAmount = order.totalAmount,
            discountAmount = order.discountAmount,
            finalAmount = order.finalAmount,
            status = order.status.name,
            notes = order.notes,
            createdAt = order.createdAt.time,
            updatedAt = order.updatedAt.time
        )
    }
}