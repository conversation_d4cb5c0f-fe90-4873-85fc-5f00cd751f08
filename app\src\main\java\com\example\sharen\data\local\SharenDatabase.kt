package com.example.sharen.data.local

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.example.sharen.data.local.converter.DateConverter
import com.example.sharen.data.local.dao.CustomerDao
import com.example.sharen.data.local.dao.UserDao
import com.example.sharen.data.local.entity.CategoryEntity
import com.example.sharen.data.local.entity.CustomerEntity
import com.example.sharen.data.local.entity.InvoiceEntity
import com.example.sharen.data.local.entity.InvoiceItemEntity
import com.example.sharen.data.local.entity.ProductEntity
import com.example.sharen.data.local.entity.SellerEntity
import com.example.sharen.data.local.entity.UserEntity

@Database(
    entities = [
        UserEntity::class,
        CustomerEntity::class,
        SellerEntity::class,
        ProductEntity::class,
        CategoryEntity::class,
        InvoiceEntity::class,
        InvoiceItemEntity::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(DateConverter::class)
abstract class SharenDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun customerDao(): CustomerDao
    
    // Remaining DAOs will be added as we implement them
} 