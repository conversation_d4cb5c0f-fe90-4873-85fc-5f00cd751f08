// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final LinearLayout layoutRegister;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilEmail;

  @NonNull
  public final TextInputLayout tilPassword;

  @NonNull
  public final TextView tvForgotPassword;

  @NonNull
  public final TextView tvLoginTitle;

  @NonNull
  public final TextView tvNoAccount;

  @NonNull
  public final TextView tvRegister;

  private ActivityLoginBinding(@NonNull ConstraintLayout rootView, @NonNull MaterialButton btnLogin,
      @NonNull TextInputEditText etEmail, @NonNull TextInputEditText etPassword,
      @NonNull ImageView ivLogo, @NonNull LinearLayout layoutRegister,
      @NonNull ProgressBar progressBar, @NonNull TextInputLayout tilEmail,
      @NonNull TextInputLayout tilPassword, @NonNull TextView tvForgotPassword,
      @NonNull TextView tvLoginTitle, @NonNull TextView tvNoAccount, @NonNull TextView tvRegister) {
    this.rootView = rootView;
    this.btnLogin = btnLogin;
    this.etEmail = etEmail;
    this.etPassword = etPassword;
    this.ivLogo = ivLogo;
    this.layoutRegister = layoutRegister;
    this.progressBar = progressBar;
    this.tilEmail = tilEmail;
    this.tilPassword = tilPassword;
    this.tvForgotPassword = tvForgotPassword;
    this.tvLoginTitle = tvLoginTitle;
    this.tvNoAccount = tvNoAccount;
    this.tvRegister = tvRegister;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_login;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.et_email;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.et_password;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.iv_logo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.layout_register;
      LinearLayout layoutRegister = ViewBindings.findChildViewById(rootView, id);
      if (layoutRegister == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.til_email;
      TextInputLayout tilEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilEmail == null) {
        break missingId;
      }

      id = R.id.til_password;
      TextInputLayout tilPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPassword == null) {
        break missingId;
      }

      id = R.id.tv_forgot_password;
      TextView tvForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (tvForgotPassword == null) {
        break missingId;
      }

      id = R.id.tv_login_title;
      TextView tvLoginTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvLoginTitle == null) {
        break missingId;
      }

      id = R.id.tv_no_account;
      TextView tvNoAccount = ViewBindings.findChildViewById(rootView, id);
      if (tvNoAccount == null) {
        break missingId;
      }

      id = R.id.tv_register;
      TextView tvRegister = ViewBindings.findChildViewById(rootView, id);
      if (tvRegister == null) {
        break missingId;
      }

      return new ActivityLoginBinding((ConstraintLayout) rootView, btnLogin, etEmail, etPassword,
          ivLogo, layoutRegister, progressBar, tilEmail, tilPassword, tvForgotPassword,
          tvLoginTitle, tvNoAccount, tvRegister);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
