package com.example.sharen.data.remote;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0018\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ*\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\r\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000e\u0010\u000bJ*\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0010\u0010\u000bJ*\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0012\u0010\u000bJ*\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u000bJ\u0014\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0016H\u0002J\"\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0019\u0010\u001aJ*\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010\u001c\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001e\u0010\u001fJ*\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010\u001c\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b!\u0010\u001fJ\"\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00180\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b#\u0010\u001aJ\u0014\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0016H\u0002J$\u0010%\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J$\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J2\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b*\u0010+J\u0014\u0010,\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0016H\u0002J\"\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00180\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b.\u0010\u001aJ\u0014\u0010/\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u0016H\u0002J\"\u00100\u001a\b\u0012\u0004\u0012\u00020\u00180\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b1\u0010\u001aJ2\u00102\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u000204H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b6\u00107J2\u00108\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u000204H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b9\u00107J$\u0010:\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J2\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b<\u0010+J$\u0010=\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J2\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b?\u0010+J$\u0010@\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J2\u0010A\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bB\u0010+J$\u0010C\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J2\u0010D\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bE\u0010+J$\u0010F\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u00162\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dH\u0002J*\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00103\u001a\u000204H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bH\u0010IJ*\u0010J\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u00103\u001a\u000204H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bK\u0010IJ\u0012\u0010L\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180N0MJ\u0012\u0010O\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180N0MJ\u0018\u0010P\u001a\u00020\u001d2\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u000204H\u0002J\u0010\u0010Q\u001a\u00020\u001d2\u0006\u00103\u001a\u000204H\u0002J\u0018\u0010R\u001a\u00020\u001d2\u0006\u00103\u001a\u0002042\u0006\u00105\u001a\u000204H\u0002J\u0010\u0010S\u001a\u00020\u001d2\u0006\u00103\u001a\u000204H\u0002J*\u0010T\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bU\u0010\u000bJ!\u0010V\u001a\u00020W2\u0006\u0010&\u001a\u00020X2\u0006\u0010\'\u001a\u00020XH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010YJ*\u0010Z\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010\r\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b[\u0010\u000bJ\"\u0010\\\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180N0M2\u0006\u0010&\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dJ\u001a\u0010]\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180N0M2\u0006\u0010^\u001a\u00020_J*\u0010`\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010a\u001a\u00020\u0018H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bb\u0010cJ*\u0010d\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010e\u001a\u00020\u0018H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bf\u0010cJ*\u0010g\u001a\b\u0012\u0004\u0012\u00020\u00180\u00062\u0006\u0010a\u001a\u00020\u0018H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\bh\u0010cR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006i"}, d2 = {"Lcom/example/sharen/data/remote/ReportRemoteDataSource;", "", "apiService", "Lcom/example/sharen/data/remote/SupabaseApiService;", "(Lcom/example/sharen/data/remote/SupabaseApiService;)V", "deleteReport", "Lkotlin/Result;", "", "reportId", "", "deleteReport-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteReportTemplate", "templateId", "deleteReportTemplate-gIAlu-s", "exportReportToCsv", "exportReportToCsv-gIAlu-s", "exportReportToExcel", "exportReportToExcel-gIAlu-s", "exportReportToPdf", "exportReportToPdf-gIAlu-s", "generateCurrentInventoryContent", "", "generateCurrentInventoryReport", "Lcom/example/sharen/data/model/Report;", "generateCurrentInventoryReport-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateDailyFinancialReport", "date", "Ljava/util/Date;", "generateDailyFinancialReport-gIAlu-s", "(Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateDailySalesReport", "generateDailySalesReport-gIAlu-s", "generateDebtReport", "generateDebtReport-IoAF18A", "generateDebtReportContent", "generateFinancialReportContent", "startDate", "endDate", "generateInventoryMovementContent", "generateInventoryMovementReport", "generateInventoryMovementReport-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateInventoryValueContent", "generateInventoryValueReport", "generateInventoryValueReport-IoAF18A", "generateLowStockContent", "generateLowStockReport", "generateLowStockReport-IoAF18A", "generateMonthlyFinancialReport", "year", "", "month", "generateMonthlyFinancialReport-0E7RQCE", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateMonthlySalesReport", "generateMonthlySalesReport-0E7RQCE", "generateProfitLossContent", "generateProfitLossReport", "generateProfitLossReport-0E7RQCE", "generateSalesByCategoryContent", "generateSalesByCategoryReport", "generateSalesByCategoryReport-0E7RQCE", "generateSalesByCustomerContent", "generateSalesByCustomerReport", "generateSalesByCustomerReport-0E7RQCE", "generateSalesByProductContent", "generateSalesByProductReport", "generateSalesByProductReport-0E7RQCE", "generateSalesReportContent", "generateYearlyFinancialReport", "generateYearlyFinancialReport-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateYearlySalesReport", "generateYearlySalesReport-gIAlu-s", "getAllReportTemplates", "Lkotlinx/coroutines/flow/Flow;", "", "getAllReports", "getFirstDayOfMonth", "getFirstDayOfYear", "getLastDayOfMonth", "getLastDayOfYear", "getReport", "getReport-gIAlu-s", "getReportData", "Lcom/example/sharen/data/model/ReportData;", "", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReportTemplate", "getReportTemplate-gIAlu-s", "getReportsByDateRange", "getReportsByType", "type", "Lcom/example/sharen/data/model/ReportType;", "saveReport", "report", "saveReport-gIAlu-s", "(Lcom/example/sharen/data/model/Report;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveReportTemplate", "template", "saveReportTemplate-gIAlu-s", "updateReport", "updateReport-gIAlu-s", "app_debug"})
public final class ReportRemoteDataSource {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.SupabaseApiService apiService = null;
    
    @javax.inject.Inject
    public ReportRemoteDataSource(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.SupabaseApiService apiService) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getReportData(long startDate, long endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.example.sharen.data.model.ReportData> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getAllReports() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getReportsByType(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.ReportType type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getReportsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateFinancialReportContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateProfitLossContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateDebtReportContent() {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateSalesReportContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateSalesByProductContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateSalesByCustomerContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateSalesByCategoryContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateCurrentInventoryContent() {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateLowStockContent() {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateInventoryMovementContent(java.util.Date startDate, java.util.Date endDate) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> generateInventoryValueContent() {
        return null;
    }
    
    private final java.util.Date getFirstDayOfMonth(int year, int month) {
        return null;
    }
    
    private final java.util.Date getLastDayOfMonth(int year, int month) {
        return null;
    }
    
    private final java.util.Date getFirstDayOfYear(int year) {
        return null;
    }
    
    private final java.util.Date getLastDayOfYear(int year) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Report>> getAllReportTemplates() {
        return null;
    }
}