package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddCustomerUseCase_Factory implements Factory<AddCustomerUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public AddCustomerUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public AddCustomerUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static AddCustomerUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new AddCustomerUseCase_Factory(customerRepositoryProvider);
  }

  public static AddCustomerUseCase newInstance(CustomerRepository customerRepository) {
    return new AddCustomerUseCase(customerRepository);
  }
}
