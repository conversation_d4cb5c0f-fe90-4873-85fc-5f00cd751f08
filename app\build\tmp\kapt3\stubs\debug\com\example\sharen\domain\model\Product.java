package com.example.sharen.domain.model;

/**
 * Domain Model برای محصول
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\u0006\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u00f9\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0010\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0016\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0016\u0012\u000e\b\u0002\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019\u0012\u000e\b\u0002\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0019\u0012\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001e\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010 \u0012\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\"J\t\u0010M\u001a\u00020\u0003H\u00c6\u0003J\t\u0010N\u001a\u00020\rH\u00c6\u0003J\t\u0010O\u001a\u00020\rH\u00c6\u0003J\t\u0010P\u001a\u00020\u0010H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0010H\u00c6\u0003J\u000b\u0010R\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\u0014H\u00c6\u0003J\t\u0010T\u001a\u00020\u0016H\u00c6\u0003J\t\u0010U\u001a\u00020\u0016H\u00c6\u0003J\u000f\u0010V\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019H\u00c6\u0003J\u000f\u0010W\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0019H\u00c6\u0003J\t\u0010X\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010Y\u001a\u0004\u0018\u00010\u001eH\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010 H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010^\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010`\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010a\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0087\u0002\u0010c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\r2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00102\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u00162\u000e\b\u0002\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u00192\u000e\b\u0002\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u00192\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010 2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\t\u0010d\u001a\u00020\u0010H\u00d6\u0001J\u0013\u0010e\u001a\u00020\u00142\b\u0010f\u001a\u0004\u0018\u00010gH\u00d6\u0003J\t\u0010h\u001a\u00020\u0010H\u00d6\u0001J\t\u0010i\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010j\u001a\u00020k2\u0006\u0010l\u001a\u00020m2\u0006\u0010n\u001a\u00020\u0010H\u00d6\u0001R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010$R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010$R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010$R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010$R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010$R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010$R\u0013\u0010\u001f\u001a\u0004\u0018\u00010 \u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010$R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010$R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u00103R\u0011\u00104\u001a\u00020\u00148F\u00a2\u0006\u0006\u001a\u0004\b4\u00103R\u0011\u00105\u001a\u00020\u00148F\u00a2\u0006\u0006\u001a\u0004\b5\u00103R\u0011\u00106\u001a\u00020\u00148F\u00a2\u0006\u0006\u001a\u0004\b6\u00103R\u0013\u0010!\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010$R\u0011\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u00109R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010$R\u0011\u0010;\u001a\u00020\r8F\u00a2\u0006\u0006\u001a\u0004\b<\u0010=R\u0011\u0010>\u001a\u00020?8F\u00a2\u0006\u0006\u001a\u0004\b@\u0010AR\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010=R\u0013\u0010\u001d\u001a\u0004\u0018\u00010\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010DR\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010=R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010+R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u00109R\u0011\u0010H\u001a\u00020I8F\u00a2\u0006\u0006\u001a\u0004\bJ\u0010KR\u0011\u0010\u0017\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u0010-\u00a8\u0006o"}, d2 = {"Lcom/example/sharen/domain/model/Product;", "Landroid/os/Parcelable;", "id", "", "name", "code", "barcode", "description", "categoryId", "categoryName", "brandId", "brandName", "purchasePrice", "", "sellingPrice", "stock", "", "minimumStock", "imageUrl", "isActive", "", "createdAt", "Ljava/util/Date;", "updatedAt", "sizes", "", "Lcom/example/sharen/domain/model/ProductSize;", "colors", "Lcom/example/sharen/domain/model/ProductColor;", "season", "Lcom/example/sharen/domain/model/Season;", "gender", "Lcom/example/sharen/domain/model/Gender;", "material", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JJIILjava/lang/String;ZLjava/util/Date;Ljava/util/Date;Ljava/util/List;Ljava/util/List;Lcom/example/sharen/domain/model/Season;Lcom/example/sharen/domain/model/Gender;Ljava/lang/String;)V", "getBarcode", "()Ljava/lang/String;", "getBrandId", "getBrandName", "getCategoryId", "getCategoryName", "getCode", "getColors", "()Ljava/util/List;", "getCreatedAt", "()Ljava/util/Date;", "getDescription", "getGender", "()Lcom/example/sharen/domain/model/Gender;", "getId", "getImageUrl", "()Z", "isInStock", "isLowStock", "isOutOfStock", "getMaterial", "getMinimumStock", "()I", "getName", "profit", "getProfit", "()J", "profitPercentage", "", "getProfitPercentage", "()D", "getPurchasePrice", "getSeason", "()Lcom/example/sharen/domain/model/Season;", "getSellingPrice", "getSizes", "getStock", "stockStatus", "Lcom/example/sharen/domain/model/StockStatus;", "getStockStatus", "()Lcom/example/sharen/domain/model/StockStatus;", "getUpdatedAt", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize
public final class Product implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String code = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String barcode = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String description = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String categoryId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String categoryName = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String brandId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String brandName = null;
    private final long purchasePrice = 0L;
    private final long sellingPrice = 0L;
    private final int stock = 0;
    private final int minimumStock = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String imageUrl = null;
    private final boolean isActive = false;
    @org.jetbrains.annotations.NotNull
    private final java.util.Date createdAt = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Date updatedAt = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.example.sharen.domain.model.ProductSize> sizes = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.example.sharen.domain.model.ProductColor> colors = null;
    @org.jetbrains.annotations.Nullable
    private final com.example.sharen.domain.model.Season season = null;
    @org.jetbrains.annotations.Nullable
    private final com.example.sharen.domain.model.Gender gender = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String material = null;
    
    public Product(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.Nullable
    java.lang.String code, @org.jetbrains.annotations.Nullable
    java.lang.String barcode, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String categoryId, @org.jetbrains.annotations.Nullable
    java.lang.String categoryName, @org.jetbrains.annotations.Nullable
    java.lang.String brandId, @org.jetbrains.annotations.Nullable
    java.lang.String brandName, long purchasePrice, long sellingPrice, int stock, int minimumStock, @org.jetbrains.annotations.Nullable
    java.lang.String imageUrl, boolean isActive, @org.jetbrains.annotations.NotNull
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull
    java.util.Date updatedAt, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.domain.model.ProductSize> sizes, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.domain.model.ProductColor> colors, @org.jetbrains.annotations.Nullable
    com.example.sharen.domain.model.Season season, @org.jetbrains.annotations.Nullable
    com.example.sharen.domain.model.Gender gender, @org.jetbrains.annotations.Nullable
    java.lang.String material) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getBarcode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCategoryId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCategoryName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getBrandId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getBrandName() {
        return null;
    }
    
    public final long getPurchasePrice() {
        return 0L;
    }
    
    public final long getSellingPrice() {
        return 0L;
    }
    
    public final int getStock() {
        return 0;
    }
    
    public final int getMinimumStock() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getImageUrl() {
        return null;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date getCreatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date getUpdatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.domain.model.ProductSize> getSizes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.domain.model.ProductColor> getColors() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.example.sharen.domain.model.Season getSeason() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.example.sharen.domain.model.Gender getGender() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getMaterial() {
        return null;
    }
    
    public final boolean isInStock() {
        return false;
    }
    
    public final boolean isLowStock() {
        return false;
    }
    
    public final boolean isOutOfStock() {
        return false;
    }
    
    public final long getProfit() {
        return 0L;
    }
    
    public final double getProfitPercentage() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.StockStatus getStockStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component10() {
        return 0L;
    }
    
    public final long component11() {
        return 0L;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component14() {
        return null;
    }
    
    public final boolean component15() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.domain.model.ProductSize> component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.domain.model.ProductColor> component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.example.sharen.domain.model.Season component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.example.sharen.domain.model.Gender component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.domain.model.Product copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.Nullable
    java.lang.String code, @org.jetbrains.annotations.Nullable
    java.lang.String barcode, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String categoryId, @org.jetbrains.annotations.Nullable
    java.lang.String categoryName, @org.jetbrains.annotations.Nullable
    java.lang.String brandId, @org.jetbrains.annotations.Nullable
    java.lang.String brandName, long purchasePrice, long sellingPrice, int stock, int minimumStock, @org.jetbrains.annotations.Nullable
    java.lang.String imageUrl, boolean isActive, @org.jetbrains.annotations.NotNull
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull
    java.util.Date updatedAt, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.domain.model.ProductSize> sizes, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.domain.model.ProductColor> colors, @org.jetbrains.annotations.Nullable
    com.example.sharen.domain.model.Season season, @org.jetbrains.annotations.Nullable
    com.example.sharen.domain.model.Gender gender, @org.jetbrains.annotations.Nullable
    java.lang.String material) {
        return null;
    }
    
    @java.lang.Override
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override
    public void writeToParcel(@org.jetbrains.annotations.NotNull
    android.os.Parcel parcel, int flags) {
    }
}