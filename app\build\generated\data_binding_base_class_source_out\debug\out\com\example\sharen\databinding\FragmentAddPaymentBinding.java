// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentAddPaymentBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextInputEditText amountEditText;

  @NonNull
  public final TextInputLayout amountLayout;

  @NonNull
  public final TextInputEditText notesEditText;

  @NonNull
  public final TextInputLayout notesLayout;

  @NonNull
  public final TextInputLayout paymentMethodLayout;

  @NonNull
  public final AutoCompleteTextView paymentMethodSpinner;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputEditText referenceNumberEditText;

  @NonNull
  public final TextInputLayout referenceNumberLayout;

  @NonNull
  public final MaterialButton saveButton;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentAddPaymentBinding(@NonNull CoordinatorLayout rootView,
      @NonNull TextInputEditText amountEditText, @NonNull TextInputLayout amountLayout,
      @NonNull TextInputEditText notesEditText, @NonNull TextInputLayout notesLayout,
      @NonNull TextInputLayout paymentMethodLayout,
      @NonNull AutoCompleteTextView paymentMethodSpinner, @NonNull ProgressBar progressBar,
      @NonNull TextInputEditText referenceNumberEditText,
      @NonNull TextInputLayout referenceNumberLayout, @NonNull MaterialButton saveButton,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.amountEditText = amountEditText;
    this.amountLayout = amountLayout;
    this.notesEditText = notesEditText;
    this.notesLayout = notesLayout;
    this.paymentMethodLayout = paymentMethodLayout;
    this.paymentMethodSpinner = paymentMethodSpinner;
    this.progressBar = progressBar;
    this.referenceNumberEditText = referenceNumberEditText;
    this.referenceNumberLayout = referenceNumberLayout;
    this.saveButton = saveButton;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentAddPaymentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentAddPaymentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_add_payment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentAddPaymentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.amountEditText;
      TextInputEditText amountEditText = ViewBindings.findChildViewById(rootView, id);
      if (amountEditText == null) {
        break missingId;
      }

      id = R.id.amountLayout;
      TextInputLayout amountLayout = ViewBindings.findChildViewById(rootView, id);
      if (amountLayout == null) {
        break missingId;
      }

      id = R.id.notesEditText;
      TextInputEditText notesEditText = ViewBindings.findChildViewById(rootView, id);
      if (notesEditText == null) {
        break missingId;
      }

      id = R.id.notesLayout;
      TextInputLayout notesLayout = ViewBindings.findChildViewById(rootView, id);
      if (notesLayout == null) {
        break missingId;
      }

      id = R.id.paymentMethodLayout;
      TextInputLayout paymentMethodLayout = ViewBindings.findChildViewById(rootView, id);
      if (paymentMethodLayout == null) {
        break missingId;
      }

      id = R.id.paymentMethodSpinner;
      AutoCompleteTextView paymentMethodSpinner = ViewBindings.findChildViewById(rootView, id);
      if (paymentMethodSpinner == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.referenceNumberEditText;
      TextInputEditText referenceNumberEditText = ViewBindings.findChildViewById(rootView, id);
      if (referenceNumberEditText == null) {
        break missingId;
      }

      id = R.id.referenceNumberLayout;
      TextInputLayout referenceNumberLayout = ViewBindings.findChildViewById(rootView, id);
      if (referenceNumberLayout == null) {
        break missingId;
      }

      id = R.id.saveButton;
      MaterialButton saveButton = ViewBindings.findChildViewById(rootView, id);
      if (saveButton == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentAddPaymentBinding((CoordinatorLayout) rootView, amountEditText,
          amountLayout, notesEditText, notesLayout, paymentMethodLayout, paymentMethodSpinner,
          progressBar, referenceNumberEditText, referenceNumberLayout, saveButton, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
