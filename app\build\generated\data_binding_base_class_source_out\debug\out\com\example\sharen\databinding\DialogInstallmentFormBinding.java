// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogInstallmentFormBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnSelectDate;

  @NonNull
  public final Button btnSubmit;

  @NonNull
  public final TextInputEditText etAmount;

  @NonNull
  public final TextInputEditText etNotes;

  @NonNull
  public final TextView tvDueDate;

  private DialogInstallmentFormBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnSelectDate, @NonNull Button btnSubmit, @NonNull TextInputEditText etAmount,
      @NonNull TextInputEditText etNotes, @NonNull TextView tvDueDate) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSelectDate = btnSelectDate;
    this.btnSubmit = btnSubmit;
    this.etAmount = etAmount;
    this.etNotes = etNotes;
    this.tvDueDate = tvDueDate;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogInstallmentFormBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogInstallmentFormBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_installment_form, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogInstallmentFormBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnSelectDate;
      Button btnSelectDate = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectDate == null) {
        break missingId;
      }

      id = R.id.btnSubmit;
      Button btnSubmit = ViewBindings.findChildViewById(rootView, id);
      if (btnSubmit == null) {
        break missingId;
      }

      id = R.id.etAmount;
      TextInputEditText etAmount = ViewBindings.findChildViewById(rootView, id);
      if (etAmount == null) {
        break missingId;
      }

      id = R.id.etNotes;
      TextInputEditText etNotes = ViewBindings.findChildViewById(rootView, id);
      if (etNotes == null) {
        break missingId;
      }

      id = R.id.tvDueDate;
      TextView tvDueDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDueDate == null) {
        break missingId;
      }

      return new DialogInstallmentFormBinding((LinearLayout) rootView, btnCancel, btnSelectDate,
          btnSubmit, etAmount, etNotes, tvDueDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
