package com.example.sharen.domain.usecase.product;

import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetProductByIdUseCase_Factory implements Factory<GetProductByIdUseCase> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public GetProductByIdUseCase_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public GetProductByIdUseCase get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static GetProductByIdUseCase_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new GetProductByIdUseCase_Factory(productRepositoryProvider);
  }

  public static GetProductByIdUseCase newInstance(ProductRepository productRepository) {
    return new GetProductByIdUseCase(productRepository);
  }
}
