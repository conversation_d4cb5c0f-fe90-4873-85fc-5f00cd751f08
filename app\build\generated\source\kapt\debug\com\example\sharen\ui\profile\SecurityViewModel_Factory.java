package com.example.sharen.ui.profile;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityViewModel_Factory implements Factory<SecurityViewModel> {
  @Override
  public SecurityViewModel get() {
    return newInstance();
  }

  public static SecurityViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SecurityViewModel newInstance() {
    return new SecurityViewModel();
  }

  private static final class InstanceHolder {
    private static final SecurityViewModel_Factory INSTANCE = new SecurityViewModel_Factory();
  }
}
