package com.example.sharen.domain.usecase.payment;

/**
 * Use Case برای ایجاد پرداخت جدید
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J^\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u000bH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0013\u0010\u0014R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u0015"}, d2 = {"Lcom/example/sharen/domain/usecase/payment/CreatePaymentUseCase;", "", "paymentRepository", "Lcom/example/sharen/domain/repository/PaymentRepository;", "customerRepository", "Lcom/example/sharen/domain/repository/CustomerRepository;", "(Lcom/example/sharen/domain/repository/PaymentRepository;Lcom/example/sharen/domain/repository/CustomerRepository;)V", "invoke", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Payment;", "customerId", "", "amount", "", "method", "Lcom/example/sharen/domain/model/PaymentMethod;", "invoiceId", "description", "referenceNumber", "invoke-bMdYcbs", "(Ljava/lang/String;JLcom/example/sharen/domain/model/PaymentMethod;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class CreatePaymentUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.PaymentRepository paymentRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.CustomerRepository customerRepository = null;
    
    @javax.inject.Inject
    public CreatePaymentUseCase(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.PaymentRepository paymentRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.CustomerRepository customerRepository) {
        super();
    }
}