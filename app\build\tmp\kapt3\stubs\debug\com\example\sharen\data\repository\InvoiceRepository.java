package com.example.sharen.data.repository;

/**
 * رپوزیتوری مدیریت فاکتورها
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J2\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\b\u0010\tJ*\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00032\u0006\u0010\f\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\r\u0010\u000eJ8\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0011\u001a\u00020\u00102\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0014\u0010\u0015J*\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0018\u0010\u0019J\u0014\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00130\u001bH&J\u0014\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00130\u001bH&J\u0016\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00100\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H&J\u000e\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\u001bH&J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H&J\u001c\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00130\u001b2\u0006\u0010\"\u001a\u00020\u0006H&J$\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00130\u001b2\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020%H&J\u0018\u0010\'\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\u001b2\u0006\u0010(\u001a\u00020\u0006H&J\u001c\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u00130\u001b2\u0006\u0010\u0005\u001a\u00020\u0006H&J\u001e\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00130\u001b2\b\b\u0002\u0010+\u001a\u00020\u001fH&J\u000e\u0010,\u001a\b\u0012\u0004\u0012\u00020-0\u001bH&J*\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b/\u0010\u0019J2\u00100\u001a\b\u0012\u0004\u0012\u00020\u00170\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u00101\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b2\u00103J\u001c\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00130\u001b2\u0006\u00105\u001a\u00020\u0006H&J*\u00106\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0011\u001a\u00020\u0010H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b7\u00108J2\u00109\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b:\u0010\tJ2\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010<\u001a\u00020=H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b>\u0010?\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006@"}, d2 = {"Lcom/example/sharen/data/repository/InvoiceRepository;", "", "addInvoiceItem", "Lkotlin/Result;", "Lcom/example/sharen/data/model/InvoiceItem;", "invoiceId", "", "item", "addInvoiceItem-0E7RQCE", "(Ljava/lang/String;Lcom/example/sharen/data/model/InvoiceItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addPayment", "Lcom/example/sharen/data/model/Payment;", "payment", "addPayment-gIAlu-s", "(Lcom/example/sharen/data/model/Payment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInvoice", "Lcom/example/sharen/data/model/Invoice;", "invoice", "items", "", "createInvoice-0E7RQCE", "(Lcom/example/sharen/data/model/Invoice;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInvoice", "", "deleteInvoice-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllInvoices", "Lkotlinx/coroutines/flow/Flow;", "getAllPayments", "getInvoiceById", "getInvoiceCount", "", "getInvoiceItems", "getInvoicesByCustomerId", "customerId", "getInvoicesByDateRange", "startDate", "Ljava/util/Date;", "endDate", "getPaymentById", "paymentId", "getPaymentsByInvoiceId", "getRecentInvoices", "limit", "getTotalSales", "", "recalculateInvoice", "recalculateInvoice-gIAlu-s", "removeInvoiceItem", "itemId", "removeInvoiceItem-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchInvoices", "query", "updateInvoice", "updateInvoice-gIAlu-s", "(Lcom/example/sharen/data/model/Invoice;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInvoiceItem", "updateInvoiceItem-0E7RQCE", "updateInvoiceStatus", "newStatus", "Lcom/example/sharen/data/model/InvoiceStatus;", "updateInvoiceStatus-0E7RQCE", "(Ljava/lang/String;Lcom/example/sharen/data/model/InvoiceStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface InvoiceRepository {
    
    /**
     * دریافت همه فاکتورها
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Invoice>> getAllInvoices();
    
    /**
     * دریافت فاکتور با شناسه مشخص
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Invoice> getInvoiceById(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId);
    
    /**
     * جستجوی فاکتورها با کلمه کلیدی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Invoice>> searchInvoices(@org.jetbrains.annotations.NotNull
    java.lang.String query);
    
    /**
     * دریافت فاکتورهای یک مشتری
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Invoice>> getInvoicesByCustomerId(@org.jetbrains.annotations.NotNull
    java.lang.String customerId);
    
    /**
     * دریافت فاکتورهای با تاریخ مشخص
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Invoice>> getInvoicesByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate);
    
    /**
     * دریافت پرداخت‌های یک فاکتور
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getPaymentsByInvoiceId(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId);
    
    /**
     * دریافت اقلام یک فاکتور
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.InvoiceItem>> getInvoiceItems(@org.jetbrains.annotations.NotNull
    java.lang.String invoiceId);
    
    /**
     * دریافت تعداد کل فاکتورها
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Integer> getInvoiceCount();
    
    /**
     * دریافت مجموع فروش
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Long> getTotalSales();
    
    /**
     * دریافت فاکتورهای اخیر (مثلاً ۱۰ مورد آخر)
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Invoice>> getRecentInvoices(int limit);
    
    /**
     * دریافت همه پرداخت‌ها
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Payment>> getAllPayments();
    
    /**
     * دریافت پرداخت با شناسه مشخص
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharen.data.model.Payment> getPaymentById(@org.jetbrains.annotations.NotNull
    java.lang.String paymentId);
    
    /**
     * رپوزیتوری مدیریت فاکتورها
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}