// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRegisterBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnRegister;

  @NonNull
  public final TextInputEditText etConfirmPassword;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final TextInputEditText etName;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final TextInputEditText etPhone;

  @NonNull
  public final TextInputEditText etReferrerCode;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final LinearLayout layoutLogin;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout tilConfirmPassword;

  @NonNull
  public final TextInputLayout tilEmail;

  @NonNull
  public final TextInputLayout tilName;

  @NonNull
  public final TextInputLayout tilPassword;

  @NonNull
  public final TextInputLayout tilPhone;

  @NonNull
  public final TextInputLayout tilReferrerCode;

  @NonNull
  public final TextView tvHaveAccount;

  @NonNull
  public final TextView tvLogin;

  @NonNull
  public final TextView tvRegisterTitle;

  private ActivityRegisterBinding(@NonNull ScrollView rootView, @NonNull MaterialButton btnRegister,
      @NonNull TextInputEditText etConfirmPassword, @NonNull TextInputEditText etEmail,
      @NonNull TextInputEditText etName, @NonNull TextInputEditText etPassword,
      @NonNull TextInputEditText etPhone, @NonNull TextInputEditText etReferrerCode,
      @NonNull ImageView ivLogo, @NonNull LinearLayout layoutLogin,
      @NonNull ProgressBar progressBar, @NonNull TextInputLayout tilConfirmPassword,
      @NonNull TextInputLayout tilEmail, @NonNull TextInputLayout tilName,
      @NonNull TextInputLayout tilPassword, @NonNull TextInputLayout tilPhone,
      @NonNull TextInputLayout tilReferrerCode, @NonNull TextView tvHaveAccount,
      @NonNull TextView tvLogin, @NonNull TextView tvRegisterTitle) {
    this.rootView = rootView;
    this.btnRegister = btnRegister;
    this.etConfirmPassword = etConfirmPassword;
    this.etEmail = etEmail;
    this.etName = etName;
    this.etPassword = etPassword;
    this.etPhone = etPhone;
    this.etReferrerCode = etReferrerCode;
    this.ivLogo = ivLogo;
    this.layoutLogin = layoutLogin;
    this.progressBar = progressBar;
    this.tilConfirmPassword = tilConfirmPassword;
    this.tilEmail = tilEmail;
    this.tilName = tilName;
    this.tilPassword = tilPassword;
    this.tilPhone = tilPhone;
    this.tilReferrerCode = tilReferrerCode;
    this.tvHaveAccount = tvHaveAccount;
    this.tvLogin = tvLogin;
    this.tvRegisterTitle = tvRegisterTitle;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRegisterBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRegisterBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_register, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRegisterBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_register;
      MaterialButton btnRegister = ViewBindings.findChildViewById(rootView, id);
      if (btnRegister == null) {
        break missingId;
      }

      id = R.id.et_confirm_password;
      TextInputEditText etConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (etConfirmPassword == null) {
        break missingId;
      }

      id = R.id.et_email;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.et_name;
      TextInputEditText etName = ViewBindings.findChildViewById(rootView, id);
      if (etName == null) {
        break missingId;
      }

      id = R.id.et_password;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.et_phone;
      TextInputEditText etPhone = ViewBindings.findChildViewById(rootView, id);
      if (etPhone == null) {
        break missingId;
      }

      id = R.id.et_referrer_code;
      TextInputEditText etReferrerCode = ViewBindings.findChildViewById(rootView, id);
      if (etReferrerCode == null) {
        break missingId;
      }

      id = R.id.iv_logo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.layout_login;
      LinearLayout layoutLogin = ViewBindings.findChildViewById(rootView, id);
      if (layoutLogin == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.til_confirm_password;
      TextInputLayout tilConfirmPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilConfirmPassword == null) {
        break missingId;
      }

      id = R.id.til_email;
      TextInputLayout tilEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilEmail == null) {
        break missingId;
      }

      id = R.id.til_name;
      TextInputLayout tilName = ViewBindings.findChildViewById(rootView, id);
      if (tilName == null) {
        break missingId;
      }

      id = R.id.til_password;
      TextInputLayout tilPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPassword == null) {
        break missingId;
      }

      id = R.id.til_phone;
      TextInputLayout tilPhone = ViewBindings.findChildViewById(rootView, id);
      if (tilPhone == null) {
        break missingId;
      }

      id = R.id.til_referrer_code;
      TextInputLayout tilReferrerCode = ViewBindings.findChildViewById(rootView, id);
      if (tilReferrerCode == null) {
        break missingId;
      }

      id = R.id.tv_have_account;
      TextView tvHaveAccount = ViewBindings.findChildViewById(rootView, id);
      if (tvHaveAccount == null) {
        break missingId;
      }

      id = R.id.tv_login;
      TextView tvLogin = ViewBindings.findChildViewById(rootView, id);
      if (tvLogin == null) {
        break missingId;
      }

      id = R.id.tv_register_title;
      TextView tvRegisterTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvRegisterTitle == null) {
        break missingId;
      }

      return new ActivityRegisterBinding((ScrollView) rootView, btnRegister, etConfirmPassword,
          etEmail, etName, etPassword, etPhone, etReferrerCode, ivLogo, layoutLogin, progressBar,
          tilConfirmPassword, tilEmail, tilName, tilPassword, tilPhone, tilReferrerCode,
          tvHaveAccount, tvLogin, tvRegisterTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
