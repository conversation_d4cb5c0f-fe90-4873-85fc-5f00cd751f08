package com.example.sharen.presentation.viewmodel

import androidx.lifecycle.viewModelScope
import com.example.sharen.core.base.BaseViewModel
import com.example.sharen.domain.model.Customer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date
import javax.inject.Inject

/**
 * ViewModel برای مدیریت مشتریان
 */
@HiltViewModel
class CustomerViewModel @Inject constructor(
    // TODO: اضافه کردن CustomerRepository
) : BaseViewModel() {

    private val _customers = MutableStateFlow<List<Customer>>(emptyList())
    val customers: StateFlow<List<Customer>> = _customers.asStateFlow()

    private val _selectedCustomer = MutableStateFlow<Customer?>(null)
    val selectedCustomer: StateFlow<Customer?> = _selectedCustomer.asStateFlow()

    init {
        loadCustomers()
    }

    /**
     * بارگذاری لیست مشتریان
     */
    fun loadCustomers() {
        launchWithLoading {
            // شبیه‌سازی بارگذاری داده‌ها
            kotlinx.coroutines.delay(1000)
            
            // داده‌های نمونه
            val sampleCustomers = listOf(
                Customer(
                    id = "1",
                    userId = "user1",
                    name = "احمد محمدی",
                    phone = "09123456789",
                    address = "تهران، خیابان ولیعصر",
                    creditLimit = 5_000_000,
                    totalPurchases = 2_500_000,
                    totalPayments = 2_000_000,
                    totalDebt = 500_000,
                    lastPurchaseDate = Date(),
                    createdAt = Date()
                ),
                Customer(
                    id = "2",
                    userId = "user2",
                    name = "فاطمه احمدی",
                    phone = "09987654321",
                    address = "اصفهان، خیابان چهارباغ",
                    creditLimit = 3_000_000,
                    totalPurchases = 1_800_000,
                    totalPayments = 1_800_000,
                    totalDebt = 0,
                    lastPurchaseDate = Date(System.currentTimeMillis() - 86400000), // دیروز
                    createdAt = Date()
                ),
                Customer(
                    id = "3",
                    userId = "user3",
                    name = "علی رضایی",
                    phone = "09111111111",
                    address = "شیراز، خیابان زند",
                    creditLimit = 10_000_000,
                    totalPurchases = 8_500_000,
                    totalPayments = 7_000_000,
                    totalDebt = 1_500_000,
                    lastPurchaseDate = Date(System.currentTimeMillis() - 172800000), // 2 روز پیش
                    createdAt = Date()
                )
            )
            
            _customers.value = sampleCustomers
        }
    }

    /**
     * جستجوی مشتریان
     */
    fun searchCustomers(query: String) {
        launchWithLoading(showLoading = false) {
            kotlinx.coroutines.delay(300) // شبیه‌سازی تأخیر جستجو
            
            val allCustomers = _customers.value
            val filteredCustomers = allCustomers.filter { customer ->
                customer.name.contains(query, ignoreCase = true) ||
                customer.phone.contains(query) ||
                customer.address?.contains(query, ignoreCase = true) == true
            }
            
            _customers.value = filteredCustomers
        }
    }

    /**
     * دریافت مشتری با شناسه
     */
    fun getCustomerById(customerId: String) {
        launchWithLoading {
            val customer = _customers.value.find { it.id == customerId }
            _selectedCustomer.value = customer
            
            if (customer == null) {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * افزودن مشتری جدید
     */
    fun addCustomer(customer: Customer) {
        launchWithLoading {
            // شبیه‌سازی ذخیره در دیتابیس
            kotlinx.coroutines.delay(1000)
            
            val newCustomer = customer.copy(
                id = "new_${System.currentTimeMillis()}",
                createdAt = Date()
            )
            
            val currentList = _customers.value.toMutableList()
            currentList.add(0, newCustomer) // اضافه کردن به ابتدای لیست
            _customers.value = currentList
            
            showSuccess("مشتری جدید با موفقیت اضافه شد")
        }
    }

    /**
     * بروزرسانی مشتری
     */
    fun updateCustomer(customer: Customer) {
        launchWithLoading {
            // شبیه‌سازی بروزرسانی در دیتابیس
            kotlinx.coroutines.delay(1000)
            
            val currentList = _customers.value.toMutableList()
            val index = currentList.indexOfFirst { it.id == customer.id }
            
            if (index != -1) {
                currentList[index] = customer.copy(updatedAt = Date())
                _customers.value = currentList
                _selectedCustomer.value = currentList[index]
                showSuccess("اطلاعات مشتری با موفقیت بروزرسانی شد")
            } else {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * حذف مشتری
     */
    fun deleteCustomer(customerId: String) {
        launchWithLoading {
            // شبیه‌سازی حذف از دیتابیس
            kotlinx.coroutines.delay(1000)
            
            val currentList = _customers.value.toMutableList()
            val removed = currentList.removeAll { it.id == customerId }
            
            if (removed) {
                _customers.value = currentList
                showSuccess("مشتری با موفقیت حذف شد")
            } else {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * بروزرسانی اعتبار مشتری
     */
    fun updateCustomerCredit(customerId: String, newCreditLimit: Long) {
        launchWithLoading {
            val currentList = _customers.value.toMutableList()
            val index = currentList.indexOfFirst { it.id == customerId }
            
            if (index != -1) {
                val updatedCustomer = currentList[index].copy(
                    creditLimit = newCreditLimit,
                    updatedAt = Date()
                )
                currentList[index] = updatedCustomer
                _customers.value = currentList
                _selectedCustomer.value = updatedCustomer
                showSuccess("حد اعتبار مشتری بروزرسانی شد")
            } else {
                showError("مشتری یافت نشد")
            }
        }
    }

    /**
     * بروزرسانی داده‌ها
     */
    fun refreshCustomers() {
        loadCustomers()
    }

    /**
     * فیلتر مشتریان بدهکار
     */
    fun getDebtorCustomers() {
        launchWithLoading(showLoading = false) {
            val allCustomers = _customers.value
            val debtorCustomers = allCustomers.filter { it.totalDebt > 0 }
            _customers.value = debtorCustomers
        }
    }

    /**
     * فیلتر مشتریان بدون بدهی
     */
    fun getClearCustomers() {
        launchWithLoading(showLoading = false) {
            val allCustomers = _customers.value
            val clearCustomers = allCustomers.filter { it.totalDebt == 0L }
            _customers.value = clearCustomers
        }
    }
}
