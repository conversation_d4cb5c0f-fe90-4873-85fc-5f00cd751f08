<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_installment_list" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_installment_list.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_installment_list_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="58" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="8" startOffset="8" endLine="14" endOffset="47"/></Target><Target id="@+id/swipeRefresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="18" startOffset="4" endLine="31" endOffset="59"/></Target><Target id="@+id/recyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="24" startOffset="8" endLine="29" endOffset="35"/></Target><Target id="@+id/fabAddInstallment" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="33" startOffset="4" endLine="40" endOffset="42"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="42" startOffset="4" endLine="47" endOffset="35"/></Target><Target id="@+id/textViewEmpty" view="TextView"><Expressions/><location startLine="49" startOffset="4" endLine="56" endOffset="35"/></Target></Targets></Layout>