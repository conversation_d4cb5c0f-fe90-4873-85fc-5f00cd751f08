package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UpdateCustomerCreditUseCase_Factory implements Factory<UpdateCustomerCreditUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public UpdateCustomerCreditUseCase_Factory(
      Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public UpdateCustomerCreditUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static UpdateCustomerCreditUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new UpdateCustomerCreditUseCase_Factory(customerRepositoryProvider);
  }

  public static UpdateCustomerCreditUseCase newInstance(CustomerRepository customerRepository) {
    return new UpdateCustomerCreditUseCase(customerRepository);
  }
}
