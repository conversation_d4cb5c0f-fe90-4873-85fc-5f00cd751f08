package com.example.sharen.data.model

import java.util.Date

/**
 * مدل داده اقساط
 */
data class Installment(
    val id: Long = 0,
    val customerId: Long,
    val totalAmount: Double,
    val paidAmount: Double,
    val dueDate: Date,
    val notes: String? = null,
    val status: InstallmentStatus = InstallmentStatus.PENDING
) {
    // آیا قسط دیرکرد دارد؟
    val isOverdue: Boolean
        get() = !isPaid && dueDate.before(Date())

    // تعداد روزهای دیرکرد
    val overduedays: Int
        get() {
            if (!isOverdue) return 0
            val today = Date()
            val diffTime = today.time - dueDate.time
            return (diffTime / (1000 * 60 * 60 * 24)).toInt()
        }

    // آیا قسط پرداخت شده است؟
    val isPaid: Boolean
        get() = status == InstallmentStatus.PAID
}

/**
 * وضعیت‌های قسط
 */
enum class InstallmentStatus {
    PENDING,        // در انتظار پرداخت
    PARTIALLY_PAID, // پرداخت جزئی
    PAID,          // پرداخت شده
    OVERDUE        // دیرکرد
}