<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_installment_edit" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_installment_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_installment_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="148" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="8" endLine="18" endOffset="54"/></Target><Target id="@+id/editTextTotalAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="40" startOffset="16" endLine="44" endOffset="55"/></Target><Target id="@+id/editTextPaidAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="55" startOffset="16" endLine="59" endOffset="55"/></Target><Target id="@+id/editTextDueDate" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="70" startOffset="16" endLine="74" endOffset="46"/></Target><Target id="@+id/chipGroupStatus" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="85" startOffset="12" endLine="113" endOffset="56"/></Target><Target id="@+id/chipPending" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="92" startOffset="16" endLine="97" endOffset="74"/></Target><Target id="@+id/chipPaid" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="99" startOffset="16" endLine="104" endOffset="74"/></Target><Target id="@+id/chipOverdue" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="106" startOffset="16" endLine="111" endOffset="74"/></Target><Target id="@+id/editTextNotes" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="122" startOffset="16" endLine="127" endOffset="42"/></Target><Target id="@+id/buttonSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="131" startOffset="12" endLine="135" endOffset="45"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="141" startOffset="4" endLine="146" endOffset="35"/></Target></Targets></Layout>