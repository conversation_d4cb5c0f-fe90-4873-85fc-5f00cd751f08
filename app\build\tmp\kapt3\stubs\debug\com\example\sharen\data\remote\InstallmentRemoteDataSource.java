package com.example.sharen.data.remote;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bJ*\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u00062\u0006\u0010\u000e\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u000f\u0010\u0010J*\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0013\u0010\u000bJH\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u00062\u0006\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u0012\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001fJ%\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001f2\u0006\u0010!\u001a\u00020\tH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u000bJ*\u0010\"\u001a\b\u0012\u0004\u0012\u00020\r0\u00062\u0006\u0010\b\u001a\u00020\tH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b#\u0010\u000bJ>\u0010$\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010%0\u00062\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\u001aH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\'\u0010(J\u001a\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001f2\u0006\u0010!\u001a\u00020\tJ\"\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001f2\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\u001aJ\u001a\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001f2\u0006\u0010,\u001a\u00020-J\u001d\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001fH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010/J%\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00150\u001f2\u0006\u0010!\u001a\u00020\tH\u0086@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u000bJ2\u00101\u001a\b\u0012\u0004\u0012\u00020\r0\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u00102\u001a\u00020\u0007H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b3\u00104J*\u00105\u001a\b\u0012\u0004\u0012\u00020\r0\u00062\u0006\u0010\u000e\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b6\u0010\u0010R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u00067"}, d2 = {"Lcom/example/sharen/data/remote/InstallmentRemoteDataSource;", "", "apiService", "Lcom/example/sharen/data/remote/SupabaseApiService;", "(Lcom/example/sharen/data/remote/SupabaseApiService;)V", "calculateRemainingAmount", "Lkotlin/Result;", "", "installmentId", "", "calculateRemainingAmount-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createInstallment", "Lcom/example/sharen/data/model/Installment;", "installment", "createInstallment-gIAlu-s", "(Lcom/example/sharen/data/model/Installment;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteInstallment", "", "deleteInstallment-gIAlu-s", "generateInstallmentSchedule", "", "totalAmount", "numberOfInstallments", "", "startDate", "Ljava/util/Date;", "interestRate", "generateInstallmentSchedule-yxL6bBk", "(DILjava/util/Date;DLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllInstallments", "Lkotlinx/coroutines/flow/Flow;", "getCustomerInstallmentHistory", "customerId", "getInstallment", "getInstallment-gIAlu-s", "getInstallmentStatistics", "", "endDate", "getInstallmentStatistics-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstallmentsByCustomer", "getInstallmentsByDateRange", "getInstallmentsByStatus", "status", "Lcom/example/sharen/data/model/InstallmentStatus;", "getOverdueInstallments", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUpcomingInstallments", "payInstallment", "amount", "payInstallment-0E7RQCE", "(Ljava/lang/String;DLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateInstallment", "updateInstallment-gIAlu-s", "app_debug"})
public final class InstallmentRemoteDataSource {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.remote.SupabaseApiService apiService = null;
    
    @javax.inject.Inject
    public InstallmentRemoteDataSource(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.remote.SupabaseApiService apiService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getAllInstallments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallmentsByCustomer(@org.jetbrains.annotations.NotNull
    java.lang.String customerId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallmentsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.data.model.Installment>> getInstallmentsByStatus(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.InstallmentStatus status) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getUpcomingInstallments(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Installment>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getOverdueInstallments(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Installment>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getCustomerInstallmentHistory(@org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.example.sharen.data.model.Installment>>> $completion) {
        return null;
    }
}