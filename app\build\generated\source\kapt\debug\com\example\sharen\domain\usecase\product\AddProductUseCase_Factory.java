package com.example.sharen.domain.usecase.product;

import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AddProductUseCase_Factory implements Factory<AddProductUseCase> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public AddProductUseCase_Factory(Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  @Override
  public AddProductUseCase get() {
    return newInstance(productRepositoryProvider.get());
  }

  public static AddProductUseCase_Factory create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new AddProductUseCase_Factory(productRepositoryProvider);
  }

  public static AddProductUseCase newInstance(ProductRepository productRepository) {
    return new AddProductUseCase(productRepository);
  }
}
