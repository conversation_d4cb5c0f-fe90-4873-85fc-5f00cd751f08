package com.example.sharen.data.local.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import com.example.sharen.data.model.OrderItem
import java.util.Date

@Entity(
    tableName = "order_items",
    foreignKeys = [
        ForeignKey(
            entity = OrderEntity::class,
            parentColumns = ["id"],
            childColumns = ["orderId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = ProductEntity::class,
            parentColumns = ["id"],
            childColumns = ["productId"],
            onDelete = ForeignKey.RESTRICT
        )
    ],
    indices = [
        Index("orderId"),
        Index("productId")
    ]
)
data class OrderItemEntity(
    @PrimaryKey
    val id: String,
    val orderId: String,
    val productId: String,
    val quantity: Int,
    val unitPrice: Long,
    val discountAmount: Long = 0,
    val totalAmount: Long,
    val notes: String?,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toOrderItem(): OrderItem = OrderItem(
        id = id,
        orderId = orderId,
        productId = productId,
        quantity = quantity,
        unitPrice = unitPrice,
        discountAmount = discountAmount,
        totalAmount = totalAmount,
        notes = notes,
        createdAt = Date(createdAt),
        updatedAt = Date(updatedAt)
    )
    
    companion object {
        fun fromOrderItem(orderItem: OrderItem): OrderItemEntity = OrderItemEntity(
            id = orderItem.id,
            orderId = orderItem.orderId,
            productId = orderItem.productId,
            quantity = orderItem.quantity,
            unitPrice = orderItem.unitPrice,
            discountAmount = orderItem.discountAmount,
            totalAmount = orderItem.totalAmount,
            notes = orderItem.notes,
            createdAt = orderItem.createdAt.time,
            updatedAt = orderItem.updatedAt.time
        )
    }
} 