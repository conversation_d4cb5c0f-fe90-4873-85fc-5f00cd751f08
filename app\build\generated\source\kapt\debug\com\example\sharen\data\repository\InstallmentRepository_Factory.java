package com.example.sharen.data.repository;

import com.example.sharen.data.api.InstallmentApi;
import com.example.sharen.data.local.dao.InstallmentDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InstallmentRepository_Factory implements Factory<InstallmentRepository> {
  private final Provider<InstallmentApi> apiProvider;

  private final Provider<InstallmentDao> daoProvider;

  public InstallmentRepository_Factory(Provider<InstallmentApi> apiProvider,
      Provider<InstallmentDao> daoProvider) {
    this.apiProvider = apiProvider;
    this.daoProvider = daoProvider;
  }

  @Override
  public InstallmentRepository get() {
    return newInstance(apiProvider.get(), daoProvider.get());
  }

  public static InstallmentRepository_Factory create(Provider<InstallmentApi> apiProvider,
      Provider<InstallmentDao> daoProvider) {
    return new InstallmentRepository_Factory(apiProvider, daoProvider);
  }

  public static InstallmentRepository newInstance(InstallmentApi api, InstallmentDao dao) {
    return new InstallmentRepository(api, dao);
  }
}
