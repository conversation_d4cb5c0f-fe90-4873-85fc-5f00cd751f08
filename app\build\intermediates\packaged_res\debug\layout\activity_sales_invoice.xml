<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl"
    tools:context=".ui.invoice.SalesInvoiceActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- هدر فاکتور -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/invoice_details"
                        android:textAppearance="@style/TextAppearance.AppCompat.Large"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/invoice_number"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvInvoiceNumber"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            tools:text="INV-1001" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/customer_name"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvCustomerName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/select_customer"
                            android:textColor="@color/text_secondary" />

                        <Button
                            android:id="@+id/btnSelectCustomer"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/select_customer"
                            android:textColor="@color/primary" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- آیتم‌های فاکتور -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/invoice_items"
                            android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/btnAddItem"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/add_item"
                            android:textColor="@color/primary"
                            app:icon="@android:drawable/ic_input_add"
                            app:iconTint="@color/primary" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvInvoiceItems"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_sales_invoice_product" />

                    <TextView
                        android:id="@+id/emptyView"
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:gravity="center"
                        android:text="@string/no_invoice_items"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:visibility="visible" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- مبالغ و جزئیات -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/invoice_total"
                        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/invoice_total"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start"
                            tools:text="500,000 تومان" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/discount"
                            android:textStyle="bold" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/discount"
                            app:hintEnabled="false">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etDiscount"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number"
                                android:maxLines="1"
                                android:text="0" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/tax"
                            android:textStyle="bold" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/tax"
                            app:hintEnabled="false">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etTax"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="number"
                                android:maxLines="1"
                                android:text="0" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <Button
                        android:id="@+id/btnRecalculate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="8dp"
                        android:text="@string/recalculate" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/divider" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/final_amount"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvFinalAmount"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="start"
                            android:textColor="@color/primary"
                            android:textStyle="bold"
                            tools:text="495,000 تومان" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- دکمه‌های پایین -->
            <Button
                android:id="@+id/btnSave"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/save"
                android:textStyle="bold" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 