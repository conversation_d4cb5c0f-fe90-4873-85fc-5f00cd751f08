<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_installment_detail" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\fragment_installment_detail.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_installment_detail_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="237" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="10" startOffset="8" endLine="17" endOffset="47"/></Target><Target id="@+id/textViewTotalAmount" view="TextView"><Expressions/><location startLine="51" startOffset="20" endLine="56" endOffset="80"/></Target><Target id="@+id/textViewPaidAmount" view="TextView"><Expressions/><location startLine="71" startOffset="20" endLine="76" endOffset="76"/></Target><Target id="@+id/textViewRemainingAmount" view="TextView"><Expressions/><location startLine="85" startOffset="20" endLine="90" endOffset="76"/></Target><Target id="@+id/textViewDueDate" view="TextView"><Expressions/><location startLine="115" startOffset="20" endLine="120" endOffset="76"/></Target><Target id="@+id/chipStatus" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="135" startOffset="20" endLine="139" endOffset="56"/></Target><Target id="@+id/textViewNotes" view="TextView"><Expressions/><location startLine="164" startOffset="20" endLine="169" endOffset="76"/></Target><Target id="@+id/buttonEdit" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="180" startOffset="16" endLine="187" endOffset="49"/></Target><Target id="@+id/buttonDelete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="189" startOffset="16" endLine="196" endOffset="51"/></Target><Target id="@+id/buttonPay" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="206" startOffset="16" endLine="213" endOffset="48"/></Target><Target id="@+id/buttonRemind" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="215" startOffset="16" endLine="222" endOffset="51"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="230" startOffset="4" endLine="235" endOffset="35"/></Target></Targets></Layout>