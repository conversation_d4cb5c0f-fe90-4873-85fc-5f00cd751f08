package com.example.sharen.di;

import com.example.sharen.data.repository.InstallmentRepository;
import com.example.sharen.ui.installment.InstallmentViewModel;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("dagger.hilt.android.scopes.ViewModelScoped")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ViewModelModule_ProvideInstallmentViewModelFactory implements Factory<InstallmentViewModel> {
  private final Provider<InstallmentRepository> repositoryProvider;

  public ViewModelModule_ProvideInstallmentViewModelFactory(
      Provider<InstallmentRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public InstallmentViewModel get() {
    return provideInstallmentViewModel(repositoryProvider.get());
  }

  public static ViewModelModule_ProvideInstallmentViewModelFactory create(
      Provider<InstallmentRepository> repositoryProvider) {
    return new ViewModelModule_ProvideInstallmentViewModelFactory(repositoryProvider);
  }

  public static InstallmentViewModel provideInstallmentViewModel(InstallmentRepository repository) {
    return Preconditions.checkNotNullFromProvides(ViewModelModule.INSTANCE.provideInstallmentViewModel(repository));
  }
}
