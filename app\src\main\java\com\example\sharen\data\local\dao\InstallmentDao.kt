package com.example.sharen.data.local.dao

import androidx.room.*
import com.example.sharen.data.local.entity.InstallmentEntity
import com.example.sharen.data.model.InstallmentStatus
import kotlinx.coroutines.flow.Flow
import java.util.Date

@Dao
interface InstallmentDao {
    @Query("SELECT * FROM installments")
    fun getAll(): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE id = :id")
    suspend fun getById(id: Long): InstallmentEntity?

    @Query("SELECT * FROM installments WHERE customerId = :customerId")
    fun getByCustomerId(customerId: Long): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE dueDate BETWEEN :startDate AND :endDate")
    fun getByDateRange(startDate: Date, endDate: Date): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE status = :status")
    fun getByStatus(status: InstallmentStatus): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE customerId = :customerId AND dueDate > datetime('now') ORDER BY dueDate ASC")
    fun getUpcomingByCustomerId(customerId: Long): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE status = 'OVERDUE'")
    fun getOverdue(): Flow<List<InstallmentEntity>>

    @Query("SELECT * FROM installments WHERE customerId = :customerId ORDER BY dueDate DESC")
    fun getHistoryByCustomerId(customerId: Long): Flow<List<InstallmentEntity>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(installment: InstallmentEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(installments: List<InstallmentEntity>)

    @Update
    suspend fun update(installment: InstallmentEntity)

    @Delete
    suspend fun delete(installment: InstallmentEntity)

    @Query("DELETE FROM installments WHERE id = :id")
    suspend fun deleteById(id: Long)
} 