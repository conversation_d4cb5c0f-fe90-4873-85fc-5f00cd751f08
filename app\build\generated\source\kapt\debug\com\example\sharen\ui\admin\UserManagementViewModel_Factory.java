package com.example.sharen.ui.admin;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserManagementViewModel_Factory implements Factory<UserManagementViewModel> {
  private final Provider<NonExistentClass> userRepositoryProvider;

  public UserManagementViewModel_Factory(Provider<NonExistentClass> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public UserManagementViewModel get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static UserManagementViewModel_Factory create(
      Provider<NonExistentClass> userRepositoryProvider) {
    return new UserManagementViewModel_Factory(userRepositoryProvider);
  }

  public static UserManagementViewModel newInstance(NonExistentClass userRepository) {
    return new UserManagementViewModel(userRepository);
  }
}
