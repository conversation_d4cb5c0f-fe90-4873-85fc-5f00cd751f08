// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemInstallmentBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final Chip chipStatus;

  @NonNull
  public final TextView textViewDueDate;

  @NonNull
  public final TextView textViewNotes;

  @NonNull
  public final TextView textViewPaidAmount;

  @NonNull
  public final TextView textViewTotalAmount;

  private ItemInstallmentBinding(@NonNull MaterialCardView rootView, @NonNull Chip chipStatus,
      @NonNull TextView textViewDueDate, @NonNull TextView textViewNotes,
      @NonNull TextView textViewPaidAmount, @NonNull TextView textViewTotalAmount) {
    this.rootView = rootView;
    this.chipStatus = chipStatus;
    this.textViewDueDate = textViewDueDate;
    this.textViewNotes = textViewNotes;
    this.textViewPaidAmount = textViewPaidAmount;
    this.textViewTotalAmount = textViewTotalAmount;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemInstallmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemInstallmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_installment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemInstallmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chipStatus;
      Chip chipStatus = ViewBindings.findChildViewById(rootView, id);
      if (chipStatus == null) {
        break missingId;
      }

      id = R.id.textViewDueDate;
      TextView textViewDueDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewDueDate == null) {
        break missingId;
      }

      id = R.id.textViewNotes;
      TextView textViewNotes = ViewBindings.findChildViewById(rootView, id);
      if (textViewNotes == null) {
        break missingId;
      }

      id = R.id.textViewPaidAmount;
      TextView textViewPaidAmount = ViewBindings.findChildViewById(rootView, id);
      if (textViewPaidAmount == null) {
        break missingId;
      }

      id = R.id.textViewTotalAmount;
      TextView textViewTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotalAmount == null) {
        break missingId;
      }

      return new ItemInstallmentBinding((MaterialCardView) rootView, chipStatus, textViewDueDate,
          textViewNotes, textViewPaidAmount, textViewTotalAmount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
