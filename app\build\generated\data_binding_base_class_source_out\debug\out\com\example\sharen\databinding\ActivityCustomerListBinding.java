// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCustomerListBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final CardView cardSearch;

  @NonNull
  public final EditText etSearch;

  @NonNull
  public final FloatingActionButton fabAddCustomer;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvCustomers;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvEmptyState;

  @NonNull
  public final TextView tvTotalCustomers;

  private ActivityCustomerListBinding(@NonNull CoordinatorLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull CardView cardSearch,
      @NonNull EditText etSearch, @NonNull FloatingActionButton fabAddCustomer,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView rvCustomers, @NonNull Toolbar toolbar,
      @NonNull TextView tvEmptyState, @NonNull TextView tvTotalCustomers) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.cardSearch = cardSearch;
    this.etSearch = etSearch;
    this.fabAddCustomer = fabAddCustomer;
    this.progressBar = progressBar;
    this.rvCustomers = rvCustomers;
    this.toolbar = toolbar;
    this.tvEmptyState = tvEmptyState;
    this.tvTotalCustomers = tvTotalCustomers;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCustomerListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCustomerListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_customer_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCustomerListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottom_navigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.card_search;
      CardView cardSearch = ViewBindings.findChildViewById(rootView, id);
      if (cardSearch == null) {
        break missingId;
      }

      id = R.id.et_search;
      EditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.fab_add_customer;
      FloatingActionButton fabAddCustomer = ViewBindings.findChildViewById(rootView, id);
      if (fabAddCustomer == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rv_customers;
      RecyclerView rvCustomers = ViewBindings.findChildViewById(rootView, id);
      if (rvCustomers == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_empty_state;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tv_total_customers;
      TextView tvTotalCustomers = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalCustomers == null) {
        break missingId;
      }

      return new ActivityCustomerListBinding((CoordinatorLayout) rootView, bottomNavigation,
          cardSearch, etSearch, fabAddCustomer, progressBar, rvCustomers, toolbar, tvEmptyState,
          tvTotalCustomers);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
