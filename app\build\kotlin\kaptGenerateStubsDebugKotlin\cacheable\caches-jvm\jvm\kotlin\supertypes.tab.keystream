com.example.sharen.MainActivity$com.example.sharen.SharenApplication)com.example.sharen.core.base.BaseActivity)com.example.sharen.core.base.BaseFragment*com.example.sharen.core.base.BaseViewModel)com.example.sharen.data.local.AppDatabase,com.example.sharen.data.local.SharenDatabase/com.example.sharen.data.model.InstallmentStatus+com.example.sharen.data.model.InvoiceStatus)com.example.sharen.data.model.PaymentType.com.example.sharen.data.model.NotificationType)com.example.sharen.data.model.OrderStatus+com.example.sharen.data.model.PaymentMethod+com.example.sharen.data.model.PaymentStatus*com.example.sharen.data.model.ExportFormat(com.example.sharen.data.model.ReportType,com.example.sharen.data.model.Result.Success*com.example.sharen.data.model.Result.Error,com.example.sharen.data.model.Result.Loading-com.example.sharen.data.model.TransactionType&com.example.sharen.data.model.UserRole5com.example.sharen.data.repository.AuthRepositoryImpl9com.example.sharen.data.repository.CustomerRepositoryImpl<com.example.sharen.data.repository.InstallmentRepositoryImpl8com.example.sharen.data.repository.InvoiceRepositoryImpl=com.example.sharen.data.repository.NotificationRepositoryImpl8com.example.sharen.data.repository.ProductRepositoryImpl7com.example.sharen.data.repository.ReportRepositoryImpl5com.example.sharen.data.repository.UserRepositoryImpl:com.example.sharen.data.repository.impl.AuthRepositoryImpl:com.example.sharen.data.repository.impl.UserRepositoryImpl(com.example.sharen.domain.model.Customer,com.example.sharen.domain.model.CreditStatus+com.example.sharen.domain.model.Installment1com.example.sharen.domain.model.InstallmentStatus'com.example.sharen.domain.model.Invoice+com.example.sharen.domain.model.InvoiceItem-com.example.sharen.domain.model.InvoiceStatus+com.example.sharen.domain.model.PaymentType%com.example.sharen.domain.model.Order+com.example.sharen.domain.model.OrderStatus)com.example.sharen.domain.model.OrderItem'com.example.sharen.domain.model.Payment-com.example.sharen.domain.model.PaymentStatus-com.example.sharen.domain.model.PaymentMethod'com.example.sharen.domain.model.Product+com.example.sharen.domain.model.ProductSize,com.example.sharen.domain.model.ProductColor+com.example.sharen.domain.model.ProductType&com.example.sharen.domain.model.Season&com.example.sharen.domain.model.Gender+com.example.sharen.domain.model.StockStatus+com.example.sharen.domain.model.Transaction/com.example.sharen.domain.model.TransactionType$com.example.sharen.domain.model.User(com.example.sharen.domain.model.UserRole*com.example.sharen.domain.model.UserStatus=<EMAIL>@com.example.sharen.domain.usecase.customer.CustomerFilter.Search>com.example.sharen.presentation.ui.auth.ForgotPasswordActivity5com.example.sharen.presentation.ui.auth.LoginActivity8com.example.sharen.presentation.ui.auth.RegisterActivity<com.example.sharen.presentation.ui.customer.CustomerActivityCcom.example.sharen.presentation.ui.customer.adapter.CustomerAdapterVcom.example.sharen.presentation.ui.customer.adapter.CustomerAdapter.CustomerViewHolderXcom.example.sharen.presentation.ui.customer.adapter.CustomerAdapter.CustomerDiffCallback>com.example.sharen.presentation.ui.dashboard.DashboardActivity7com.example.sharen.presentation.viewmodel.AuthViewModel9com.example.sharen.presentation.viewmodel.LoginState.Idle<com.example.sharen.presentation.viewmodel.LoginState.Loading<com.example.sharen.presentation.viewmodel.LoginState.Success:com.example.sharen.presentation.viewmodel.LoginState.Error;com.example.sharen.presentation.viewmodel.CustomerViewModel<com.example.sharen.presentation.viewmodel.DashboardViewModel2com.example.sharen.ui.admin.UserManagementActivity3com.example.sharen.ui.admin.UserManagementViewModel(com.example.sharen.ui.auth.AuthViewModel1com.example.sharen.ui.auth.ForgotPasswordActivity(com.example.sharen.ui.auth.LoginActivity+com.example.sharen.ui.auth.RegisterActivity)com.example.sharen.ui.auth.SplashActivity.com.example.sharen.ui.customer.CustomerAdapterAcom.example.sharen.ui.customer.CustomerAdapter.CustomerViewHolderCcom.example.sharen.ui.customer.CustomerAdapter.CustomerDiffCallback6com.example.sharen.ui.customer.CustomerDetailsActivity7com.example.sharen.ui.customer.CustomerDetailsViewModel3com.example.sharen.ui.customer.CustomerFormActivity4com.example.sharen.ui.customer.CustomerFormViewModel3com.example.sharen.ui.customer.CustomerListActivity4com.example.sharen.ui.customer.CustomerListViewModel1com.example.sharen.ui.dashboard.DashboardActivity2com.example.sharen.ui.dashboard.DashboardViewModel2com.example.sharen.ui.dashboard.TransactionAdapterHcom.example.sharen.ui.dashboard.TransactionAdapter.TransactionViewHolderJcom.example.sharen.ui.dashboard.TransactionAdapter.TransactionDiffCallback;com.example.sharen.ui.installment.InstallmentDetailFragment9com.example.sharen.ui.installment.InstallmentEditFragment9com.example.sharen.ui.installment.InstallmentListFragment6com.example.sharen.ui.installment.InstallmentViewModel<com.example.sharen.ui.installment.adapter.InstallmentAdapterRcom.example.sharen.ui.installment.adapter.InstallmentAdapter.InstallmentViewHolderTcom.example.sharen.ui.installment.adapter.InstallmentAdapter.InstallmentDiffCallback,com.example.sharen.ui.invoice.InvoiceAdapter><EMAIL>,com.example.sharen.ui.invoice.PaymentAdapter><EMAIL>,com.example.sharen.ui.payment.PaymentAdapter><EMAIL>,com.example.sharen.ui.product.ProductAdapter><EMAIL>:com.example.sharen.ui.profile.NotificationSettingsActivity;com.example.sharen.ui.profile.NotificationSettingsViewModel4com.example.sharen.ui.profile.PasswordChangeActivity5com.example.sharen.ui.profile.PasswordChangeViewModel-com.example.sharen.ui.profile.ProfileActivity.com.example.sharen.ui.profile.ProfileViewModel.com.example.sharen.ui.profile.SecurityActivity/com.example.sharen.ui.profile.SecurityViewModel+com.example.sharen.ui.report.ReportActivity,com.example.sharen.ui.report.ReportViewModel/com.example.sharen.ui.settings.SettingsActivity0com.example.sharen.ui.settings.SettingsViewModel1com.example.sharen.databinding.ItemInvoiceBinding1com.example.sharen.databinding.ItemPaymentBinding8com.example.sharen.databinding.ItemInvoiceProductBinding1com.example.sharen.databinding.ItemProductBinding2com.example.sharen.databinding.ItemCustomerBinding5com.example.sharen.databinding.ItemTransactionBinding6com.example.sharen.databinding.ItemNotificationBinding5com.example.sharen.databinding.ItemInstallmentBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    