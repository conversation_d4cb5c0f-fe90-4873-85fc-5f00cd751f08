com.example.sharen.MainActivity$com.example.sharen.SharenApplication)com.example.sharen.data.local.AppDatabase,com.example.sharen.data.local.SharenDatabase/com.example.sharen.data.model.InstallmentStatus+com.example.sharen.data.model.InvoiceStatus)com.example.sharen.data.model.PaymentType.com.example.sharen.data.model.NotificationType)com.example.sharen.data.model.OrderStatus+com.example.sharen.data.model.PaymentMethod+com.example.sharen.data.model.PaymentStatus*com.example.sharen.data.model.ExportFormat(com.example.sharen.data.model.ReportType,com.example.sharen.data.model.Result.Success*com.example.sharen.data.model.Result.Error,com.example.sharen.data.model.Result.Loading-com.example.sharen.data.model.TransactionType&com.example.sharen.data.model.UserRole5com.example.sharen.data.repository.AuthRepositoryImpl9com.example.sharen.data.repository.CustomerRepositoryImpl<com.example.sharen.data.repository.InstallmentRepositoryImpl8com.example.sharen.data.repository.InvoiceRepositoryImpl=com.example.sharen.data.repository.NotificationRepositoryImpl8com.example.sharen.data.repository.PaymentRepositoryImpl8com.example.sharen.data.repository.ProductRepositoryImpl7com.example.sharen.data.repository.ReportRepositoryImpl:com.example.sharen.data.repository.impl.AuthRepositoryImpl2com.example.sharen.ui.admin.UserManagementActivity3com.example.sharen.ui.admin.UserManagementViewModel(com.example.sharen.ui.auth.AuthViewModel1com.example.sharen.ui.auth.ForgotPasswordActivity(com.example.sharen.ui.auth.LoginActivity+com.example.sharen.ui.auth.RegisterActivity)com.example.sharen.ui.auth.SplashActivity.com.example.sharen.ui.customer.CustomerAdapterAcom.example.sharen.ui.customer.CustomerAdapter.CustomerViewHolderCcom.example.sharen.ui.customer.CustomerAdapter.CustomerDiffCallback6com.example.sharen.ui.customer.CustomerDetailsActivity7com.example.sharen.ui.customer.CustomerDetailsViewModel3com.example.sharen.ui.customer.CustomerFormActivity4com.example.sharen.ui.customer.CustomerFormViewModel3com.example.sharen.ui.customer.CustomerListActivity4com.example.sharen.ui.customer.CustomerListViewModel1com.example.sharen.ui.dashboard.DashboardActivity2com.example.sharen.ui.dashboard.DashboardViewModel2com.example.sharen.ui.dashboard.TransactionAdapterHcom.example.sharen.ui.dashboard.TransactionAdapter.TransactionViewHolderJcom.example.sharen.ui.dashboard.TransactionAdapter.TransactionDiffCallback;com.example.sharen.ui.installment.InstallmentDetailFragment9com.example.sharen.ui.installment.InstallmentEditFragment9com.example.sharen.ui.installment.InstallmentListFragment3com.example.sharen.ui.installment.InstallmentStatus6com.example.sharen.ui.installment.InstallmentViewModel<com.example.sharen.ui.installment.adapter.InstallmentAdapterRcom.example.sharen.ui.installment.adapter.InstallmentAdapter.InstallmentViewHolderTcom.example.sharen.ui.installment.adapter.InstallmentAdapter.InstallmentDiffCallback,com.example.sharen.ui.invoice.InvoiceAdapter><EMAIL>,com.example.sharen.ui.invoice.PaymentAdapter><EMAIL>,com.example.sharen.ui.payment.PaymentAdapter><EMAIL>,com.example.sharen.ui.product.ProductAdapter><EMAIL>:com.example.sharen.ui.profile.NotificationSettingsActivity;com.example.sharen.ui.profile.NotificationSettingsViewModel4com.example.sharen.ui.profile.PasswordChangeActivity5com.example.sharen.ui.profile.PasswordChangeViewModel-com.example.sharen.ui.profile.ProfileActivity.com.example.sharen.ui.profile.ProfileViewModel.com.example.sharen.ui.profile.SecurityActivity/com.example.sharen.ui.profile.SecurityViewModel+com.example.sharen.ui.report.ReportActivity,com.example.sharen.ui.report.ReportViewModel/com.example.sharen.ui.settings.SettingsActivity0com.example.sharen.ui.settings.SettingsViewModel1com.example.sharen.databinding.ItemInvoiceBinding1com.example.sharen.databinding.ItemPaymentBinding8com.example.sharen.databinding.ItemInvoiceProductBinding1com.example.sharen.databinding.ItemProductBinding2com.example.sharen.databinding.ItemCustomerBinding5com.example.sharen.databinding.ItemTransactionBinding6com.example.sharen.databinding.ItemNotificationBinding5com.example.sharen.databinding.ItemInstallmentBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      