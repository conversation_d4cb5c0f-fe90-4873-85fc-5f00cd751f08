// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInstallmentDetailBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton buttonDelete;

  @NonNull
  public final MaterialButton buttonEdit;

  @NonNull
  public final MaterialButton buttonPay;

  @NonNull
  public final MaterialButton buttonRemind;

  @NonNull
  public final Chip chipStatus;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView textViewDueDate;

  @NonNull
  public final TextView textViewNotes;

  @NonNull
  public final TextView textViewPaidAmount;

  @NonNull
  public final TextView textViewRemainingAmount;

  @NonNull
  public final TextView textViewTotalAmount;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentInstallmentDetailBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton buttonDelete, @NonNull MaterialButton buttonEdit,
      @NonNull MaterialButton buttonPay, @NonNull MaterialButton buttonRemind,
      @NonNull Chip chipStatus, @NonNull ProgressBar progressBar, @NonNull TextView textViewDueDate,
      @NonNull TextView textViewNotes, @NonNull TextView textViewPaidAmount,
      @NonNull TextView textViewRemainingAmount, @NonNull TextView textViewTotalAmount,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.buttonDelete = buttonDelete;
    this.buttonEdit = buttonEdit;
    this.buttonPay = buttonPay;
    this.buttonRemind = buttonRemind;
    this.chipStatus = chipStatus;
    this.progressBar = progressBar;
    this.textViewDueDate = textViewDueDate;
    this.textViewNotes = textViewNotes;
    this.textViewPaidAmount = textViewPaidAmount;
    this.textViewRemainingAmount = textViewRemainingAmount;
    this.textViewTotalAmount = textViewTotalAmount;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInstallmentDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInstallmentDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_installment_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInstallmentDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDelete;
      MaterialButton buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.buttonEdit;
      MaterialButton buttonEdit = ViewBindings.findChildViewById(rootView, id);
      if (buttonEdit == null) {
        break missingId;
      }

      id = R.id.buttonPay;
      MaterialButton buttonPay = ViewBindings.findChildViewById(rootView, id);
      if (buttonPay == null) {
        break missingId;
      }

      id = R.id.buttonRemind;
      MaterialButton buttonRemind = ViewBindings.findChildViewById(rootView, id);
      if (buttonRemind == null) {
        break missingId;
      }

      id = R.id.chipStatus;
      Chip chipStatus = ViewBindings.findChildViewById(rootView, id);
      if (chipStatus == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.textViewDueDate;
      TextView textViewDueDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewDueDate == null) {
        break missingId;
      }

      id = R.id.textViewNotes;
      TextView textViewNotes = ViewBindings.findChildViewById(rootView, id);
      if (textViewNotes == null) {
        break missingId;
      }

      id = R.id.textViewPaidAmount;
      TextView textViewPaidAmount = ViewBindings.findChildViewById(rootView, id);
      if (textViewPaidAmount == null) {
        break missingId;
      }

      id = R.id.textViewRemainingAmount;
      TextView textViewRemainingAmount = ViewBindings.findChildViewById(rootView, id);
      if (textViewRemainingAmount == null) {
        break missingId;
      }

      id = R.id.textViewTotalAmount;
      TextView textViewTotalAmount = ViewBindings.findChildViewById(rootView, id);
      if (textViewTotalAmount == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentInstallmentDetailBinding((CoordinatorLayout) rootView, buttonDelete,
          buttonEdit, buttonPay, buttonRemind, chipStatus, progressBar, textViewDueDate,
          textViewNotes, textViewPaidAmount, textViewRemainingAmount, textViewTotalAmount, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
