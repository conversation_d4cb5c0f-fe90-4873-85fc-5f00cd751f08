<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_invoice" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\item_invoice.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_invoice_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="118" endOffset="35"/></Target><Target id="@+id/ivDocIcon" view="ImageView"><Expressions/><location startLine="15" startOffset="8" endLine="23" endOffset="55"/></Target><Target id="@+id/tvInvoiceNumber" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="35" endOffset="49"/></Target><Target id="@+id/tvCustomerName" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="48" endOffset="44"/></Target><Target id="@+id/tvDate" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="59" endOffset="37"/></Target><Target id="@+id/tvPaymentType" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="70" endOffset="31"/></Target><Target id="@+id/divider" view="View"><Expressions/><location startLine="72" startOffset="8" endLine="78" endOffset="63"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="80" startOffset="8" endLine="92" endOffset="44"/></Target><Target id="@+id/tvAmount" view="TextView"><Expressions/><location startLine="94" startOffset="8" endLine="103" endOffset="40"/></Target><Target id="@+id/ivPaid" view="ImageView"><Expressions/><location startLine="105" startOffset="8" endLine="115" endOffset="40"/></Target></Targets></Layout>