package com.example.sharen.domain.usecase.product;

/**
 * Use Case برای دریافت محصول با شناسه
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J,\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u00062\u0006\u0010\b\u001a\u00020\tH\u0086B\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\n\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\f"}, d2 = {"Lcom/example/sharen/domain/usecase/product/GetProductByIdUseCase;", "", "productRepository", "Lcom/example/sharen/domain/repository/ProductRepository;", "(Lcom/example/sharen/domain/repository/ProductRepository;)V", "invoke", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/Product;", "productId", "", "invoke-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class GetProductByIdUseCase {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.domain.repository.ProductRepository productRepository = null;
    
    @javax.inject.Inject
    public GetProductByIdUseCase(@org.jetbrains.annotations.NotNull
    com.example.sharen.domain.repository.ProductRepository productRepository) {
        super();
    }
}