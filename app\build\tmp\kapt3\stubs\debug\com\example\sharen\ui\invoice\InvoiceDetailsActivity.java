package com.example.sharen.ui.invoice;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u0000 ,2\u00020\u0001:\u0001,B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0002J\b\u0010\u0018\u001a\u00020\u0017H\u0002J\b\u0010\u0019\u001a\u00020\u0017H\u0002J\u0012\u0010\u001a\u001a\u00020\u00172\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0014J\u0010\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 H\u0016J\u0010\u0010!\u001a\u00020\u001e2\u0006\u0010\"\u001a\u00020#H\u0016J\b\u0010$\u001a\u00020\u0017H\u0002J\b\u0010%\u001a\u00020\u0017H\u0002J\b\u0010&\u001a\u00020\u0017H\u0002J\b\u0010\'\u001a\u00020\u0017H\u0002J\b\u0010(\u001a\u00020\u0017H\u0002J\b\u0010)\u001a\u00020\u0017H\u0002J\u0010\u0010*\u001a\u00020\u00172\u0006\u0010+\u001a\u00020\u0006H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n \r*\u0004\u0018\u00010\f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0010\u001a\u00020\u00118BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006-"}, d2 = {"Lcom/example/sharen/ui/invoice/InvoiceDetailsActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "binding", "Lcom/example/sharen/databinding/ActivityInvoiceDetailsBinding;", "currentInvoice", "Lcom/example/sharen/data/model/Invoice;", "dateFormatter", "Ljava/text/SimpleDateFormat;", "itemsAdapter", "Lcom/example/sharen/ui/invoice/InvoiceItemAdapter;", "numberFormatter", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "paymentsAdapter", "Lcom/example/sharen/ui/invoice/PaymentAdapter;", "viewModel", "Lcom/example/sharen/ui/invoice/InvoiceDetailsViewModel;", "getViewModel", "()Lcom/example/sharen/ui/invoice/InvoiceDetailsViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "approveInvoice", "", "editInvoice", "navigateToAddPayment", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateOptionsMenu", "", "menu", "Landroid/view/Menu;", "onOptionsItemSelected", "item", "Landroid/view/MenuItem;", "printInvoice", "promptDeleteInvoice", "setupAdapters", "setupObservers", "setupUI", "shareInvoice", "updateUI", "invoice", "Companion", "app_debug"})
public final class InvoiceDetailsActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.example.sharen.databinding.ActivityInvoiceDetailsBinding binding;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy viewModel$delegate = null;
    private com.example.sharen.ui.invoice.InvoiceItemAdapter itemsAdapter;
    private com.example.sharen.ui.invoice.PaymentAdapter paymentsAdapter;
    private final java.text.NumberFormat numberFormatter = null;
    @org.jetbrains.annotations.NotNull
    private final java.text.SimpleDateFormat dateFormatter = null;
    @org.jetbrains.annotations.Nullable
    private com.example.sharen.data.model.Invoice currentInvoice;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String EXTRA_INVOICE_ID = "invoice_id";
    @org.jetbrains.annotations.NotNull
    public static final com.example.sharen.ui.invoice.InvoiceDetailsActivity.Companion Companion = null;
    
    public InvoiceDetailsActivity() {
        super();
    }
    
    private final com.example.sharen.ui.invoice.InvoiceDetailsViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupAdapters() {
    }
    
    private final void setupObservers() {
    }
    
    private final void updateUI(com.example.sharen.data.model.Invoice invoice) {
    }
    
    private final void navigateToAddPayment() {
    }
    
    private final void approveInvoice() {
    }
    
    private final void editInvoice() {
    }
    
    private final void printInvoice() {
    }
    
    @java.lang.Override
    public boolean onCreateOptionsMenu(@org.jetbrains.annotations.NotNull
    android.view.Menu menu) {
        return false;
    }
    
    @java.lang.Override
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull
    android.view.MenuItem item) {
        return false;
    }
    
    private final void shareInvoice() {
    }
    
    private final void promptDeleteInvoice() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/sharen/ui/invoice/InvoiceDetailsActivity$Companion;", "", "()V", "EXTRA_INVOICE_ID", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}