package com.example.sharen.data.local.dao

import androidx.room.*
import com.example.sharen.data.local.entity.ProductEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ProductDao {
    @Query("SELECT * FROM products")
    fun getAllProducts(): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE id = :id")
    suspend fun getProductById(id: String): ProductEntity?

    @Query("SELECT * FROM products WHERE categoryId = :categoryId")
    fun getProductsByCategory(categoryId: String): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE brandId = :brandId")
    fun getProductsByBrand(brandId: String): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE name LIKE '%' || :query || '%' OR code LIKE '%' || :query || '%' OR barcode LIKE '%' || :query || '%'")
    fun searchProducts(query: String): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE stock > 0")
    fun getProductsInStock(): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE stock = 0")
    fun getProductsOutOfStock(): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE stock <= minimumStock AND stock > 0")
    fun getLowStockProducts(): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE sellingPrice BETWEEN :minPrice AND :maxPrice")
    fun getProductsByPriceRange(minPrice: Long, maxPrice: Long): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE isActive = 1")
    fun getActiveProducts(): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE isActive = 0")
    fun getInactiveProducts(): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE season = :season")
    fun getProductsBySeason(season: String): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE gender = :gender")
    fun getProductsByGender(gender: String): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products ORDER BY stock DESC LIMIT :limit")
    fun getBestSellingProducts(limit: Int): Flow<List<ProductEntity>>

    @Query("SELECT * FROM products WHERE createdAt > :cutoffDate ORDER BY createdAt DESC")
    fun getNewProducts(cutoffDate: Long): Flow<List<ProductEntity>>

    @Query("UPDATE products SET stock = :newStock, updatedAt = :updatedAt WHERE id = :productId")
    suspend fun updateProductStock(productId: String, newStock: Int, updatedAt: Long = System.currentTimeMillis())

    @Query("DELETE FROM products WHERE id = :productId")
    suspend fun deleteProduct(productId: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: ProductEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<ProductEntity>)

    @Update
    suspend fun updateProduct(product: ProductEntity)

    @Delete
    suspend fun deleteProduct(product: ProductEntity)

    @Query("DELETE FROM products")
    suspend fun deleteAllProducts()
}