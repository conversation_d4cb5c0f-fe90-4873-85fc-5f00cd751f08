package com.example.sharen.domain.usecase.analytics;

import com.example.sharen.domain.repository.CustomerRepository;
import com.example.sharen.domain.repository.InvoiceRepository;
import com.example.sharen.domain.repository.PaymentRepository;
import com.example.sharen.domain.repository.ProductRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetDashboardStatsUseCase_Factory implements Factory<GetDashboardStatsUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<InvoiceRepository> invoiceRepositoryProvider;

  private final Provider<PaymentRepository> paymentRepositoryProvider;

  public GetDashboardStatsUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<PaymentRepository> paymentRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
    this.productRepositoryProvider = productRepositoryProvider;
    this.invoiceRepositoryProvider = invoiceRepositoryProvider;
    this.paymentRepositoryProvider = paymentRepositoryProvider;
  }

  @Override
  public GetDashboardStatsUseCase get() {
    return newInstance(customerRepositoryProvider.get(), productRepositoryProvider.get(), invoiceRepositoryProvider.get(), paymentRepositoryProvider.get());
  }

  public static GetDashboardStatsUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider,
      Provider<ProductRepository> productRepositoryProvider,
      Provider<InvoiceRepository> invoiceRepositoryProvider,
      Provider<PaymentRepository> paymentRepositoryProvider) {
    return new GetDashboardStatsUseCase_Factory(customerRepositoryProvider, productRepositoryProvider, invoiceRepositoryProvider, paymentRepositoryProvider);
  }

  public static GetDashboardStatsUseCase newInstance(CustomerRepository customerRepository,
      ProductRepository productRepository, InvoiceRepository invoiceRepository,
      PaymentRepository paymentRepository) {
    return new GetDashboardStatsUseCase(customerRepository, productRepository, invoiceRepository, paymentRepository);
  }
}
