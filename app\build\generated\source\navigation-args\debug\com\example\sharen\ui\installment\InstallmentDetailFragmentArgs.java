package com.example.sharen.ui.installment;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.lifecycle.SavedStateHandle;
import androidx.navigation.NavArgs;
import java.lang.IllegalArgumentException;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.HashMap;

public class InstallmentDetailFragmentArgs implements NavArgs {
  private final HashMap arguments = new HashMap();

  private InstallmentDetailFragmentArgs() {
  }

  @SuppressWarnings("unchecked")
  private InstallmentDetailFragmentArgs(HashMap argumentsMap) {
    this.arguments.putAll(argumentsMap);
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static InstallmentDetailFragmentArgs fromBundle(@NonNull Bundle bundle) {
    InstallmentDetailFragmentArgs __result = new InstallmentDetailFragmentArgs();
    bundle.setClassLoader(InstallmentDetailFragmentArgs.class.getClassLoader());
    if (bundle.containsKey("installmentId")) {
      long installmentId;
      installmentId = bundle.getLong("installmentId");
      __result.arguments.put("installmentId", installmentId);
    } else {
      throw new IllegalArgumentException("Required argument \"installmentId\" is missing and does not have an android:defaultValue");
    }
    return __result;
  }

  @NonNull
  @SuppressWarnings("unchecked")
  public static InstallmentDetailFragmentArgs fromSavedStateHandle(
      @NonNull SavedStateHandle savedStateHandle) {
    InstallmentDetailFragmentArgs __result = new InstallmentDetailFragmentArgs();
    if (savedStateHandle.contains("installmentId")) {
      long installmentId;
      installmentId = savedStateHandle.get("installmentId");
      __result.arguments.put("installmentId", installmentId);
    } else {
      throw new IllegalArgumentException("Required argument \"installmentId\" is missing and does not have an android:defaultValue");
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  public long getInstallmentId() {
    return (long) arguments.get("installmentId");
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public Bundle toBundle() {
    Bundle __result = new Bundle();
    if (arguments.containsKey("installmentId")) {
      long installmentId = (long) arguments.get("installmentId");
      __result.putLong("installmentId", installmentId);
    }
    return __result;
  }

  @SuppressWarnings("unchecked")
  @NonNull
  public SavedStateHandle toSavedStateHandle() {
    SavedStateHandle __result = new SavedStateHandle();
    if (arguments.containsKey("installmentId")) {
      long installmentId = (long) arguments.get("installmentId");
      __result.set("installmentId", installmentId);
    }
    return __result;
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
        return true;
    }
    if (object == null || getClass() != object.getClass()) {
        return false;
    }
    InstallmentDetailFragmentArgs that = (InstallmentDetailFragmentArgs) object;
    if (arguments.containsKey("installmentId") != that.arguments.containsKey("installmentId")) {
      return false;
    }
    if (getInstallmentId() != that.getInstallmentId()) {
      return false;
    }
    return true;
  }

  @Override
  public int hashCode() {
    int result = 1;
    result = 31 * result + (int)(getInstallmentId() ^ (getInstallmentId() >>> 32));
    return result;
  }

  @Override
  public String toString() {
    return "InstallmentDetailFragmentArgs{"
        + "installmentId=" + getInstallmentId()
        + "}";
  }

  public static final class Builder {
    private final HashMap arguments = new HashMap();

    @SuppressWarnings("unchecked")
    public Builder(@NonNull InstallmentDetailFragmentArgs original) {
      this.arguments.putAll(original.arguments);
    }

    @SuppressWarnings("unchecked")
    public Builder(long installmentId) {
      this.arguments.put("installmentId", installmentId);
    }

    @NonNull
    public InstallmentDetailFragmentArgs build() {
      InstallmentDetailFragmentArgs result = new InstallmentDetailFragmentArgs(arguments);
      return result;
    }

    @NonNull
    @SuppressWarnings("unchecked")
    public Builder setInstallmentId(long installmentId) {
      this.arguments.put("installmentId", installmentId);
      return this;
    }

    @SuppressWarnings({"unchecked","GetterOnBuilder"})
    public long getInstallmentId() {
      return (long) arguments.get("installmentId");
    }
  }
}
