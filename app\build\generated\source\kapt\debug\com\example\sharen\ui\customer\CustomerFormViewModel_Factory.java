package com.example.sharen.ui.customer;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerFormViewModel_Factory implements Factory<CustomerFormViewModel> {
  private final Provider<NonExistentClass> customerRepositoryProvider;

  public CustomerFormViewModel_Factory(Provider<NonExistentClass> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public CustomerFormViewModel get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static CustomerFormViewModel_Factory create(
      Provider<NonExistentClass> customerRepositoryProvider) {
    return new CustomerFormViewModel_Factory(customerRepositoryProvider);
  }

  public static CustomerFormViewModel newInstance(NonExistentClass customerRepository) {
    return new CustomerFormViewModel(customerRepository);
  }
}
