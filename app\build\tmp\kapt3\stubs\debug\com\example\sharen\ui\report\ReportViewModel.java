package com.example.sharen.ui.report;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u0002\n\u0002\b\u0012\n\u0002\u0010\b\n\u0002\b\u0017\n\u0002\u0010\t\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\u0006\u0010+\u001a\u00020,J\u000e\u0010-\u001a\u00020,2\u0006\u0010.\u001a\u00020\u000fJ\u0006\u0010/\u001a\u00020,J\u000e\u00100\u001a\u00020,2\u0006\u0010.\u001a\u00020\u000fJ\u000e\u00101\u001a\u00020,2\u0006\u0010.\u001a\u00020\u000fJ\u000e\u00102\u001a\u00020,2\u0006\u0010.\u001a\u00020\u000fJ\u0006\u00103\u001a\u00020,J\u000e\u00104\u001a\u00020,2\u0006\u00105\u001a\u00020\rJ\u000e\u00106\u001a\u00020,2\u0006\u00105\u001a\u00020\rJ\u0006\u00107\u001a\u00020,J\u0016\u00108\u001a\u00020,2\u0006\u00109\u001a\u00020\r2\u0006\u0010:\u001a\u00020\rJ\u0006\u0010;\u001a\u00020,J\u0006\u0010<\u001a\u00020,J\u0016\u0010=\u001a\u00020,2\u0006\u0010>\u001a\u00020?2\u0006\u0010@\u001a\u00020?J\u0016\u0010A\u001a\u00020,2\u0006\u0010>\u001a\u00020?2\u0006\u0010@\u001a\u00020?J\u0016\u0010B\u001a\u00020,2\u0006\u00109\u001a\u00020\r2\u0006\u0010:\u001a\u00020\rJ\u0016\u0010C\u001a\u00020,2\u0006\u00109\u001a\u00020\r2\u0006\u0010:\u001a\u00020\rJ\u0016\u0010D\u001a\u00020,2\u0006\u00109\u001a\u00020\r2\u0006\u0010:\u001a\u00020\rJ\u0016\u0010E\u001a\u00020,2\u0006\u00109\u001a\u00020\r2\u0006\u0010:\u001a\u00020\rJ\u000e\u0010F\u001a\u00020,2\u0006\u0010>\u001a\u00020?J\u000e\u0010G\u001a\u00020,2\u0006\u0010>\u001a\u00020?J\u0006\u0010H\u001a\u00020,J\u0016\u0010I\u001a\u00020,2\u0006\u00109\u001a\u00020\r2\u0006\u0010:\u001a\u00020\rJ\u000e\u0010J\u001a\u00020,2\u0006\u0010K\u001a\u00020\u0016J\u0006\u0010L\u001a\u00020,J\b\u0010M\u001a\u00020,H\u0002J\u0006\u0010N\u001a\u00020,J\u0006\u0010O\u001a\u00020,J\u0006\u0010P\u001a\u00020,J\u0006\u0010Q\u001a\u00020,J\u000e\u0010R\u001a\u00020,2\u0006\u0010S\u001a\u00020\u0019J\u000e\u0010T\u001a\u00020,2\u0006\u0010U\u001a\u00020\u0019J\u000e\u0010V\u001a\u00020,2\u0006\u00109\u001a\u00020WJ\u000e\u0010X\u001a\u00020,2\u0006\u0010K\u001a\u00020\u0016R \u0010\n\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u001aR#\u0010\u001b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f0\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000f0\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001eR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00120\"\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0010\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u001aR\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00140\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001eR\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00160\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001eR\u001d\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190\u00180\"\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010$\u00a8\u0006Y"}, d2 = {"Lcom/example/sharen/ui/report/ReportViewModel;", "Landroidx/lifecycle/ViewModel;", "invoiceRepository", "Lcom/example/sharen/data/repository/InvoiceRepository;", "productRepository", "error/NonExistentClass", "customerRepository", "reportRepository", "Lcom/example/sharen/data/repository/ReportRepository;", "(Lcom/example/sharen/data/repository/InvoiceRepository;Lerror/NonExistentClass;Lerror/NonExistentClass;Lcom/example/sharen/data/repository/ReportRepository;)V", "_dateRange", "Landroidx/lifecycle/MutableLiveData;", "Lkotlin/Pair;", "Ljava/util/Date;", "_error", "", "_loading", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_reportData", "Lcom/example/sharen/data/model/ReportData;", "_reportType", "Lcom/example/sharen/data/model/ReportType;", "_reports", "", "Lcom/example/sharen/data/model/Report;", "Lerror/NonExistentClass;", "dateRange", "Landroidx/lifecycle/LiveData;", "getDateRange", "()Landroidx/lifecycle/LiveData;", "error", "getError", "loading", "Lkotlinx/coroutines/flow/StateFlow;", "getLoading", "()Lkotlinx/coroutines/flow/StateFlow;", "reportData", "getReportData", "reportType", "getReportType", "reports", "getReports", "clearError", "", "deleteReport", "reportId", "exportReport", "exportReportToCsv", "exportReportToExcel", "exportReportToPdf", "generateCurrentInventoryReport", "generateDailyFinancialReport", "date", "generateDailySalesReport", "generateDebtReport", "generateInventoryMovementReport", "startDate", "endDate", "generateInventoryValueReport", "generateLowStockReport", "generateMonthlyFinancialReport", "year", "", "month", "generateMonthlySalesReport", "generateProfitLossReport", "generateSalesByCategoryReport", "generateSalesByCustomerReport", "generateSalesByProductReport", "generateYearlyFinancialReport", "generateYearlySalesReport", "getAllReportTemplates", "getReportsByDateRange", "getReportsByType", "type", "loadCurrentMonthData", "loadReportData", "loadReports", "loadThisWeekData", "loadThisYearData", "loadTodayData", "saveReport", "report", "saveReportTemplate", "template", "setCustomDateRange", "", "setReportType", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class ReportViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.repository.InvoiceRepository invoiceRepository = null;
    @org.jetbrains.annotations.NotNull
    private final error.NonExistentClass productRepository = null;
    @org.jetbrains.annotations.NotNull
    private final error.NonExistentClass customerRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.repository.ReportRepository reportRepository = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<com.example.sharen.data.model.ReportType> _reportType = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<com.example.sharen.data.model.ReportType> reportType = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<kotlin.Pair<java.util.Date, java.util.Date>> _dateRange = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<kotlin.Pair<java.util.Date, java.util.Date>> dateRange = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<com.example.sharen.data.model.ReportData> _reportData = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<com.example.sharen.data.model.ReportData> reportData = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.sharen.data.model.Report>> _reports = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.data.model.Report>> reports = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _loading = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> loading = null;
    
    @javax.inject.Inject
    public ReportViewModel(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.repository.InvoiceRepository invoiceRepository, @org.jetbrains.annotations.NotNull
    error.NonExistentClass productRepository, @org.jetbrains.annotations.NotNull
    error.NonExistentClass customerRepository, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.repository.ReportRepository reportRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<com.example.sharen.data.model.ReportType> getReportType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<kotlin.Pair<java.util.Date, java.util.Date>> getDateRange() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<com.example.sharen.data.model.ReportData> getReportData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.sharen.data.model.Report>> getReports() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> getLoading() {
        return null;
    }
    
    public final void setReportType(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.ReportType type) {
    }
    
    public final void loadTodayData() {
    }
    
    public final void loadThisWeekData() {
    }
    
    public final void loadCurrentMonthData() {
    }
    
    public final void loadThisYearData() {
    }
    
    public final void setCustomDateRange(long startDate) {
    }
    
    private final void loadReportData() {
    }
    
    public final void loadReports() {
    }
    
    public final void generateDailyFinancialReport(@org.jetbrains.annotations.NotNull
    java.util.Date date) {
    }
    
    public final void generateMonthlyFinancialReport(int year, int month) {
    }
    
    public final void generateYearlyFinancialReport(int year) {
    }
    
    public final void generateProfitLossReport(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void generateDebtReport() {
    }
    
    public final void generateDailySalesReport(@org.jetbrains.annotations.NotNull
    java.util.Date date) {
    }
    
    public final void generateMonthlySalesReport(int year, int month) {
    }
    
    public final void generateYearlySalesReport(int year) {
    }
    
    public final void generateSalesByProductReport(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void generateSalesByCustomerReport(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void generateSalesByCategoryReport(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void generateCurrentInventoryReport() {
    }
    
    public final void generateLowStockReport() {
    }
    
    public final void generateInventoryMovementReport(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void generateInventoryValueReport() {
    }
    
    public final void saveReport(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Report report) {
    }
    
    public final void deleteReport(@org.jetbrains.annotations.NotNull
    java.lang.String reportId) {
    }
    
    public final void getReportsByType(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.ReportType type) {
    }
    
    public final void getReportsByDateRange(@org.jetbrains.annotations.NotNull
    java.util.Date startDate, @org.jetbrains.annotations.NotNull
    java.util.Date endDate) {
    }
    
    public final void exportReportToPdf(@org.jetbrains.annotations.NotNull
    java.lang.String reportId) {
    }
    
    public final void exportReportToExcel(@org.jetbrains.annotations.NotNull
    java.lang.String reportId) {
    }
    
    public final void exportReportToCsv(@org.jetbrains.annotations.NotNull
    java.lang.String reportId) {
    }
    
    public final void saveReportTemplate(@org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.Report template) {
    }
    
    public final void getAllReportTemplates() {
    }
    
    public final void clearError() {
    }
    
    public final void exportReport() {
    }
}