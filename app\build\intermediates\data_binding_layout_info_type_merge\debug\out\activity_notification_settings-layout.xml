<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_notification_settings" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_notification_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_notification_settings_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="73" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="15" endOffset="74"/></Target><Target id="@+id/switchNotifications" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="42" startOffset="20" endLine="47" endOffset="80"/></Target><Target id="@+id/switchSound" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="49" startOffset="20" endLine="55" endOffset="80"/></Target><Target id="@+id/switchVibration" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="57" startOffset="20" endLine="63" endOffset="80"/></Target></Targets></Layout>