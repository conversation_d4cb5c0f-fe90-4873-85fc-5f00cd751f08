package com.example.sharen.data.model;

/**
 * مدل داده فاکتور فروش
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\bA\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u00ed\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u0012\b\b\u0002\u0010\f\u001a\u00020\n\u0012\b\b\u0002\u0010\r\u001a\u00020\n\u0012\b\b\u0002\u0010\u000e\u001a\u00020\n\u0012\b\b\u0002\u0010\u000f\u001a\u00020\n\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u0012\u000e\b\u0002\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001a\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0015\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0015\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010 J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\t\u0010D\u001a\u00020\nH\u00c6\u0003J\t\u0010E\u001a\u00020\nH\u00c6\u0003J\t\u0010F\u001a\u00020\nH\u00c6\u0003J\t\u0010G\u001a\u00020\u0011H\u00c6\u0003J\t\u0010H\u001a\u00020\u0013H\u00c6\u0003J\u000b\u0010I\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003J\u000b\u0010J\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010K\u001a\u00020\u0018H\u00c6\u0003J\u000f\u0010L\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001aH\u00c6\u0003J\t\u0010M\u001a\u00020\u0015H\u00c6\u0003J\t\u0010N\u001a\u00020\u0003H\u00c6\u0003J\t\u0010O\u001a\u00020\u0015H\u00c6\u0003J\u000b\u0010P\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003J\u000b\u0010Q\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010R\u001a\u00020\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010T\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010U\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010V\u001a\u00020\nH\u00c6\u0003J\t\u0010W\u001a\u00020\nH\u00c6\u0003J\t\u0010X\u001a\u00020\nH\u00c6\u0003J\u00f7\u0001\u0010Y\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\n2\b\b\u0002\u0010\u000e\u001a\u00020\n2\b\b\u0002\u0010\u000f\u001a\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0017\u001a\u00020\u00182\u000e\b\u0002\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001a2\b\b\u0002\u0010\u001c\u001a\u00020\u00152\b\b\u0002\u0010\u001d\u001a\u00020\u00152\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00152\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010Z\u001a\u00020\u00182\b\u0010[\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\\\u001a\u00020]H\u00d6\u0001J\t\u0010^\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u001e\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0013\u0010\u001f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0011\u0010%\u001a\u00020\n8F\u00a2\u0006\u0006\u001a\u0004\b&\u0010\'R\u0011\u0010(\u001a\u00020\n8F\u00a2\u0006\u0006\u001a\u0004\b)\u0010\'R\u0011\u0010\u001c\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\"R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010$R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010$R\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\'R\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\"R\u0011\u0010\r\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\'R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010$R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010$R\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u00102R\u0011\u00103\u001a\u00020\u00188F\u00a2\u0006\u0006\u001a\u0004\b3\u00102R\u0011\u00104\u001a\u00020\u00188F\u00a2\u0006\u0006\u001a\u0004\b4\u00102R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001b0\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00106R\u0013\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010$R\u0011\u0010\u000e\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010\'R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0011\u0010\u000f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010\'R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010$R\u0013\u0010\b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010$R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010?R\u0011\u0010\f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010\'R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010\'R\u0011\u0010\u001d\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010\"\u00a8\u0006_"}, d2 = {"Lcom/example/sharen/data/model/Invoice;", "", "id", "", "invoiceNumber", "customerId", "customerName", "sellerId", "sellerName", "totalAmount", "", "discount", "tax", "finalAmount", "paidAmount", "remainingAmount", "status", "Lcom/example/sharen/data/model/InvoiceStatus;", "paymentType", "Lcom/example/sharen/data/model/PaymentType;", "dueDate", "Ljava/util/Date;", "notes", "isDeleted", "", "items", "", "Lcom/example/sharen/data/model/InvoiceItem;", "createdAt", "updatedAt", "approvedAt", "approvedBy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JJJJJJLcom/example/sharen/data/model/InvoiceStatus;Lcom/example/sharen/data/model/PaymentType;Ljava/util/Date;Ljava/lang/String;ZLjava/util/List;Ljava/util/Date;Ljava/util/Date;Ljava/util/Date;Ljava/lang/String;)V", "getApprovedAt", "()Ljava/util/Date;", "getApprovedBy", "()Ljava/lang/String;", "calculatedFinalAmount", "getCalculatedFinalAmount", "()J", "calculatedRemainingAmount", "getCalculatedRemainingAmount", "getCreatedAt", "getCustomerId", "getCustomerName", "getDiscount", "getDueDate", "getFinalAmount", "getId", "getInvoiceNumber", "()Z", "isFullyPaid", "isInstallment", "getItems", "()Ljava/util/List;", "getNotes", "getPaidAmount", "getPaymentType", "()Lcom/example/sharen/data/model/PaymentType;", "getRemainingAmount", "getSellerId", "getSellerName", "getStatus", "()Lcom/example/sharen/data/model/InvoiceStatus;", "getTax", "getTotalAmount", "getUpdatedAt", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class Invoice {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String invoiceNumber = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String customerId = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String customerName = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sellerId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sellerName = null;
    private final long totalAmount = 0L;
    private final long discount = 0L;
    private final long tax = 0L;
    private final long finalAmount = 0L;
    private final long paidAmount = 0L;
    private final long remainingAmount = 0L;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.model.InvoiceStatus status = null;
    @org.jetbrains.annotations.NotNull
    private final com.example.sharen.data.model.PaymentType paymentType = null;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date dueDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    private final boolean isDeleted = false;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.example.sharen.data.model.InvoiceItem> items = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Date createdAt = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.Date updatedAt = null;
    @org.jetbrains.annotations.Nullable
    private final java.util.Date approvedAt = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String approvedBy = null;
    
    public Invoice(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName, @org.jetbrains.annotations.Nullable
    java.lang.String sellerId, @org.jetbrains.annotations.Nullable
    java.lang.String sellerName, long totalAmount, long discount, long tax, long finalAmount, long paidAmount, long remainingAmount, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.InvoiceStatus status, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentType paymentType, @org.jetbrains.annotations.Nullable
    java.util.Date dueDate, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean isDeleted, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.data.model.InvoiceItem> items, @org.jetbrains.annotations.NotNull
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull
    java.util.Date updatedAt, @org.jetbrains.annotations.Nullable
    java.util.Date approvedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getInvoiceNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCustomerId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCustomerName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSellerId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSellerName() {
        return null;
    }
    
    public final long getTotalAmount() {
        return 0L;
    }
    
    public final long getDiscount() {
        return 0L;
    }
    
    public final long getTax() {
        return 0L;
    }
    
    public final long getFinalAmount() {
        return 0L;
    }
    
    public final long getPaidAmount() {
        return 0L;
    }
    
    public final long getRemainingAmount() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.model.InvoiceStatus getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.model.PaymentType getPaymentType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getDueDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.data.model.InvoiceItem> getItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date getCreatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date getUpdatedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date getApprovedAt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getApprovedBy() {
        return null;
    }
    
    public final long getCalculatedFinalAmount() {
        return 0L;
    }
    
    public final long getCalculatedRemainingAmount() {
        return 0L;
    }
    
    public final boolean isFullyPaid() {
        return false;
    }
    
    public final boolean isInstallment() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    public final long component10() {
        return 0L;
    }
    
    public final long component11() {
        return 0L;
    }
    
    public final long component12() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.model.InvoiceStatus component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.model.PaymentType component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    public final boolean component17() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.example.sharen.data.model.InvoiceItem> component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.Date component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.util.Date component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component6() {
        return null;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.example.sharen.data.model.Invoice copy(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    java.lang.String invoiceNumber, @org.jetbrains.annotations.NotNull
    java.lang.String customerId, @org.jetbrains.annotations.NotNull
    java.lang.String customerName, @org.jetbrains.annotations.Nullable
    java.lang.String sellerId, @org.jetbrains.annotations.Nullable
    java.lang.String sellerName, long totalAmount, long discount, long tax, long finalAmount, long paidAmount, long remainingAmount, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.InvoiceStatus status, @org.jetbrains.annotations.NotNull
    com.example.sharen.data.model.PaymentType paymentType, @org.jetbrains.annotations.Nullable
    java.util.Date dueDate, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean isDeleted, @org.jetbrains.annotations.NotNull
    java.util.List<com.example.sharen.data.model.InvoiceItem> items, @org.jetbrains.annotations.NotNull
    java.util.Date createdAt, @org.jetbrains.annotations.NotNull
    java.util.Date updatedAt, @org.jetbrains.annotations.Nullable
    java.util.Date approvedAt, @org.jetbrains.annotations.Nullable
    java.lang.String approvedBy) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}