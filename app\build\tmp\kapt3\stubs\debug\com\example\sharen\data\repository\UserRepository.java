package com.example.sharen.data.repository;

/**
 * Repository Interface برای مدیریت کاربران
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\n\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0006\u0010\u0007J*\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\f\u0010\rJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000fH&J\u0010\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u000fH&J\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u000bH\u00a6@\u00f8\u0001\u0002\u00a2\u0006\u0002\u0010\u0013J,\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0015\u0010\rJ\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00100\u000f2\u0006\u0010\u0017\u001a\u00020\u000bH&J*\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0002\u00a2\u0006\u0004\b\u0019\u0010\u0007\u0082\u0002\u000f\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/example/sharen/data/repository/UserRepository;", "", "addUser", "Lkotlin/Result;", "Lcom/example/sharen/domain/model/User;", "user", "addUser-gIAlu-s", "(Lcom/example/sharen/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteUser", "", "userId", "", "deleteUser-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllUsers", "Lkotlinx/coroutines/flow/Flow;", "", "getCurrentUser", "getCurrentUserId", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserById", "getUserById-gIAlu-s", "searchUsers", "query", "updateUser", "updateUser-gIAlu-s", "app_debug"})
public abstract interface UserRepository {
    
    /**
     * دریافت تمام کاربران
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.User>> getAllUsers();
    
    /**
     * دریافت کاربر فعلی
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.example.sharen.domain.model.User> getCurrentUser();
    
    /**
     * دریافت شناسه کاربر فعلی
     */
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCurrentUserId(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion);
    
    /**
     * جستجوی کاربران
     */
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.sharen.domain.model.User>> searchUsers(@org.jetbrains.annotations.NotNull
    java.lang.String query);
}