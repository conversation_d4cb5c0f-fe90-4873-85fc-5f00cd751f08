package com.example.sharen.data.repository

import com.example.sharen.data.model.Result
import com.example.sharen.data.model.User
import kotlinx.coroutines.flow.Flow

interface AuthRepository {
    suspend fun login(email: String, password: String): Result<User>
    suspend fun register(name: String, email: String, password: String, phone: String): Result<User>
    suspend fun logout()
    fun getCurrentUser(): Flow<User?>
    suspend fun updateUser(user: User): Result<User>
    suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit>
} 