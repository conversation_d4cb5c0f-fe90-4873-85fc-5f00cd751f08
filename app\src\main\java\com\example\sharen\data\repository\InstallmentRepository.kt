package com.example.sharen.data.repository

import com.example.sharen.data.api.InstallmentApi
import com.example.sharen.data.db.InstallmentDao
import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
open class InstallmentRepository @Inject constructor(
    private val api: InstallmentApi,
    private val dao: InstallmentDao
) {

    fun getInstallments(): Flow<List<Installment>> = flow {
        try {
            // First emit cached data
            emit(dao.getAllInstallments())
            
            // Then fetch from network
            val networkInstallments = api.getInstallments()
            
            // Update cache
            dao.insertAll(networkInstallments)
            
            // Emit updated data
            emit(networkInstallments)
        } catch (e: Exception) {
            // If network fails, emit cached data
            emit(dao.getAllInstallments())
            throw e
        }
    }.flowOn(Dispatchers.IO)

    fun getInstallment(id: Long): Flow<Installment> = flow {
        try {
            // First emit cached data
            dao.getInstallmentById(id)?.let { emit(it) }
            
            // Then fetch from network
            val networkInstallment = api.getInstallment(id)
            
            // Update cache
            dao.insert(networkInstallment)
            
            // Emit updated data
            emit(networkInstallment)
        } catch (e: Exception) {
            // If network fails, emit cached data
            dao.getInstallmentById(id)?.let { emit(it) }
            throw e
        }
    }.flowOn(Dispatchers.IO)

    suspend fun createInstallment(installment: Installment): Installment {
        val createdInstallment = api.createInstallment(installment)
        dao.insert(createdInstallment)
        return createdInstallment
    }

    suspend fun updateInstallment(installment: Installment): Installment {
        val updatedInstallment = api.updateInstallment(installment.id, installment)
        dao.update(updatedInstallment)
        return updatedInstallment
    }

    suspend fun deleteInstallment(id: Long) {
        api.deleteInstallment(id)
        dao.deleteById(id)
    }

    suspend fun payInstallment(id: Long, amount: Double): Installment {
        val updatedInstallment = api.payInstallment(id, amount)
        dao.update(updatedInstallment)
        return updatedInstallment
    }

    suspend fun sendReminder(id: Long) {
        api.sendReminder(id)
    }

    fun getInstallmentsByCustomer(customerId: Long): Flow<List<Installment>> = flow {
        emit(dao.getByCustomerId(customerId))
    }

    fun getInstallmentsByDateRange(startDate: Date, endDate: Date): Flow<List<Installment>> = flow {
        emit(dao.getByDateRange(startDate, endDate))
    }

    fun getInstallmentsByStatus(status: InstallmentStatus): Flow<List<Installment>> = flow {
        emit(dao.getByStatus(status))
    }

    fun getUpcomingInstallments(customerId: Long): Flow<List<Installment>> = flow {
        emit(dao.getUpcomingByCustomerId(customerId))
    }

    fun getOverdueInstallments(): Flow<List<Installment>> = flow {
        emit(dao.getOverdue())
    }

    suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Map<String, Double> {
        return api.getInstallmentStatistics(startDate, endDate)
    }

    suspend fun calculateRemainingAmount(id: Long): Double {
        return api.calculateRemainingAmount(id)
    }

    fun getCustomerInstallmentHistory(customerId: Long): Flow<List<Installment>> = flow {
        emit(dao.getHistoryByCustomerId(customerId))
    }

    suspend fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        intervalInDays: Int
    ): List<Installment> {
        return api.generateInstallmentSchedule(
            totalAmount,
            numberOfInstallments,
            startDate,
            intervalInDays
        )
    }
} 