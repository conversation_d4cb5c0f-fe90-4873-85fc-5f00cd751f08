package com.example.sharen.data.repository

import com.example.sharen.data.api.InstallmentApi
import com.example.sharen.data.local.dao.InstallmentDao
import com.example.sharen.data.local.entity.InstallmentEntity
import com.example.sharen.data.model.Installment
import com.example.sharen.data.model.InstallmentStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
open class InstallmentRepository @Inject constructor(
    private val api: InstallmentApi,
    private val dao: InstallmentDao
) {

    fun getInstallments(): Flow<List<Installment>> = flow {
        try {
            // First emit cached data
            dao.getAll().collect { entities ->
                emit(entities.map { it.toInstallment() })
            }

            // Then fetch from network
            val networkInstallments = api.getInstallments()

            // Update cache
            dao.insertAll(networkInstallments.map { InstallmentEntity.fromInstallment(it) })

            // Emit updated data
            emit(networkInstallments)
        } catch (e: Exception) {
            // If network fails, emit cached data
            dao.getAll().collect { entities ->
                emit(entities.map { it.toInstallment() })
            }
            throw e
        }
    }.flowOn(Dispatchers.IO)

    fun getInstallment(id: Long): Flow<Installment?> = flow {
        try {
            // First emit cached data
            dao.getById(id)?.let { emit(it.toInstallment()) }

            // Then fetch from network
            val networkInstallment = api.getInstallment(id)

            // Update cache
            dao.insert(InstallmentEntity.fromInstallment(networkInstallment))

            // Emit updated data
            emit(networkInstallment)
        } catch (e: Exception) {
            // If network fails, emit cached data
            dao.getById(id)?.let { emit(it.toInstallment()) }
            throw e
        }
    }.flowOn(Dispatchers.IO)

    suspend fun createInstallment(installment: Installment): Installment {
        val createdInstallment = api.createInstallment(installment)
        dao.insert(InstallmentEntity.fromInstallment(createdInstallment))
        return createdInstallment
    }

    suspend fun updateInstallment(installment: Installment): Installment {
        val updatedInstallment = api.updateInstallment(installment.id, installment)
        dao.update(InstallmentEntity.fromInstallment(updatedInstallment))
        return updatedInstallment
    }

    suspend fun deleteInstallment(id: Long) {
        api.deleteInstallment(id)
        dao.deleteById(id)
    }

    suspend fun payInstallment(id: Long, amount: Double): Installment {
        val updatedInstallment = api.payInstallment(id, amount)
        dao.update(InstallmentEntity.fromInstallment(updatedInstallment))
        return updatedInstallment
    }

    suspend fun sendReminder(id: Long) {
        api.sendReminder(id)
    }

    fun getInstallmentsByCustomer(customerId: Long): Flow<List<Installment>> =
        dao.getByCustomerId(customerId).map { entities ->
            entities.map { it.toInstallment() }
        }

    fun getInstallmentsByDateRange(startDate: Date, endDate: Date): Flow<List<Installment>> =
        dao.getByDateRange(startDate, endDate).map { entities ->
            entities.map { it.toInstallment() }
        }

    fun getInstallmentsByStatus(status: InstallmentStatus): Flow<List<Installment>> =
        dao.getByStatus(status).map { entities ->
            entities.map { it.toInstallment() }
        }

    fun getUpcomingInstallments(customerId: Long): Flow<List<Installment>> =
        dao.getUpcomingByCustomerId(customerId).map { entities ->
            entities.map { it.toInstallment() }
        }

    fun getOverdueInstallments(): Flow<List<Installment>> =
        dao.getOverdue().map { entities ->
            entities.map { it.toInstallment() }
        }

    suspend fun getInstallmentStatistics(startDate: Date, endDate: Date): Map<String, Double> {
        return api.getInstallmentStatistics(startDate, endDate)
    }

    suspend fun calculateRemainingAmount(id: Long): Double {
        return api.calculateRemainingAmount(id)
    }

    fun getCustomerInstallmentHistory(customerId: Long): Flow<List<Installment>> =
        dao.getHistoryByCustomerId(customerId).map { entities ->
            entities.map { it.toInstallment() }
        }

    suspend fun generateInstallmentSchedule(
        totalAmount: Double,
        numberOfInstallments: Int,
        startDate: Date,
        intervalInDays: Int
    ): List<Installment> {
        return api.generateInstallmentSchedule(
            totalAmount,
            numberOfInstallments,
            startDate,
            intervalInDays
        )
    }
}