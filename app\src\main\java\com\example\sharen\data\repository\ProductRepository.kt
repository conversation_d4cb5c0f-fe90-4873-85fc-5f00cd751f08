package com.example.sharen.data.repository

import com.example.sharen.data.model.Product
import kotlinx.coroutines.flow.Flow

/**
 * رپوزیتوری مدیریت محصولات
 */
interface ProductRepository {
    /**
     * دریافت لیست همه محصولات
     */
    fun getAllProducts(): Flow<List<Product>>
    
    /**
     * دریافت محصول با شناسه مشخص
     */
    fun getProductById(productId: String): Flow<Product>
    
    /**
     * جستجوی محصولات با کلمه کلیدی
     */
    fun searchProducts(query: String): Flow<List<Product>>
    
    /**
     * ایجاد محصول جدید
     */
    suspend fun createProduct(product: Product): Result<Product>
    
    /**
     * بروزرسانی اطلاعات محصول
     */
    suspend fun updateProduct(product: Product): Result<Product>
    
    /**
     * حذف محصول
     */
    suspend fun deleteProduct(productId: String): Result<Unit>
    
    /**
     * بروزرسانی موجودی محصول
     */
    suspend fun updateProductStock(productId: String, newStock: Int): Result<Product>
    
    /**
     * دریافت تعداد کل محصولات
     */
    fun getProductCount(): Flow<Int>
    
    /**
     * دریافت لیست محصولات با موجودی کم
     */
    fun getLowStockProducts(): Flow<List<Product>>
    
    /**
     * دریافت محصولات یک دسته‌بندی خاص
     */
    fun getProductsByCategory(category: String): Flow<List<Product>>
} 