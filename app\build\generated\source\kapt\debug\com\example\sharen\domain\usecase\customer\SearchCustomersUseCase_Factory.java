package com.example.sharen.domain.usecase.customer;

import com.example.sharen.domain.repository.CustomerRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SearchCustomersUseCase_Factory implements Factory<SearchCustomersUseCase> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public SearchCustomersUseCase_Factory(Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  @Override
  public SearchCustomersUseCase get() {
    return newInstance(customerRepositoryProvider.get());
  }

  public static SearchCustomersUseCase_Factory create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new SearchCustomersUseCase_Factory(customerRepositoryProvider);
  }

  public static SearchCustomersUseCase newInstance(CustomerRepository customerRepository) {
    return new SearchCustomersUseCase(customerRepository);
  }
}
