{"formatVersion": 1, "database": {"version": 2, "identityHash": "8c0824ee4f3db7afc304e633fea37f7c", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `email` TEXT NOT NULL, `phone` TEXT NOT NULL, `role` TEXT NOT NULL, `isApproved` INTEGER NOT NULL, `is_current` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `imageUrl` TEXT, `referrerId` TEXT, `referrerCode` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "role", "columnName": "role", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isApproved", "columnName": "isApproved", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isCurrent", "columnName": "is_current", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "referrerId", "columnName": "referrerId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "referrerCode", "columnName": "referrerCode", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "customers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `name` TEXT NOT NULL, `phone` TEXT NOT NULL, `address` TEXT, `notes` TEXT, `creditLimit` INTEGER NOT NULL, `totalPurchases` INTEGER NOT NULL, `totalPayments` INTEGER NOT NULL, `totalDebt` INTEGER NOT NULL, `lastPurchaseDate` INTEGER, `lastPaymentDate` INTEGER, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`userId`) REFERENCES `users`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "creditLimit", "columnName": "creditLimit", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPurchases", "columnName": "totalPurchases", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalPayments", "columnName": "totalPayments", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalDebt", "columnName": "totalDebt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastPurchaseDate", "columnName": "lastPurchaseDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastPaymentDate", "columnName": "lastPaymentDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_customers_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_customers_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": [{"table": "users", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["userId"], "referencedColumns": ["id"]}]}, {"tableName": "sellers", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `name` TEXT NOT NULL, `phone` TEXT NOT NULL, `address` TEXT, `email` TEXT, `totalSales` INTEGER NOT NULL, `totalCommission` INTEGER NOT NULL, `totalDebt` INTEGER NOT NULL, `commissionRate` INTEGER NOT NULL, `isActive` INTEGER NOT NULL, `bankAccount` TEXT, `bankName` TEXT, `bankSheba` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "phone", "columnName": "phone", "affinity": "TEXT", "notNull": true}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": false}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalSales", "columnName": "totalSales", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalCommission", "columnName": "totalCommission", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalDebt", "columnName": "totalDebt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "commissionRate", "columnName": "commissionRate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bankAccount", "columnName": "bankAccount", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bankName", "columnName": "bankName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bankSheba", "columnName": "bankSheba", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "products", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `code` TEXT NOT NULL, `barcode` TEXT, `description` TEXT, `categoryId` TEXT NOT NULL, `brandId` TEXT, `type` TEXT NOT NULL, `season` TEXT NOT NULL, `gender` TEXT NOT NULL, `sizes` TEXT NOT NULL, `colors` TEXT NOT NULL, `materials` TEXT NOT NULL, `purchasePrice` INTEGER NOT NULL, `sellingPrice` INTEGER NOT NULL, `stock` INTEGER NOT NULL, `minimumStock` INTEGER NOT NULL, `imageUrls` TEXT NOT NULL, `tags` TEXT NOT NULL, `isActive` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`categoryId`) REFERENCES `categories`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "code", "columnName": "code", "affinity": "TEXT", "notNull": true}, {"fieldPath": "barcode", "columnName": "barcode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "categoryId", "columnName": "categoryId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "brandId", "columnName": "brandId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "season", "columnName": "season", "affinity": "TEXT", "notNull": true}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sizes", "columnName": "sizes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "colors", "columnName": "colors", "affinity": "TEXT", "notNull": true}, {"fieldPath": "materials", "columnName": "materials", "affinity": "TEXT", "notNull": true}, {"fieldPath": "purchasePrice", "columnName": "purchasePrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sellingPrice", "columnName": "sellingPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "stock", "columnName": "stock", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "minimumStock", "columnName": "minimumStock", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "imageUrls", "columnName": "imageUrls", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tags", "columnName": "tags", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_products_categoryId", "unique": false, "columnNames": ["categoryId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_products_categoryId` ON `${TABLE_NAME}` (`categoryId`)"}], "foreignKeys": [{"table": "categories", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["categoryId"], "referencedColumns": ["id"]}]}, {"tableName": "categories", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT, `parentId` TEXT, `imageUrl` TEXT, `sortOrder` INTEGER NOT NULL, `isActive` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`parentId`) REFERENCES `categories`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "parentId", "columnName": "parentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sortOrder", "columnName": "sortOrder", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_categories_parentId", "unique": false, "columnNames": ["parentId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_categories_parentId` ON `${TABLE_NAME}` (`parentId`)"}], "foreignKeys": [{"table": "categories", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["parentId"], "referencedColumns": ["id"]}]}, {"tableName": "invoices", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `invoiceNumber` TEXT NOT NULL, `customerId` TEXT NOT NULL, `customerName` TEXT NOT NULL, `sellerId` TEXT, `sellerName` TEXT, `totalAmount` INTEGER NOT NULL, `discount` INTEGER NOT NULL, `tax` INTEGER NOT NULL, `finalAmount` INTEGER NOT NULL, `paidAmount` INTEGER NOT NULL, `remainingAmount` INTEGER NOT NULL, `status` TEXT NOT NULL, `paymentType` TEXT NOT NULL, `dueDate` INTEGER, `notes` TEXT, `isDeleted` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `approvedAt` INTEGER, `approvedBy` TEXT, PRIMARY KEY(`id`), FOREIGN KEY(`customerId`) REFERENCES `customers`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`sellerId`) REFERENCES `sellers`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`approvedBy`) REFERENCES `users`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "invoiceNumber", "columnName": "invoiceNumber", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerId", "columnName": "customerId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerName", "columnName": "customerName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sellerId", "columnName": "sellerId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sellerName", "columnName": "sellerName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalAmount", "columnName": "totalAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "discount", "columnName": "discount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tax", "columnName": "tax", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finalAmount", "columnName": "finalAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paidAmount", "columnName": "paidAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "remainingAmount", "columnName": "remainingAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "paymentType", "columnName": "paymentType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "dueDate", "columnName": "dueDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isDeleted", "columnName": "isDeleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "approvedAt", "columnName": "approvedAt", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "approvedBy", "columnName": "approvedBy", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_invoices_customerId", "unique": false, "columnNames": ["customerId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_invoices_customerId` ON `${TABLE_NAME}` (`customerId`)"}, {"name": "index_invoices_sellerId", "unique": false, "columnNames": ["sellerId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_invoices_sellerId` ON `${TABLE_NAME}` (`sellerId`)"}, {"name": "index_invoices_approvedBy", "unique": false, "columnNames": ["approvedBy"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_invoices_approvedBy` ON `${TABLE_NAME}` (`approvedBy`)"}], "foreignKeys": [{"table": "customers", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["customerId"], "referencedColumns": ["id"]}, {"table": "sellers", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sellerId"], "referencedColumns": ["id"]}, {"table": "users", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["approvedBy"], "referencedColumns": ["id"]}]}, {"tableName": "invoice_items", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `invoiceId` TEXT NOT NULL, `productId` TEXT NOT NULL, `productName` TEXT NOT NULL, `productCode` TEXT, `quantity` INTEGER NOT NULL, `unitPrice` INTEGER NOT NULL, `discount` INTEGER NOT NULL, `tax` INTEGER NOT NULL, `notes` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`invoiceId`) REFERENCES `invoices`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`productId`) REFERENCES `products`(`id`) ON UPDATE NO ACTION ON DELETE RESTRICT )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "invoiceId", "columnName": "invoiceId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productId", "columnName": "productId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productName", "columnName": "productName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productCode", "columnName": "productCode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "quantity", "columnName": "quantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "discount", "columnName": "discount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tax", "columnName": "tax", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_invoice_items_invoiceId", "unique": false, "columnNames": ["invoiceId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_invoice_items_invoiceId` ON `${TABLE_NAME}` (`invoiceId`)"}, {"name": "index_invoice_items_productId", "unique": false, "columnNames": ["productId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_invoice_items_productId` ON `${TABLE_NAME}` (`productId`)"}], "foreignKeys": [{"table": "invoices", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["invoiceId"], "referencedColumns": ["id"]}, {"table": "products", "onDelete": "RESTRICT", "onUpdate": "NO ACTION", "columns": ["productId"], "referencedColumns": ["id"]}]}, {"tableName": "payments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `customerId` TEXT NOT NULL, `orderId` TEXT, `amount` REAL NOT NULL, `date` INTEGER NOT NULL, `status` TEXT NOT NULL, `method` TEXT NOT NULL, `referenceNumber` TEXT, `notes` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, `createdBy` TEXT, `updatedBy` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerId", "columnName": "customerId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "orderId", "columnName": "orderId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "amount", "columnName": "amount", "affinity": "REAL", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "method", "columnName": "method", "affinity": "TEXT", "notNull": true}, {"fieldPath": "referenceNumber", "columnName": "referenceNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "created<PERSON>y", "columnName": "created<PERSON>y", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedBy", "columnName": "updatedBy", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "installments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `customerId` INTEGER NOT NULL, `totalAmount` REAL NOT NULL, `paidAmount` REAL NOT NULL, `dueDate` INTEGER NOT NULL, `notes` TEXT, `status` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "customerId", "columnName": "customerId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalAmount", "columnName": "totalAmount", "affinity": "REAL", "notNull": true}, {"fieldPath": "paidAmount", "columnName": "paidAmount", "affinity": "REAL", "notNull": true}, {"fieldPath": "dueDate", "columnName": "dueDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "transactions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `customerId` TEXT NOT NULL, `orderId` TEXT, `paymentId` TEXT, `amount` INTEGER NOT NULL, `type` TEXT NOT NULL, `description` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`customerId`) REFERENCES `customers`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`orderId`) REFERENCES `orders`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL , FOREIGN KEY(`paymentId`) REFERENCES `payments`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerId", "columnName": "customerId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "orderId", "columnName": "orderId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "paymentId", "columnName": "paymentId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "amount", "columnName": "amount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_transactions_customerId", "unique": false, "columnNames": ["customerId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_transactions_customerId` ON `${TABLE_NAME}` (`customerId`)"}, {"name": "index_transactions_orderId", "unique": false, "columnNames": ["orderId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_transactions_orderId` ON `${TABLE_NAME}` (`orderId`)"}, {"name": "index_transactions_paymentId", "unique": false, "columnNames": ["paymentId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_transactions_paymentId` ON `${TABLE_NAME}` (`paymentId`)"}], "foreignKeys": [{"table": "customers", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["customerId"], "referencedColumns": ["id"]}, {"table": "orders", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["orderId"], "referencedColumns": ["id"]}, {"table": "payments", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["paymentId"], "referencedColumns": ["id"]}]}, {"tableName": "orders", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `customerId` TEXT NOT NULL, `sellerId` TEXT, `totalAmount` INTEGER NOT NULL, `discountAmount` INTEGER NOT NULL, `finalAmount` INTEGER NOT NULL, `status` TEXT NOT NULL, `notes` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`customerId`) REFERENCES `customers`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`sellerId`) REFERENCES `sellers`(`id`) ON UPDATE NO ACTION ON DELETE SET NULL )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "customerId", "columnName": "customerId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sellerId", "columnName": "sellerId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "totalAmount", "columnName": "totalAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "discountAmount", "columnName": "discountAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "finalAmount", "columnName": "finalAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_orders_customerId", "unique": false, "columnNames": ["customerId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_orders_customerId` ON `${TABLE_NAME}` (`customerId`)"}, {"name": "index_orders_sellerId", "unique": false, "columnNames": ["sellerId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_orders_sellerId` ON `${TABLE_NAME}` (`sellerId`)"}], "foreignKeys": [{"table": "customers", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["customerId"], "referencedColumns": ["id"]}, {"table": "sellers", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["sellerId"], "referencedColumns": ["id"]}]}, {"tableName": "order_items", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `orderId` TEXT NOT NULL, `productId` TEXT NOT NULL, `quantity` INTEGER NOT NULL, `unitPrice` INTEGER NOT NULL, `discountAmount` INTEGER NOT NULL, `totalAmount` INTEGER NOT NULL, `notes` TEXT, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`orderId`) REFERENCES `orders`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`productId`) REFERENCES `products`(`id`) ON UPDATE NO ACTION ON DELETE RESTRICT )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "orderId", "columnName": "orderId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "productId", "columnName": "productId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "quantity", "columnName": "quantity", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "unitPrice", "columnName": "unitPrice", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "discountAmount", "columnName": "discountAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalAmount", "columnName": "totalAmount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_order_items_orderId", "unique": false, "columnNames": ["orderId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_order_items_orderId` ON `${TABLE_NAME}` (`orderId`)"}, {"name": "index_order_items_productId", "unique": false, "columnNames": ["productId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_order_items_productId` ON `${TABLE_NAME}` (`productId`)"}], "foreignKeys": [{"table": "orders", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["orderId"], "referencedColumns": ["id"]}, {"table": "products", "onDelete": "RESTRICT", "onUpdate": "NO ACTION", "columns": ["productId"], "referencedColumns": ["id"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '8c0824ee4f3db7afc304e633fea37f7c')"]}}