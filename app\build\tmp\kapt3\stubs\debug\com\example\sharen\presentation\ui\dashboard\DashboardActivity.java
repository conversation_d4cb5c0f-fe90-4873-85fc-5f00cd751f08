package com.example.sharen.presentation.ui.dashboard;

/**
 * صفحه داشبورد اصلی
 */
@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\n\u001a\u00020\u0002H\u0016J\b\u0010\u000b\u001a\u00020\fH\u0002J\b\u0010\r\u001a\u00020\fH\u0002J\b\u0010\u000e\u001a\u00020\fH\u0002J\b\u0010\u000f\u001a\u00020\fH\u0002J\b\u0010\u0010\u001a\u00020\fH\u0002J\b\u0010\u0011\u001a\u00020\fH\u0002J\b\u0010\u0012\u001a\u00020\fH\u0002J\b\u0010\u0013\u001a\u00020\fH\u0002J\b\u0010\u0014\u001a\u00020\fH\u0002J\b\u0010\u0015\u001a\u00020\fH\u0016J\b\u0010\u0016\u001a\u00020\fH\u0002J\b\u0010\u0017\u001a\u00020\fH\u0002J\b\u0010\u0018\u001a\u00020\fH\u0016J\u0010\u0010\u0019\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\u001bH\u0002R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u001c"}, d2 = {"Lcom/example/sharen/presentation/ui/dashboard/DashboardActivity;", "Lcom/example/sharen/core/base/BaseActivity;", "Lcom/example/sharen/databinding/ActivityDashboardBinding;", "()V", "dashboardViewModel", "Lcom/example/sharen/presentation/viewmodel/DashboardViewModel;", "getDashboardViewModel", "()Lcom/example/sharen/presentation/viewmodel/DashboardViewModel;", "dashboardViewModel$delegate", "Lkotlin/Lazy;", "getViewBinding", "navigateToCustomers", "", "navigateToInvoices", "navigateToNewCustomer", "navigateToNewInvoice", "navigateToNewProduct", "navigateToPayments", "navigateToProducts", "navigateToReports", "observeDashboardData", "observeData", "setupClickListeners", "setupUI", "setupViews", "updateStatsCards", "stats", "Lcom/example/sharen/presentation/ui/dashboard/DashboardStats;", "app_debug"})
public final class DashboardActivity extends com.example.sharen.core.base.BaseActivity<com.example.sharen.databinding.ActivityDashboardBinding> {
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy dashboardViewModel$delegate = null;
    
    public DashboardActivity() {
        super();
    }
    
    private final com.example.sharen.presentation.viewmodel.DashboardViewModel getDashboardViewModel() {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public com.example.sharen.databinding.ActivityDashboardBinding getViewBinding() {
        return null;
    }
    
    @java.lang.Override
    public void setupViews() {
    }
    
    @java.lang.Override
    public void observeData() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void setupUI() {
    }
    
    private final void observeDashboardData() {
    }
    
    private final void updateStatsCards(com.example.sharen.presentation.ui.dashboard.DashboardStats stats) {
    }
    
    private final void navigateToCustomers() {
    }
    
    private final void navigateToProducts() {
    }
    
    private final void navigateToInvoices() {
    }
    
    private final void navigateToPayments() {
    }
    
    private final void navigateToNewInvoice() {
    }
    
    private final void navigateToNewCustomer() {
    }
    
    private final void navigateToNewProduct() {
    }
    
    private final void navigateToReports() {
    }
}