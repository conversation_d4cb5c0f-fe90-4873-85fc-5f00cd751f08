// Generated by view binder compiler. Do not edit!
package com.example.sharen.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.sharen.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProductListBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final ImageButton btnFilter;

  @NonNull
  public final ChipGroup chipGroupFilters;

  @NonNull
  public final Chip chipLowStock;

  @NonNull
  public final FloatingActionButton fabAddProduct;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvProducts;

  @NonNull
  public final SearchView searchView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvNoProducts;

  @NonNull
  public final TextView tvProductCount;

  private ActivityProductListBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull ImageButton btnFilter,
      @NonNull ChipGroup chipGroupFilters, @NonNull Chip chipLowStock,
      @NonNull FloatingActionButton fabAddProduct, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView rvProducts, @NonNull SearchView searchView, @NonNull Toolbar toolbar,
      @NonNull TextView tvNoProducts, @NonNull TextView tvProductCount) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.btnFilter = btnFilter;
    this.chipGroupFilters = chipGroupFilters;
    this.chipLowStock = chipLowStock;
    this.fabAddProduct = fabAddProduct;
    this.progressBar = progressBar;
    this.rvProducts = rvProducts;
    this.searchView = searchView;
    this.toolbar = toolbar;
    this.tvNoProducts = tvNoProducts;
    this.tvProductCount = tvProductCount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProductListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProductListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_product_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProductListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btnFilter;
      ImageButton btnFilter = ViewBindings.findChildViewById(rootView, id);
      if (btnFilter == null) {
        break missingId;
      }

      id = R.id.chipGroupFilters;
      ChipGroup chipGroupFilters = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupFilters == null) {
        break missingId;
      }

      id = R.id.chipLowStock;
      Chip chipLowStock = ViewBindings.findChildViewById(rootView, id);
      if (chipLowStock == null) {
        break missingId;
      }

      id = R.id.fabAddProduct;
      FloatingActionButton fabAddProduct = ViewBindings.findChildViewById(rootView, id);
      if (fabAddProduct == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rvProducts;
      RecyclerView rvProducts = ViewBindings.findChildViewById(rootView, id);
      if (rvProducts == null) {
        break missingId;
      }

      id = R.id.searchView;
      SearchView searchView = ViewBindings.findChildViewById(rootView, id);
      if (searchView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvNoProducts;
      TextView tvNoProducts = ViewBindings.findChildViewById(rootView, id);
      if (tvNoProducts == null) {
        break missingId;
      }

      id = R.id.tvProductCount;
      TextView tvProductCount = ViewBindings.findChildViewById(rootView, id);
      if (tvProductCount == null) {
        break missingId;
      }

      return new ActivityProductListBinding((CoordinatorLayout) rootView, appBarLayout, btnFilter,
          chipGroupFilters, chipLowStock, fabAddProduct, progressBar, rvProducts, searchView,
          toolbar, tvNoProducts, tvProductCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
