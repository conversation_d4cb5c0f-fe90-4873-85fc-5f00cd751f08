<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_customer_details" modulePackage="com.example.sharen" filePath="app\src\main\res\layout\activity_customer_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_customer_details_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="282" endOffset="53"/></Target><Target id="@+id/app_bar" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="39" endOffset="53"/></Target><Target id="@+id/collapsing_toolbar" view="com.google.android.material.appbar.CollapsingToolbarLayout"><Expressions/><location startLine="14" startOffset="8" endLine="38" endOffset="68"/></Target><Target id="@+id/iv_customer_header" view="ImageView"><Expressions/><location startLine="22" startOffset="12" endLine="29" endOffset="52"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="31" startOffset="12" endLine="36" endOffset="70"/></Target><Target id="@+id/card_customer_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="51" startOffset="12" endLine="131" endOffset="47"/></Target><Target id="@+id/tv_customer_name" view="TextView"><Expressions/><location startLine="74" startOffset="20" endLine="82" endOffset="48"/></Target><Target id="@+id/tv_customer_phone" view="TextView"><Expressions/><location startLine="98" startOffset="20" endLine="105" endOffset="50"/></Target><Target id="@+id/tv_customer_address" view="TextView"><Expressions/><location startLine="121" startOffset="20" endLine="128" endOffset="68"/></Target><Target id="@+id/card_financial_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="133" startOffset="12" endLine="237" endOffset="47"/></Target><Target id="@+id/tv_debt_amount" view="TextView"><Expressions/><location startLine="157" startOffset="20" endLine="165" endOffset="54"/></Target><Target id="@+id/tv_total_purchases" view="TextView"><Expressions/><location startLine="181" startOffset="20" endLine="188" endOffset="55"/></Target><Target id="@+id/tv_last_purchase" view="TextView"><Expressions/><location startLine="204" startOffset="20" endLine="211" endOffset="49"/></Target><Target id="@+id/tv_customer_since" view="TextView"><Expressions/><location startLine="227" startOffset="20" endLine="234" endOffset="49"/></Target><Target id="@+id/tv_recent_invoices_title" view="TextView"><Expressions/><location startLine="239" startOffset="12" endLine="248" endOffset="80"/></Target><Target id="@+id/rv_recent_invoices" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="250" startOffset="12" endLine="258" endOffset="59"/></Target><Target id="@+id/fab_edit" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="263" startOffset="4" endLine="271" endOffset="47"/></Target><Target id="@+id/fab_new_invoice" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="273" startOffset="4" endLine="280" endOffset="54"/></Target></Targets></Layout>